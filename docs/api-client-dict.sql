-- API客户端状态字典配置
INSERT INTO sys_dict (id, dict_name, dict_code, description, del_flag, create_by, create_time, update_by, update_time, type) 
VALUES ('api_client_status_dict', 'API客户端状态', 'api_client_status', 'API客户端状态字典', 0, 'admin', NOW(), NULL, NULL, 0);

-- API客户端状态字典项
INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, description, sort_order, status, create_by, create_time, update_by, update_time) 
VALUES 
('api_client_status_0', 'api_client_status_dict', '禁用', '0', '禁用状态', 1, 1, 'admin', NOW(), NULL, NULL),
('api_client_status_1', 'api_client_status_dict', '启用', '1', '启用状态', 2, 1, 'admin', NOW(), NULL, NULL);

-- 菜单权限配置
INSERT INTO sys_permission (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
VALUES 
('comInterface_menu', NULL, 'API接口管理', '/comInterface', 'layouts/RouteView', NULL, NULL, 0, NULL, '1', 1.00, 0, 'api', 1, 0, 0, 0, 0, 'API接口管理菜单', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0),
('apiClient_menu', 'comInterface_menu', 'API客户端配置', '/comInterface/apiClient', 'comInterface/ApiClientList', NULL, NULL, 1, NULL, '1', 1.00, 0, 'setting', 1, 1, 0, 0, 0, 'API客户端配置管理', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0);

-- API客户端权限按钮
INSERT INTO sys_permission (id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
VALUES 
('apiClient_add', 'apiClient_menu', '添加', NULL, NULL, NULL, NULL, 2, 'comInterface:apiClient:add', '1', 1.00, 0, NULL, 0, 1, 0, 0, 0, 'API客户端添加', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0),
('apiClient_edit', 'apiClient_menu', '编辑', NULL, NULL, NULL, NULL, 2, 'comInterface:apiClient:edit', '1', 2.00, 0, NULL, 0, 1, 0, 0, 0, 'API客户端编辑', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0),
('apiClient_delete', 'apiClient_menu', '删除', NULL, NULL, NULL, NULL, 2, 'comInterface:apiClient:delete', '1', 3.00, 0, NULL, 0, 1, 0, 0, 0, 'API客户端删除', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0),
('apiClient_query', 'apiClient_menu', '查询', NULL, NULL, NULL, NULL, 2, 'comInterface:apiClient:query', '1', 4.00, 0, NULL, 0, 1, 0, 0, 0, 'API客户端查询', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0),
('apiClient_export', 'apiClient_menu', '导出', NULL, NULL, NULL, NULL, 2, 'comInterface:apiClient:export', '1', 5.00, 0, NULL, 0, 1, 0, 0, 0, 'API客户端导出', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0),
('apiClient_import', 'apiClient_menu', '导入', NULL, NULL, NULL, NULL, 2, 'comInterface:apiClient:import', '1', 6.00, 0, NULL, 0, 1, 0, 0, 0, 'API客户端导入', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0),
('apiClient_toggle', 'apiClient_menu', '启用禁用', NULL, NULL, NULL, NULL, 2, 'comInterface:apiClient:toggle', '1', 7.00, 0, NULL, 0, 1, 0, 0, 0, 'API客户端启用禁用', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0),
('apiClient_regenerate', 'apiClient_menu', '重新生成密钥', NULL, NULL, NULL, NULL, 2, 'comInterface:apiClient:regenerate', '1', 8.00, 0, NULL, 0, 1, 0, 0, 0, 'API客户端重新生成密钥', '1', 0, 0, 'admin', NOW(), NULL, NULL, 0);
