# 团检报告API端点使用示例

## 新增的两个端点

### 1. 常规团检报告渲染数据接口

**请求方式**: GET  
**请求路径**: `/api/v1/company/registration/{companyRegId}/team-report`  
**参数说明**:
- `companyRegId` (必需): 企业预约ID
- `companyDeptId` (可选): 企业部门ID

**请求示例**:
```http
GET /api/v1/company/registration/1234567890/team-report?companyDeptId=dept001
```

**响应数据结构**:
```json
{
  "success": true,
  "message": "Team report render data retrieved successfully",
  "result": {
    "template": {
      "DataSources": [...],
      "Pages": [...],
      // ActiveReportJS模板结构
    },
    "data": {
      "company": {...},
      "companyReg": {...},
      "companyTeams": [...],
      "companyTeamPersonStat": {...},
      // 团检报告完整数据
    },
    "reportType": "team",
    "companyRegId": "1234567890",
    "companyDeptId": "dept001"
  }
}
```

### 2. 职业病团检报告渲染数据接口

**请求方式**: GET  
**请求路径**: `/api/v1/company/registration/{companyRegId}/zy-team-report`  
**参数说明**:
- `companyRegId` (必需): 企业预约ID
- `companyDeptId` (可选): 企业部门ID

**请求示例**:
```http
GET /api/v1/company/registration/1234567890/zy-team-report?companyDeptId=dept001
```

**响应数据结构**:
```json
{
  "success": true,
  "message": "Occupational team report render data retrieved successfully",
  "result": {
    "template": {
      "DataSources": [...],
      "Pages": [...],
      // ActiveReportJS模板结构
    },
    "data": {
      "orgInfo": {...},
      "company": {...},
      "companyReg": {...},
      "zyCompanyOverview": {...},
      // 职业病团检报告完整数据
    },
    "reportType": "zyTeam",
    "companyRegId": "1234567890",
    "companyDeptId": "dept001"
  }
}
```

## 前端使用示例 (参考 CustomerRegReportPannel.vue)

```javascript
// 获取团检报告数据并渲染
async function loadTeamReport(companyRegId, companyDeptId) {
  try {
    const response = await this.$http.get(
      `/api/v1/company/registration/${companyRegId}/team-report`,
      { params: { companyDeptId } }
    );
    
    if (response.success) {
      const { template, data } = response.result;
      
      // 直接使用返回的模板和数据，无需额外处理
      // 因为后端已经处理了图片URL和数据源绑定
      
      viewer.resetDocument();
      viewer.availableExports = ['pdf'];
      viewer.open(template);
    }
  } catch (error) {
    console.error('加载团检报告失败:', error);
  }
}

// 获取职业病团检报告数据并渲染
async function loadZyTeamReport(companyRegId, companyDeptId) {
  try {
    const response = await this.$http.get(
      `/api/v1/company/registration/${companyRegId}/zy-team-report`,
      { params: { companyDeptId } }
    );
    
    if (response.success) {
      const { template, data } = response.result;
      
      viewer.resetDocument();
      viewer.availableExports = ['pdf'];
      viewer.open(template);
    }
  } catch (error) {
    console.error('加载职业病团检报告失败:', error);
  }
}
```

## 主要特性

1. **一体化数据**: 将报告数据和模板整合在一个接口中返回
2. **图片URL处理**: 自动将相对路径转换为完整URL
3. **模板匹配**: 根据体检分类自动选择合适的报告模板
4. **数据源绑定**: 自动更新模板中的数据源连接字符串
5. **ActiveReportJS兼容**: 返回格式可直接用于ActiveReportJS渲染

## 错误处理

- 404: 企业预约不存在或报告数据为空
- 404: 找不到匹配的报告模板
- 500: 服务器内部错误


