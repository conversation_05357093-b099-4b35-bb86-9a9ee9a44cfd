-- 修复部位字典helpChar数据脚本
-- 此脚本用于修复check_part_dict表中缺失的helpChar数据

-- 1. 检查当前helpChar数据状态
SELECT 
    '=== helpChar数据检查 ===' as step,
    COUNT(*) as total_records,
    COUNT(help_char) as has_helpchar,
    COUNT(*) - COUNT(help_char) as missing_helpchar
FROM check_part_dict 
WHERE enable_flag = '1';

-- 2. 显示缺失helpChar的记录
SELECT 
    '=== 缺失helpChar的记录 ===' as step,
    id, code, name, help_char
FROM check_part_dict 
WHERE enable_flag = '1' 
  AND (help_char IS NULL OR help_char = '')
ORDER BY name;

-- 3. 更新常见部位的helpChar数据
UPDATE check_part_dict SET help_char = 'TB' WHERE name LIKE '%头部%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'JB' WHERE name LIKE '%颈部%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'XB' WHERE name LIKE '%胸部%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'FB' WHERE name LIKE '%腹部%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'SFB' WHERE name LIKE '%上腹%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'XFB' WHERE name LIKE '%下腹%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'PQ' WHERE name LIKE '%盆腔%' AND (help_char IS NULL OR help_char = '');

-- 脊柱相关
UPDATE check_part_dict SET help_char = 'JZ' WHERE name LIKE '%颈椎%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'XZ' WHERE name LIKE '%胸椎%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YZ' WHERE name LIKE '%腰椎%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'WZ' WHERE name LIKE '%尾椎%' AND (help_char IS NULL OR help_char = '');

-- 关节相关
UPDATE check_part_dict SET help_char = 'ZXGJ' WHERE name LIKE '%左膝%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YXGJ' WHERE name LIKE '%右膝%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'ZJGJ' WHERE name LIKE '%左肩%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YJGJ' WHERE name LIKE '%右肩%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'ZHGJ' WHERE name LIKE '%左踝%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YHGJ' WHERE name LIKE '%右踝%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'ZWGJ' WHERE name LIKE '%左腕%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YWGJ' WHERE name LIKE '%右腕%' AND (help_char IS NULL OR help_char = '');

-- 四肢相关
UPDATE check_part_dict SET help_char = 'ZS' WHERE name LIKE '%左手%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YS' WHERE name LIKE '%右手%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'ZZ' WHERE name LIKE '%左足%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YZ' WHERE name LIKE '%右足%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'ZSB' WHERE name LIKE '%左上臂%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YSB' WHERE name LIKE '%右上臂%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'ZQB' WHERE name LIKE '%左前臂%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'YQB' WHERE name LIKE '%右前臂%' AND (help_char IS NULL OR help_char = '');

-- 其他常见部位
UPDATE check_part_dict SET help_char = 'XZ' WHERE name LIKE '%心脏%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'GZ' WHERE name LIKE '%肝脏%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'SZ' WHERE name LIKE '%肾脏%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'PZ' WHERE name LIKE '%脾脏%' AND (help_char IS NULL OR help_char = '');
UPDATE check_part_dict SET help_char = 'FEI' WHERE name LIKE '%肺%' AND (help_char IS NULL OR help_char = '');

-- 4. 如果没有基础数据，插入一些常用部位
INSERT IGNORE INTO check_part_dict (id, code, name, help_char, category, sort_order, enable_flag) VALUES
('part_head', 'HEAD', '头部', 'TB', '头颈部', 1, '1'),
('part_neck', 'NECK', '颈部', 'JB', '头颈部', 2, '1'),
('part_chest', 'CHEST', '胸部', 'XB', '躯干', 3, '1'),
('part_abdomen', 'ABDOMEN', '腹部', 'FB', '躯干', 4, '1'),
('part_left_knee', 'LEFT_KNEE', '左膝关节', 'ZXGJ', '关节', 5, '1'),
('part_right_knee', 'RIGHT_KNEE', '右膝关节', 'YXGJ', '关节', 6, '1'),
('part_cervical', 'CERVICAL', '颈椎', 'JZ', '脊柱', 7, '1'),
('part_lumbar', 'LUMBAR', '腰椎', 'YZ', '脊柱', 8, '1'),
('part_left_shoulder', 'LEFT_SHOULDER', '左肩关节', 'ZJGJ', '关节', 9, '1'),
('part_right_shoulder', 'RIGHT_SHOULDER', '右肩关节', 'YJGJ', '关节', 10, '1');

-- 5. 验证修复结果
SELECT 
    '=== 修复后数据检查 ===' as step,
    COUNT(*) as total_records,
    COUNT(help_char) as has_helpchar,
    COUNT(*) - COUNT(help_char) as missing_helpchar
FROM check_part_dict 
WHERE enable_flag = '1';

-- 6. 显示修复后的数据示例
SELECT 
    '=== 修复后数据示例 ===' as step,
    id, code, name, help_char, category
FROM check_part_dict 
WHERE enable_flag = '1' 
  AND help_char IS NOT NULL 
  AND help_char != ''
ORDER BY sort_order, name
LIMIT 20;

-- 7. 显示仍然缺失helpChar的记录（如果有）
SELECT 
    '=== 仍然缺失helpChar的记录 ===' as step,
    id, code, name, help_char
FROM check_part_dict 
WHERE enable_flag = '1' 
  AND (help_char IS NULL OR help_char = '')
ORDER BY name;

-- 8. 完成提示
SELECT 
    '=== 修复完成 ===' as step,
    CASE 
        WHEN (SELECT COUNT(*) FROM check_part_dict WHERE enable_flag = '1' AND (help_char IS NULL OR help_char = '')) = 0
        THEN '✅ 所有部位都已有helpChar数据，修复成功！'
        ELSE '⚠️ 仍有部分部位缺少helpChar数据，请手动补充'
    END as result;
