package org.jeecg;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * 应用程序启动器
 * 提供编程方式启动应用的方法
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
@Slf4j
public class ApplicationStarter {
    
    private static ConfigurableApplicationContext applicationContext;
    
    /**
     * 启动应用程序
     */
    public static ConfigurableApplicationContext startApplication() {
        return startApplication(new String[]{});
    }
    
    /**
     * 启动应用程序（带参数）
     */
    public static ConfigurableApplicationContext startApplication(String[] args) {
        try {
            log.info("Starting JeecG Boot Application programmatically...");
            
            // 设置系统属性
            System.setProperty("spring.profiles.active", "dev");
            
            // 启动Spring Boot应用
            applicationContext = SpringApplication.run(JeecgSystemApplication.class, args);
            
            log.info("JeecG Boot Application started successfully!");
            return applicationContext;
            
        } catch (Exception e) {
            log.error("Failed to start JeecG Boot Application", e);
            throw new RuntimeException("Application startup failed", e);
        }
    }
    
    /**
     * 停止应用程序
     */
    public static void stopApplication() {
        if (applicationContext != null && applicationContext.isRunning()) {
            log.info("Stopping JeecG Boot Application...");
            applicationContext.close();
            log.info("JeecG Boot Application stopped successfully!");
        }
    }
    
    /**
     * 重启应用程序
     */
    public static ConfigurableApplicationContext restartApplication() {
        stopApplication();
        return startApplication();
    }
    
    /**
     * 检查应用程序是否正在运行
     */
    public static boolean isRunning() {
        return applicationContext != null && applicationContext.isRunning();
    }
    
    /**
     * 获取应用程序上下文
     */
    public static ConfigurableApplicationContext getApplicationContext() {
        return applicationContext;
    }
    
    /**
     * 示例：如何在其他代码中使用
     */
    public static void main(String[] args) {
        // 启动应用
        ConfigurableApplicationContext context = startApplication(args);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Shutdown hook triggered, stopping application...");
            stopApplication();
        }));
        
        // 应用已启动，可以进行其他操作
        log.info("Application is running. Press Ctrl+C to stop.");
        
        // 保持应用运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("Application interrupted, shutting down...");
            stopApplication();
        }
    }
}
