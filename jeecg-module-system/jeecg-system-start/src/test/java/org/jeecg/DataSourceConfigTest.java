package org.jeecg;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 数据源配置测试
 * 用于验证Bean冲突是否已解决以及数据源配置是否正确
 */
@SpringBootTest
@ActiveProfiles("dev")
public class DataSourceConfigTest {

    @Autowired
    private DataSource dataSource;

    @Test
    public void contextLoads() {
        // 如果应用上下文能够成功加载，说明Bean冲突已解决
        System.out.println("Spring Boot应用上下文加载成功，Bean冲突已解决！");
    }

    @Test
    public void testDataSourceConnection() throws Exception {
        // 测试数据源连接
        try (Connection connection = dataSource.getConnection()) {
            System.out.println("数据源连接成功！");
            System.out.println("数据库URL: " + connection.getMetaData().getURL());
        }
    }
}
