package org.jeecg;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 启动验证测试
 * 用于验证应用能否正常启动，包括数据源配置
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("dev")
public class StartupValidationTest {

    @Test
    public void applicationContextLoads() {
        // 如果这个测试通过，说明应用上下文可以正常加载
        // 包括数据源配置、Bean冲突等问题都已解决
        System.out.println("✅ 应用启动验证成功！");
        System.out.println("✅ Bean冲突已解决");
        System.out.println("✅ 数据源配置正确");
    }
}
