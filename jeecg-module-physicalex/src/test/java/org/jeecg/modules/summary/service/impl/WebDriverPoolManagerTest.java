package org.jeecg.modules.summary.service.impl;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.openqa.selenium.WebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * WebDriverPoolManager测试类
 * 用于验证Chrome启动问题的修复
 */
public class WebDriverPoolManagerTest {
    private static final Logger log = LoggerFactory.getLogger(WebDriverPoolManagerTest.class);
    
    private WebDriverPoolManager poolManager;
    
    @BeforeEach
    public void setUp() {
        poolManager = new WebDriverPoolManager();
        
        // 设置测试用的Chrome路径（需要根据实际环境调整）
        ReflectionTestUtils.setField(poolManager, "chromeDriverPath", "E:\\project\\BJ\\chromedriver.exe");
        ReflectionTestUtils.setField(poolManager, "chromePath", "E:\\project\\BJ\\chrome-win64\\chrome-win64\\chrome.exe");
        ReflectionTestUtils.setField(poolManager, "poolSize", 1);
        
        // 初始化池管理器
        try {
            poolManager.init();
            log.info("WebDriverPoolManager初始化成功");
        } catch (Exception e) {
            log.error("WebDriverPoolManager初始化失败", e);
        }
    }
    
    @AfterEach
    public void tearDown() {
        if (poolManager != null) {
            try {
                poolManager.shutdown();
                log.info("WebDriverPoolManager已关闭");
            } catch (Exception e) {
                log.error("关闭WebDriverPoolManager时发生异常", e);
            }
        }
    }
    
    @Test
    public void testWebDriverCreation() {
        log.info("开始测试WebDriver创建");
        
        WebDriver driver = null;
        try {
            // 测试借用WebDriver
            driver = poolManager.borrowWebDriver("about:blank");
            log.info("成功借用WebDriver实例");
            
            // 简单验证
            String title = driver.getTitle();
            log.info("页面标题: {}", title);
            
            // 测试归还WebDriver
            poolManager.returnWebDriver(driver);
            driver = null; // 避免在finally中重复处理
            log.info("成功归还WebDriver实例");
            
            log.info("WebDriver创建测试通过");
            
        } catch (Exception e) {
            log.error("WebDriver创建测试失败", e);
            throw new RuntimeException("WebDriver创建测试失败", e);
        } finally {
            if (driver != null) {
                try {
                    poolManager.returnWebDriver(driver);
                } catch (Exception e) {
                    log.error("归还WebDriver时发生异常", e);
                }
            }
        }
    }
    
    @Test
    public void testMultipleWebDriverCreation() {
        log.info("开始测试多个WebDriver创建");
        
        WebDriver driver1 = null;
        WebDriver driver2 = null;
        
        try {
            // 测试创建多个WebDriver实例
            driver1 = poolManager.borrowWebDriver("about:blank");
            log.info("成功创建第一个WebDriver实例");
            
            driver2 = poolManager.borrowWebDriver("about:blank");
            log.info("成功创建第二个WebDriver实例");
            
            // 验证两个实例都正常工作
            String title1 = driver1.getTitle();
            String title2 = driver2.getTitle();
            
            log.info("第一个实例页面标题: {}", title1);
            log.info("第二个实例页面标题: {}", title2);
            
            // 归还实例
            poolManager.returnWebDriver(driver1);
            driver1 = null;
            log.info("成功归还第一个WebDriver实例");
            
            poolManager.returnWebDriver(driver2);
            driver2 = null;
            log.info("成功归还第二个WebDriver实例");
            
            log.info("多个WebDriver创建测试通过");
            
        } catch (Exception e) {
            log.error("多个WebDriver创建测试失败", e);
            throw new RuntimeException("多个WebDriver创建测试失败", e);
        } finally {
            if (driver1 != null) {
                try {
                    poolManager.returnWebDriver(driver1);
                } catch (Exception e) {
                    log.error("归还第一个WebDriver时发生异常", e);
                }
            }
            if (driver2 != null) {
                try {
                    poolManager.returnWebDriver(driver2);
                } catch (Exception e) {
                    log.error("归还第二个WebDriver时发生异常", e);
                }
            }
        }
    }
    
    @Test
    public void testWebDriverPoolStatus() {
        log.info("开始测试WebDriverPool状态");
        
        try {
            // 获取池状态
            String status = poolManager.getPoolStatus();
            log.info("池状态: {}", status);
            
            // 借用一个实例
            WebDriver driver = poolManager.borrowWebDriver("about:blank");
            
            // 再次获取池状态
            String statusAfterBorrow = poolManager.getPoolStatus();
            log.info("借用后池状态: {}", statusAfterBorrow);
            
            // 归还实例
            poolManager.returnWebDriver(driver);
            
            // 最终池状态
            String finalStatus = poolManager.getPoolStatus();
            log.info("归还后池状态: {}", finalStatus);
            
            log.info("WebDriverPool状态测试通过");
            
        } catch (Exception e) {
            log.error("WebDriverPool状态测试失败", e);
            throw new RuntimeException("WebDriverPool状态测试失败", e);
        }
    }
    
    /**
     * 手动测试方法 - 可以直接运行来验证修复效果
     */
    public static void main(String[] args) {
        WebDriverPoolManagerTest test = new WebDriverPoolManagerTest();
        
        try {
            test.setUp();
            
            log.info("=== 开始WebDriver创建测试 ===");
            test.testWebDriverCreation();
            
            log.info("=== 开始多个WebDriver创建测试 ===");
            test.testMultipleWebDriverCreation();
            
            log.info("=== 开始WebDriverPool状态测试 ===");
            test.testWebDriverPoolStatus();
            
            log.info("=== 所有测试通过 ===");
            
        } catch (Exception e) {
            log.error("测试失败", e);
        } finally {
            test.tearDown();
        }
    }
}
