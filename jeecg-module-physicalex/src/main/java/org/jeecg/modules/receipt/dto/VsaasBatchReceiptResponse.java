package org.jeecg.modules.receipt.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * vsaas2.0批量电子票据申请响应
 * <AUTHOR>
 */
@Data
@ApiModel("批量电子票据申请响应")
public class VsaasBatchReceiptResponse {

    @ApiModelProperty(value = "响应状态码：0-全部成功，1-部分成功，-1-全部失败")
    private String code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "总申请数量")
    private Integer totalCount;

    @ApiModelProperty(value = "成功数量")
    private Integer successCount;

    @ApiModelProperty(value = "失败数量")
    private Integer failureCount;

    @ApiModelProperty(value = "批量申请结果列表")
    private List<VsaasBatchReceiptItem> resultList;

    @Data
    @ApiModel("批量申请结果项")
    public static class VsaasBatchReceiptItem {

        @ApiModelProperty(value = "支付单号")
        private String billNo;

        @ApiModelProperty(value = "处理状态：success/failure")
        private String status;

        @ApiModelProperty(value = "处理消息")
        private String message;

        @ApiModelProperty(value = "vsaas申请单号")
        private String vsaasApplyNo;

        @ApiModelProperty(value = "电子票据号")
        private String receiptNo;
    }

    /**
     * 创建成功响应
     */
    public static VsaasBatchReceiptResponse success(List<VsaasBatchReceiptItem> resultList) {
        VsaasBatchReceiptResponse response = new VsaasBatchReceiptResponse();
        response.setCode("0");
        response.setMessage("批量申请完成");
        response.setResultList(resultList);
        response.setTotalCount(resultList.size());
        response.setSuccessCount((int) resultList.stream().filter(item -> "success".equals(item.getStatus())).count());
        response.setFailureCount(resultList.size() - response.getSuccessCount());
        return response;
    }
}