package org.jeecg.modules.receipt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * vsaas2.0电子票据申请请求
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据申请请求")
public class VsaasReceiptRequest {

    @ApiModelProperty(value = "机构编码", required = true)
    @NotBlank(message = "机构编码不能为空")
    private String orgCode;

    @ApiModelProperty(value = "支付单号", required = true)
    @NotBlank(message = "支付单号不能为空")
    private String billNo;

    @ApiModelProperty(value = "支付单ID", required = true)
    @NotBlank(message = "支付单ID不能为空")
    private String billId;

    @ApiModelProperty(value = "票据类型：1-门诊收费票据，2-住院收费票据", required = true)
    @NotNull(message = "票据类型不能为空")
    private Integer receiptType;

    @ApiModelProperty(value = "业务类型：1-收费，2-退费", required = true)
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @ApiModelProperty(value = "患者姓名", required = true)
    @NotBlank(message = "患者姓名不能为空")
    private String patientName;

    @ApiModelProperty(value = "患者身份证号")
    private String patientIdCard;

    @ApiModelProperty(value = "患者手机号")
    private String patientPhone;

    @ApiModelProperty(value = "票据总金额", required = true)
    @NotNull(message = "票据总金额不能为空")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "收费日期", required = true)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "收费日期不能为空")
    private Date chargeDate;

    @ApiModelProperty(value = "收费员工编码")
    private String chargeUserCode;

    @ApiModelProperty(value = "收费员工姓名")
    private String chargeUserName;

    @ApiModelProperty(value = "科室编码")
    private String deptCode;

    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @ApiModelProperty(value = "票据明细列表", required = true)
    @NotNull(message = "票据明细不能为空")
    private List<VsaasReceiptDetailRequest> detailList;

    @ApiModelProperty(value = "团检标志：1-团检，0-个检")
    private Integer teamFlag;

    @ApiModelProperty(value = "预开票标志：1-预开票，0-正常开票")
    private Integer preIssueFlag;

    @ApiModelProperty(value = "团检单位名称")
    private String companyName;

    @ApiModelProperty(value = "团检单位编码")
    private String companyCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    @ApiModelProperty(value = "扩展字段3")
    private String ext3;
}