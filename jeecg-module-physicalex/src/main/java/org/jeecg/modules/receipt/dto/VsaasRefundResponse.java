package org.jeecg.modules.receipt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * vsaas2.0电子票据冲红响应
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据冲红响应")
public class VsaasRefundResponse {

    @ApiModelProperty(value = "响应状态码：0-成功，其他-失败")
    private String code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "原电子票据号")
    private String originalReceiptNo;

    @ApiModelProperty(value = "冲红票据号")
    private String refundReceiptNo;

    @ApiModelProperty(value = "vsaas冲红申请单号")
    private String vsaasRefundApplyNo;

    @ApiModelProperty(value = "冲红金额")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "冲红时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    @ApiModelProperty(value = "冲红票据PDF文件下载地址")
    private String refundPdfUrl;

    @ApiModelProperty(value = "冲红票据图片下载地址")
    private String refundImageUrl;

    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    @ApiModelProperty(value = "扩展字段3")
    private String ext3;

    /**
     * 创建成功响应
     */
    public static VsaasRefundResponse success(String originalReceiptNo, String refundReceiptNo, String vsaasRefundApplyNo) {
        VsaasRefundResponse response = new VsaasRefundResponse();
        response.setCode("0");
        response.setMessage("冲红成功");
        response.setOriginalReceiptNo(originalReceiptNo);
        response.setRefundReceiptNo(refundReceiptNo);
        response.setVsaasRefundApplyNo(vsaasRefundApplyNo);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static VsaasRefundResponse failure(String code, String message) {
        VsaasRefundResponse response = new VsaasRefundResponse();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }
}