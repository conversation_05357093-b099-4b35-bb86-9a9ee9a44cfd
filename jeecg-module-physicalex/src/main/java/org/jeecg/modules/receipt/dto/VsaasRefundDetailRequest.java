package org.jeecg.modules.receipt.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * vsaas2.0电子票据冲红明细请求
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据冲红明细请求")
public class VsaasRefundDetailRequest {

    @ApiModelProperty(value = "项目编码", required = true)
    @NotBlank(message = "项目编码不能为空")
    private String itemCode;

    @ApiModelProperty(value = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    private String itemName;

    @ApiModelProperty(value = "冲红数量", required = true)
    @NotNull(message = "冲红数量不能为空")
    private BigDecimal refundQuantity;

    @ApiModelProperty(value = "冲红金额", required = true)
    @NotNull(message = "冲红金额不能为空")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "备注")
    private String remark;
}