package org.jeecg.modules.receipt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子票据冲红记录表
 * <AUTHOR>
 */
@Data
@TableName("electronic_receipt_refund")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "electronic_receipt_refund对象", description = "电子票据冲红记录表")
public class ElectronicReceiptRefund implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "原电子票据ID", width = 15)
    @ApiModelProperty(value = "原电子票据ID")
    private String originalReceiptId;

    @Excel(name = "原电子票据号", width = 20)
    @ApiModelProperty(value = "原电子票据号")
    private String originalReceiptNo;

    @Excel(name = "原vsaas申请单号", width = 20)
    @ApiModelProperty(value = "原vsaas申请单号")
    private String originalVsaasApplyNo;

    @Excel(name = "冲红票据号", width = 20)
    @ApiModelProperty(value = "冲红票据号")
    private String refundReceiptNo;

    @Excel(name = "vsaas冲红申请单号", width = 20)
    @ApiModelProperty(value = "vsaas冲红申请单号")
    private String vsaasRefundApplyNo;

    @Excel(name = "退费单号", width = 20)
    @ApiModelProperty(value = "退费单号")
    private String refundBillNo;

    @Excel(name = "退费单ID", width = 15)
    @ApiModelProperty(value = "退费单ID")
    private String refundBillId;

    @Excel(name = "冲红类型", width = 15)
    @ApiModelProperty(value = "冲红类型：1-全额冲红，2-部分冲红")
    private Integer refundType;

    @Excel(name = "冲红金额", width = 15)
    @ApiModelProperty(value = "冲红金额")
    private BigDecimal refundAmount;

    @Excel(name = "退费日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "退费日期")
    private Date refundDate;

    @Excel(name = "退费员工编码", width = 15)
    @ApiModelProperty(value = "退费员工编码")
    private String refundUserCode;

    @Excel(name = "退费员工姓名", width = 15)
    @ApiModelProperty(value = "退费员工姓名")
    private String refundUserName;

    @Excel(name = "退费原因", width = 30)
    @ApiModelProperty(value = "退费原因")
    private String refundReason;

    @Excel(name = "冲红状态", width = 15)
    @ApiModelProperty(value = "冲红状态：1-申请中，2-已冲红，3-冲红失败")
    private Integer refundStatus;

    @Excel(name = "冲红时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "冲红时间")
    private Date refundTime;

    @Excel(name = "冲红票据PDF下载地址", width = 30)
    @ApiModelProperty(value = "冲红票据PDF文件下载地址")
    private String refundPdfUrl;

    @Excel(name = "冲红票据图片下载地址", width = 30)
    @ApiModelProperty(value = "冲红票据图片下载地址")
    private String refundImageUrl;

    @Excel(name = "同步状态", width = 15)
    @ApiModelProperty(value = "同步状态：0-未同步，1-已同步，2-同步失败")
    private Integer syncStatus;

    @Excel(name = "最后同步时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后同步时间")
    private Date lastSyncTime;

    @Excel(name = "错误消息", width = 30)
    @ApiModelProperty(value = "错误消息")
    private String errorMessage;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    @ApiModelProperty(value = "扩展字段3")
    private String ext3;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}