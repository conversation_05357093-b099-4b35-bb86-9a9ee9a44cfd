package org.jeecg.modules.receipt.service;

import org.jeecg.modules.receipt.dto.*;

/**
 * vsaas2.0电子票据API接口服务
 * 基于《医疗电子票据管理平台-接口规范vsaas2.0--内蒙古自治区人民医院20250630》
 * 
 * <AUTHOR>
 */
public interface IVsaasReceiptApiService {

    /**
     * 申请电子票据
     * 适用于个人体检和团检的即时开票场景
     * 
     * @param request 票据申请请求
     * @return 申请结果
     */
    VsaasReceiptResponse applyReceipt(VsaasReceiptRequest request);

    /**
     * 预开电子票据（团检场景）
     * 支持团检业务的预开票据，后续统一结算
     * 
     * @param request 预开票据请求
     * @return 预开结果
     */
    VsaasReceiptResponse preIssueReceipt(VsaasReceiptRequest request);

    /**
     * 查询票据状态
     * 支持通过票据号或vsaas申请单号查询
     * 
     * @param receiptNo 票据号
     * @param vsaasApplyNo vsaas申请单号
     * @return 票据状态
     */
    VsaasReceiptStatusResponse queryReceiptStatus(String receiptNo, String vsaasApplyNo);

    /**
     * 冲红票据
     * 支持全额冲红和部分冲红
     * 
     * @param request 冲红请求
     * @return 冲红结果
     */
    VsaasRefundResponse refundReceipt(VsaasRefundRequest request);

    /**
     * 作废票据
     * 用于作废未使用的票据
     * 
     * @param receiptNo 票据号
     * @param voidReason 作废原因
     * @return 作废结果
     */
    VsaasReceiptResponse voidReceipt(String receiptNo, String voidReason);

    /**
     * 批量申请票据
     * 适用于团检批量开票场景
     * 
     * @param requests 批量申请请求列表
     * @return 批量申请结果
     */
    VsaasBatchReceiptResponse batchApplyReceipt(java.util.List<VsaasReceiptRequest> requests);

    /**
     * 下载票据文件
     * 支持PDF和图片格式下载
     * 
     * @param receiptNo 票据号
     * @param fileType 文件类型：pdf/image
     * @return 文件下载地址
     */
    String downloadReceiptFile(String receiptNo, String fileType);

    /**
     * 同步票据状态
     * 定时同步vsaas平台的票据状态变更
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 同步结果
     */
    VsaasSyncResponse syncReceiptStatus(java.util.Date startTime, java.util.Date endTime);
}