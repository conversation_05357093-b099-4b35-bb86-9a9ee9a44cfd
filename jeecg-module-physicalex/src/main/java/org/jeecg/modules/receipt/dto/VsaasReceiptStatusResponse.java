package org.jeecg.modules.receipt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * vsaas2.0电子票据状态查询响应
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据状态查询响应")
public class VsaasReceiptStatusResponse {

    @ApiModelProperty(value = "响应状态码：0-成功，其他-失败")
    private String code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "电子票据号")
    private String receiptNo;

    @ApiModelProperty(value = "vsaas申请单号")
    private String vsaasApplyNo;

    @ApiModelProperty(value = "票据状态：1-申请中，2-已开具，3-已作废，4-已冲红")
    private Integer receiptStatus;

    @ApiModelProperty(value = "票据状态描述")
    private String receiptStatusDesc;

    @ApiModelProperty(value = "票据总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "已冲红金额")
    private BigDecimal refundedAmount;

    @ApiModelProperty(value = "剩余可冲红金额")
    private BigDecimal remainAmount;

    @ApiModelProperty(value = "开票时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    @ApiModelProperty(value = "作废时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date voidTime;

    @ApiModelProperty(value = "票据PDF文件下载地址")
    private String pdfUrl;

    @ApiModelProperty(value = "票据图片下载地址")
    private String imageUrl;

    @ApiModelProperty(value = "票据二维码")
    private String qrCode;

    @ApiModelProperty(value = "最后更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 创建成功响应
     */
    public static VsaasReceiptStatusResponse success(String receiptNo, Integer receiptStatus) {
        VsaasReceiptStatusResponse response = new VsaasReceiptStatusResponse();
        response.setCode("0");
        response.setMessage("查询成功");
        response.setReceiptNo(receiptNo);
        response.setReceiptStatus(receiptStatus);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static VsaasReceiptStatusResponse failure(String code, String message) {
        VsaasReceiptStatusResponse response = new VsaasReceiptStatusResponse();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }
}