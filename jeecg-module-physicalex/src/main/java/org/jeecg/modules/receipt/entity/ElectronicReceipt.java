package org.jeecg.modules.receipt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子票据主表
 * <AUTHOR>
 */
@Data
@TableName("electronic_receipt")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "electronic_receipt对象", description = "电子票据主表")
public class ElectronicReceipt implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "关联支付单ID", width = 15)
    @ApiModelProperty(value = "关联支付单ID")
    private String billId;

    @Excel(name = "支付单号", width = 15)
    @ApiModelProperty(value = "支付单号")
    private String billNo;

    @Excel(name = "电子票据号", width = 20)
    @ApiModelProperty(value = "电子票据号")
    private String receiptNo;

    @Excel(name = "vsaas申请单号", width = 20)
    @ApiModelProperty(value = "vsaas申请单号")
    private String vsaasApplyNo;

    @Excel(name = "机构编码", width = 15)
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    @Excel(name = "票据类型", width = 15)
    @ApiModelProperty(value = "票据类型：1-门诊收费票据，2-住院收费票据")
    private Integer receiptType;

    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型：1-收费，2-退费")
    private Integer businessType;

    @Excel(name = "患者姓名", width = 15)
    @ApiModelProperty(value = "患者姓名")
    private String patientName;

    @Excel(name = "患者身份证号", width = 20)
    @ApiModelProperty(value = "患者身份证号")
    private String patientIdCard;

    @Excel(name = "患者手机号", width = 15)
    @ApiModelProperty(value = "患者手机号")
    private String patientPhone;

    @Excel(name = "票据总金额", width = 15)
    @ApiModelProperty(value = "票据总金额")
    private BigDecimal totalAmount;

    @Excel(name = "已冲红金额", width = 15)
    @ApiModelProperty(value = "已冲红金额")
    private BigDecimal refundedAmount;

    @Excel(name = "收费日期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "收费日期")
    private Date chargeDate;

    @Excel(name = "收费员工编码", width = 15)
    @ApiModelProperty(value = "收费员工编码")
    private String chargeUserCode;

    @Excel(name = "收费员工姓名", width = 15)
    @ApiModelProperty(value = "收费员工姓名")
    private String chargeUserName;

    @Excel(name = "科室编码", width = 15)
    @ApiModelProperty(value = "科室编码")
    private String deptCode;

    @Excel(name = "科室名称", width = 15)
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @Excel(name = "票据状态", width = 15)
    @ApiModelProperty(value = "票据状态：1-申请中，2-已开具，3-已作废，4-已冲红")
    private Integer receiptStatus;

    @Excel(name = "开票时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开票时间")
    private Date issueTime;

    @Excel(name = "作废时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "作废时间")
    private Date voidTime;

    @Excel(name = "票据PDF下载地址", width = 30)
    @ApiModelProperty(value = "票据PDF文件下载地址")
    private String pdfUrl;

    @Excel(name = "票据图片下载地址", width = 30)
    @ApiModelProperty(value = "票据图片下载地址")
    private String imageUrl;

    @Excel(name = "票据二维码", width = 30)
    @ApiModelProperty(value = "票据二维码")
    private String qrCode;

    @Excel(name = "票据有效期", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "票据有效期")
    private Date expireTime;

    @Excel(name = "团检标志", width = 15)
    @ApiModelProperty(value = "团检标志：1-团检，0-个检")
    private Integer teamFlag;

    @Excel(name = "预开票标志", width = 15)
    @ApiModelProperty(value = "预开票标志：1-预开票，0-正常开票")
    private Integer preIssueFlag;

    @Excel(name = "团检单位名称", width = 20)
    @ApiModelProperty(value = "团检单位名称")
    private String companyName;

    @Excel(name = "团检单位编码", width = 15)
    @ApiModelProperty(value = "团检单位编码")
    private String companyCode;

    @Excel(name = "同步状态", width = 15)
    @ApiModelProperty(value = "同步状态：0-未同步，1-已同步，2-同步失败")
    private Integer syncStatus;

    @Excel(name = "最后同步时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后同步时间")
    private Date lastSyncTime;

    @Excel(name = "错误消息", width = 30)
    @ApiModelProperty(value = "错误消息")
    private String errorMessage;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    @ApiModelProperty(value = "扩展字段3")
    private String ext3;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}