package org.jeecg.modules.receipt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.receipt.entity.ElectronicReceipt;

import java.util.Date;
import java.util.List;

/**
 * 电子票据主表 Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface ElectronicReceiptMapper extends BaseMapper<ElectronicReceipt> {

    /**
     * 分页查询电子票据
     */
    Page<ElectronicReceipt> selectReceiptPage(Page<ElectronicReceipt> page,
                                              @Param("receiptNo") String receiptNo,
                                              @Param("billNo") String billNo,
                                              @Param("patientName") String patientName,
                                              @Param("patientIdCard") String patientIdCard,
                                              @Param("receiptStatus") Integer receiptStatus,
                                              @Param("teamFlag") Integer teamFlag,
                                              @Param("startDate") Date startDate,
                                              @Param("endDate") Date endDate);

    /**
     * 根据支付单ID查询电子票据
     */
    ElectronicReceipt selectByBillId(@Param("billId") String billId);

    /**
     * 根据票据号查询电子票据
     */
    ElectronicReceipt selectByReceiptNo(@Param("receiptNo") String receiptNo);

    /**
     * 根据vsaas申请单号查询电子票据
     */
    ElectronicReceipt selectByVsaasApplyNo(@Param("vsaasApplyNo") String vsaasApplyNo);

    /**
     * 查询需要同步状态的票据
     */
    List<ElectronicReceipt> selectNeedSyncReceipts(@Param("limit") Integer limit);

    /**
     * 查询团检预开票据
     */
    List<ElectronicReceipt> selectTeamPreIssueReceipts(@Param("companyCode") String companyCode,
                                                       @Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate);

    /**
     * 统计票据数量
     */
    Long countByStatus(@Param("receiptStatus") Integer receiptStatus,
                       @Param("startDate") Date startDate,
                       @Param("endDate") Date endDate);

    /**
     * 统计票据金额
     */
    java.math.BigDecimal sumAmountByStatus(@Param("receiptStatus") Integer receiptStatus,
                                           @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate);

    /**
     * 批量更新票据状态
     */
    int batchUpdateStatus(@Param("receiptIds") List<String> receiptIds,
                          @Param("receiptStatus") Integer receiptStatus,
                          @Param("updateBy") String updateBy);
}