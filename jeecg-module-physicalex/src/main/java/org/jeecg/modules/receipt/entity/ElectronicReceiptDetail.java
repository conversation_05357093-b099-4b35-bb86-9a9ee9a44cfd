package org.jeecg.modules.receipt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子票据明细表
 * <AUTHOR>
 */
@Data
@TableName("electronic_receipt_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "electronic_receipt_detail对象", description = "电子票据明细表")
public class ElectronicReceiptDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "电子票据ID", width = 15)
    @ApiModelProperty(value = "电子票据ID")
    private String receiptId;

    @Excel(name = "电子票据号", width = 20)
    @ApiModelProperty(value = "电子票据号")
    private String receiptNo;

    @Excel(name = "支付记录ID", width = 15)
    @ApiModelProperty(value = "支付记录ID")
    private String payRecordId;

    @Excel(name = "项目组合ID", width = 15)
    @ApiModelProperty(value = "项目组合ID")
    private String itemGroupId;

    @Excel(name = "项目编码", width = 15)
    @ApiModelProperty(value = "项目编码")
    private String itemCode;

    @Excel(name = "项目名称", width = 20)
    @ApiModelProperty(value = "项目名称")
    private String itemName;

    @Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private String specification;

    @Excel(name = "单位", width = 10)
    @ApiModelProperty(value = "单位")
    private String unit;

    @Excel(name = "单价", width = 15)
    @ApiModelProperty(value = "单价")
    private BigDecimal unitPrice;

    @Excel(name = "数量", width = 15)
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @Excel(name = "金额", width = 15)
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @Excel(name = "自费金额", width = 15)
    @ApiModelProperty(value = "自费金额")
    private BigDecimal selfPayAmount;

    @Excel(name = "执行科室编码", width = 15)
    @ApiModelProperty(value = "执行科室编码")
    private String execDeptCode;

    @Excel(name = "执行科室名称", width = 20)
    @ApiModelProperty(value = "执行科室名称")
    private String execDeptName;

    @Excel(name = "医生编码", width = 15)
    @ApiModelProperty(value = "医生编码")
    private String doctorCode;

    @Excel(name = "医生姓名", width = 15)
    @ApiModelProperty(value = "医生姓名")
    private String doctorName;

    @Excel(name = "项目分类编码", width = 15)
    @ApiModelProperty(value = "项目分类编码")
    private String categoryCode;

    @Excel(name = "项目分类名称", width = 20)
    @ApiModelProperty(value = "项目分类名称")
    private String categoryName;

    @Excel(name = "已冲红数量", width = 15)
    @ApiModelProperty(value = "已冲红数量")
    private BigDecimal refundedQuantity;

    @Excel(name = "已冲红金额", width = 15)
    @ApiModelProperty(value = "已冲红金额")
    private BigDecimal refundedAmount;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}