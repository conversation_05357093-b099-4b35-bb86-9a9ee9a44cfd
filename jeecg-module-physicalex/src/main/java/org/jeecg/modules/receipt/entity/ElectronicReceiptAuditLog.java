package org.jeecg.modules.receipt.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子票据审计日志表
 * <AUTHOR>
 */
@Data
@TableName("electronic_receipt_audit_log")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "electronic_receipt_audit_log对象", description = "电子票据审计日志表")
public class ElectronicReceiptAuditLog implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "关联票据ID", width = 15)
    @ApiModelProperty(value = "关联票据ID")
    private String receiptId;

    @Excel(name = "电子票据号", width = 20)
    @ApiModelProperty(value = "电子票据号")
    private String receiptNo;

    @Excel(name = "审计类型", width = 15)
    @ApiModelProperty(value = "审计类型：CREATE/UPDATE/DELETE/APPLY/REFUND/DOWNLOAD/SYNC/VOID")
    private String auditType;

    @Excel(name = "操作描述", width = 30)
    @ApiModelProperty(value = "操作描述")
    private String operationDesc;

    @Excel(name = "风险级别", width = 15)
    @ApiModelProperty(value = "风险级别：LOW/MEDIUM/HIGH/CRITICAL")
    private String riskLevel;

    @Excel(name = "合规标志", width = 15)
    @ApiModelProperty(value = "合规标志：1-合规，0-不合规")
    private String complianceFlag;

    @Excel(name = "合规检查结果", width = 30)
    @ApiModelProperty(value = "合规检查结果")
    private String complianceResult;

    @Excel(name = "操作前数据", width = 50)
    @ApiModelProperty(value = "操作前数据(JSON格式)")
    private String beforeData;

    @Excel(name = "操作后数据", width = 50)
    @ApiModelProperty(value = "操作后数据(JSON格式)")
    private String afterData;

    @Excel(name = "数据变更摘要", width = 30)
    @ApiModelProperty(value = "数据变更摘要")
    private String changeSummary;

    @Excel(name = "涉及金额", width = 15)
    @ApiModelProperty(value = "涉及金额")
    private BigDecimal involvedAmount;

    @Excel(name = "操作用户ID", width = 15)
    @ApiModelProperty(value = "操作用户ID")
    private String operatorId;

    @Excel(name = "操作用户姓名", width = 15)
    @ApiModelProperty(value = "操作用户姓名")
    private String operatorName;

    @Excel(name = "操作用户角色", width = 15)
    @ApiModelProperty(value = "操作用户角色")
    private String operatorRole;

    @Excel(name = "操作IP地址", width = 20)
    @ApiModelProperty(value = "操作IP地址")
    private String operatorIp;

    @Excel(name = "操作时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "操作时间")
    private Date operationTime;

    @Excel(name = "业务模块", width = 15)
    @ApiModelProperty(value = "业务模块")
    private String businessModule;

    @Excel(name = "业务场景", width = 15)
    @ApiModelProperty(value = "业务场景：individual/team/batch")
    private String businessScene;

    @Excel(name = "接口调用ID", width = 15)
    @ApiModelProperty(value = "接口调用ID")
    private String interfaceCallId;

    @Excel(name = "请求参数", width = 50)
    @ApiModelProperty(value = "请求参数(JSON格式)")
    private String requestParams;

    @Excel(name = "响应结果", width = 50)
    @ApiModelProperty(value = "响应结果(JSON格式)")
    private String responseResult;

    @Excel(name = "执行耗时", width = 15)
    @ApiModelProperty(value = "执行耗时(毫秒)")
    private Long executionTime;

    @Excel(name = "操作结果", width = 15)
    @ApiModelProperty(value = "操作结果：SUCCESS/FAILURE/PARTIAL")
    private String operationResult;

    @Excel(name = "错误代码", width = 15)
    @ApiModelProperty(value = "错误代码")
    private String errorCode;

    @Excel(name = "错误消息", width = 30)
    @ApiModelProperty(value = "错误消息")
    private String errorMessage;

    @Excel(name = "异常堆栈", width = 50)
    @ApiModelProperty(value = "异常堆栈信息")
    private String exceptionStack;

    @Excel(name = "关联业务ID", width = 15)
    @ApiModelProperty(value = "关联业务ID(支付单ID/退费单ID等)")
    private String relatedBusinessId;

    @Excel(name = "关联业务类型", width = 15)
    @ApiModelProperty(value = "关联业务类型")
    private String relatedBusinessType;

    @Excel(name = "数据敏感级别", width = 15)
    @ApiModelProperty(value = "数据敏感级别：PUBLIC/INTERNAL/CONFIDENTIAL/SECRET")
    private String dataSensitivityLevel;

    @Excel(name = "审计级别", width = 15)
    @ApiModelProperty(value = "审计级别：SYSTEM/BUSINESS/SECURITY")
    private String auditLevel;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    @ApiModelProperty(value = "扩展字段3")
    private String ext3;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}