package org.jeecg.modules.receipt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * vsaas2.0票据状态同步响应
 * <AUTHOR>
 */
@Data
@ApiModel("票据状态同步响应")
public class VsaasSyncResponse {

    @ApiModelProperty(value = "响应状态码：0-成功，其他-失败")
    private String code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "同步开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncStartTime;

    @ApiModelProperty(value = "同步结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date syncEndTime;

    @ApiModelProperty(value = "同步票据总数")
    private Integer totalCount;

    @ApiModelProperty(value = "成功同步数量")
    private Integer successCount;

    @ApiModelProperty(value = "失败同步数量")
    private Integer failureCount;

    @ApiModelProperty(value = "同步详情列表")
    private List<VsaasSyncItem> syncDetailList;

    @Data
    @ApiModel("同步详情项")
    public static class VsaasSyncItem {

        @ApiModelProperty(value = "电子票据号")
        private String receiptNo;

        @ApiModelProperty(value = "vsaas申请单号")
        private String vsaasApplyNo;

        @ApiModelProperty(value = "同步前状态")
        private Integer beforeStatus;

        @ApiModelProperty(value = "同步后状态")
        private Integer afterStatus;

        @ApiModelProperty(value = "同步状态：success/failure")
        private String syncStatus;

        @ApiModelProperty(value = "同步消息")
        private String syncMessage;

        @ApiModelProperty(value = "同步时间")
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private Date syncTime;
    }

    /**
     * 创建成功响应
     */
    public static VsaasSyncResponse success(List<VsaasSyncItem> syncDetailList) {
        VsaasSyncResponse response = new VsaasSyncResponse();
        response.setCode("0");
        response.setMessage("状态同步完成");
        response.setSyncDetailList(syncDetailList);
        response.setTotalCount(syncDetailList.size());
        response.setSuccessCount((int) syncDetailList.stream().filter(item -> "success".equals(item.getSyncStatus())).count());
        response.setFailureCount(syncDetailList.size() - response.getSuccessCount());
        response.setSyncEndTime(new Date());
        return response;
    }

    /**
     * 创建失败响应
     */
    public static VsaasSyncResponse failure(String code, String message) {
        VsaasSyncResponse response = new VsaasSyncResponse();
        response.setCode(code);
        response.setMessage(message);
        response.setSyncEndTime(new Date());
        return response;
    }
}