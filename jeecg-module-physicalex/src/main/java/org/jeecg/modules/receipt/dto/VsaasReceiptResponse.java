package org.jeecg.modules.receipt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * vsaas2.0电子票据申请响应
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据申请响应")
public class VsaasReceiptResponse {

    @ApiModelProperty(value = "响应状态码：0-成功，其他-失败")
    private String code;

    @ApiModelProperty(value = "响应消息")
    private String message;

    @ApiModelProperty(value = "vsaas申请单号")
    private String vsaasApplyNo;

    @ApiModelProperty(value = "电子票据号")
    private String receiptNo;

    @ApiModelProperty(value = "票据状态：1-申请中，2-已开具，3-已作废")
    private Integer receiptStatus;

    @ApiModelProperty(value = "票据PDF文件下载地址")
    private String pdfUrl;

    @ApiModelProperty(value = "票据图片下载地址")
    private String imageUrl;

    @ApiModelProperty(value = "票据二维码")
    private String qrCode;

    @ApiModelProperty(value = "开票时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueTime;

    @ApiModelProperty(value = "票据有效期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    @ApiModelProperty(value = "扩展字段3")
    private String ext3;

    /**
     * 创建成功响应
     */
    public static VsaasReceiptResponse success(String vsaasApplyNo, String receiptNo) {
        VsaasReceiptResponse response = new VsaasReceiptResponse();
        response.setCode("0");
        response.setMessage("申请成功");
        response.setVsaasApplyNo(vsaasApplyNo);
        response.setReceiptNo(receiptNo);
        response.setReceiptStatus(1);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static VsaasReceiptResponse failure(String code, String message) {
        VsaasReceiptResponse response = new VsaasReceiptResponse();
        response.setCode(code);
        response.setMessage(message);
        return response;
    }
}