package org.jeecg.modules.receipt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.receipt.entity.ElectronicReceiptAuditLog;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 电子票据审计日志 Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface ElectronicReceiptAuditLogMapper extends BaseMapper<ElectronicReceiptAuditLog> {

    /**
     * 分页查询审计日志
     */
    Page<ElectronicReceiptAuditLog> selectAuditLogPage(Page<ElectronicReceiptAuditLog> page,
                                                       @Param("receiptNo") String receiptNo,
                                                       @Param("auditType") String auditType,
                                                       @Param("riskLevel") String riskLevel,
                                                       @Param("complianceFlag") String complianceFlag,
                                                       @Param("operatorId") String operatorId,
                                                       @Param("businessScene") String businessScene,
                                                       @Param("startTime") Date startTime,
                                                       @Param("endTime") Date endTime);

    /**
     * 根据票据ID查询审计日志
     */
    List<ElectronicReceiptAuditLog> selectByReceiptId(@Param("receiptId") String receiptId);

    /**
     * 查询高风险操作日志
     */
    List<ElectronicReceiptAuditLog> selectHighRiskLogs(@Param("riskLevels") List<String> riskLevels,
                                                       @Param("startTime") Date startTime,
                                                       @Param("endTime") Date endTime);

    /**
     * 查询不合规操作日志
     */
    List<ElectronicReceiptAuditLog> selectNonComplianceLogs(@Param("startTime") Date startTime,
                                                            @Param("endTime") Date endTime);

    /**
     * 统计审计数据
     */
    List<Map<String, Object>> selectAuditStatistics(@Param("startTime") Date startTime,
                                                     @Param("endTime") Date endTime,
                                                     @Param("groupBy") String groupBy);

    /**
     * 查询异常操作日志
     */
    List<ElectronicReceiptAuditLog> selectAbnormalLogs(@Param("operatorId") String operatorId,
                                                       @Param("startTime") Date startTime,
                                                       @Param("endTime") Date endTime);

    /**
     * 批量插入审计日志
     */
    int batchInsert(@Param("auditLogs") List<ElectronicReceiptAuditLog> auditLogs);

    /**
     * 清理历史审计日志
     */
    int deleteHistoryLogs(@Param("beforeDate") Date beforeDate);

    /**
     * 统计操作频率
     */
    List<Map<String, Object>> selectOperationFrequency(@Param("operatorId") String operatorId,
                                                        @Param("startTime") Date startTime,
                                                        @Param("endTime") Date endTime);
}