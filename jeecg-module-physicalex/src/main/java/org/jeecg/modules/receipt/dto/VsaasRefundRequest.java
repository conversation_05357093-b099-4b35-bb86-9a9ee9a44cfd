package org.jeecg.modules.receipt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * vsaas2.0电子票据冲红请求
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据冲红请求")
public class VsaasRefundRequest {

    @ApiModelProperty(value = "机构编码", required = true)
    @NotBlank(message = "机构编码不能为空")
    private String orgCode;

    @ApiModelProperty(value = "原电子票据号", required = true)
    @NotBlank(message = "原电子票据号不能为空")
    private String originalReceiptNo;

    @ApiModelProperty(value = "原vsaas申请单号")
    private String originalVsaasApplyNo;

    @ApiModelProperty(value = "退费单号", required = true)
    @NotBlank(message = "退费单号不能为空")
    private String refundBillNo;

    @ApiModelProperty(value = "退费单ID", required = true)
    @NotBlank(message = "退费单ID不能为空")
    private String refundBillId;

    @ApiModelProperty(value = "冲红类型：1-全额冲红，2-部分冲红", required = true)
    @NotNull(message = "冲红类型不能为空")
    private Integer refundType;

    @ApiModelProperty(value = "冲红总金额", required = true)
    @NotNull(message = "冲红总金额不能为空")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "退费日期", required = true)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "退费日期不能为空")
    private Date refundDate;

    @ApiModelProperty(value = "退费员工编码")
    private String refundUserCode;

    @ApiModelProperty(value = "退费员工姓名")
    private String refundUserName;

    @ApiModelProperty(value = "退费原因", required = true)
    @NotBlank(message = "退费原因不能为空")
    private String refundReason;

    @ApiModelProperty(value = "冲红明细列表")
    private List<VsaasRefundDetailRequest> detailList;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "扩展字段1")
    private String ext1;

    @ApiModelProperty(value = "扩展字段2")
    private String ext2;

    @ApiModelProperty(value = "扩展字段3")
    private String ext3;
}