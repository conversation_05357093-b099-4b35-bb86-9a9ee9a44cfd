package org.jeecg.modules.receipt.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * vsaas2.0电子票据明细请求
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据明细请求")
public class VsaasReceiptDetailRequest {

    @ApiModelProperty(value = "项目编码", required = true)
    @NotBlank(message = "项目编码不能为空")
    private String itemCode;

    @ApiModelProperty(value = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    private String itemName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单价", required = true)
    @NotNull(message = "单价不能为空")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "数量", required = true)
    @NotNull(message = "数量不能为空")
    private BigDecimal quantity;

    @ApiModelProperty(value = "金额", required = true)
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty(value = "自费金额")
    private BigDecimal selfPayAmount;

    @ApiModelProperty(value = "执行科室编码")
    private String execDeptCode;

    @ApiModelProperty(value = "执行科室名称")
    private String execDeptName;

    @ApiModelProperty(value = "医生编码")
    private String doctorCode;

    @ApiModelProperty(value = "医生姓名")
    private String doctorName;

    @ApiModelProperty(value = "项目分类编码")
    private String categoryCode;

    @ApiModelProperty(value = "项目分类名称")
    private String categoryName;

    @ApiModelProperty(value = "备注")
    private String remark;
}