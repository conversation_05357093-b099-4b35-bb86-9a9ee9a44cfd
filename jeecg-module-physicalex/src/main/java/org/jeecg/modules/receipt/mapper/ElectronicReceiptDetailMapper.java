package org.jeecg.modules.receipt.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.receipt.entity.ElectronicReceiptDetail;

import java.util.List;

/**
 * 电子票据明细表 Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface ElectronicReceiptDetailMapper extends BaseMapper<ElectronicReceiptDetail> {

    /**
     * 根据票据ID查询明细
     */
    List<ElectronicReceiptDetail> selectByReceiptId(@Param("receiptId") String receiptId);

    /**
     * 根据票据号查询明细
     */
    List<ElectronicReceiptDetail> selectByReceiptNo(@Param("receiptNo") String receiptNo);

    /**
     * 根据支付记录ID查询明细
     */
    List<ElectronicReceiptDetail> selectByPayRecordId(@Param("payRecordId") String payRecordId);

    /**
     * 批量插入明细
     */
    int batchInsert(@Param("detailList") List<ElectronicReceiptDetail> detailList);

    /**
     * 更新冲红金额
     */
    int updateRefundedAmount(@Param("detailId") String detailId,
                             @Param("refundedQuantity") java.math.BigDecimal refundedQuantity,
                             @Param("refundedAmount") java.math.BigDecimal refundedAmount,
                             @Param("updateBy") String updateBy);

    /**
     * 统计明细总金额
     */
    java.math.BigDecimal sumAmountByReceiptId(@Param("receiptId") String receiptId);
}