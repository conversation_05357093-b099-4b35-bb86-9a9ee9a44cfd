package org.jeecg.modules.summary.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.summary.service.impl.WebDriverPoolManager;
import org.jeecg.modules.summary.service.impl.WebDriverProcessMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * WebDriver管理控制器
 * 提供WebDriver池的监控和管理功能
 * 
 * <AUTHOR>
 * @since 2024-08-07
 */
@Api(tags = "WebDriver管理")
@RestController
@RequestMapping("/webdriver/management")
@Slf4j
public class WebDriverManagementController {

    @Autowired
    private WebDriverPoolManager poolManager;

    @Autowired
    private WebDriverProcessMonitor processMonitor;

    /**
     * 获取WebDriver池状态
     */
    @ApiOperation(value = "获取WebDriver池状态", notes = "获取当前WebDriver池的详细状态信息")
    @GetMapping("/pool/status")
    public Result<Map<String, Object>> getPoolStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            
            // 池状态
            status.put("poolStatus", poolManager.getEnhancedPoolStatus());
            status.put("workingDriverCount", poolManager.getWorkingDriverCount());
            status.put("canSafelyCleanup", poolManager.canSafelyCleanup());

            // 工作状态详情
            Map<String, Object> workingDetails = new HashMap<>();
            poolManager.getWorkingStatus().values().forEach(workingStatus -> {
                if (workingStatus.isWorking()) {
                    workingDetails.put(workingStatus.getTaskId(),
                        String.format("%s (工作时长: %d分钟)",
                            workingStatus.getTaskType(),
                            workingStatus.getWorkingDurationMinutes()));
                }
            });
            status.put("workingTasks", workingDetails);

            // 监控报告
            WebDriverProcessMonitor.MonitoringReport report = processMonitor.getMonitoringReport();
            status.put("currentProcessCount", report.getCurrentProcessCount());
            status.put("totalCleanupCount", report.getTotalCleanupCount());
            status.put("reportTime", report.getReportTime());

            // 最近事件
            status.put("recentEvents", processMonitor.getRecentEvents(10));
            
            return Result.OK(status);
        } catch (Exception e) {
            log.error("获取WebDriver池状态失败", e);
            return Result.error("获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 强制清理所有Chrome进程
     */
    @ApiOperation(value = "强制清理Chrome进程", notes = "紧急情况下强制清理所有Chrome进程并重新初始化池")
    @PostMapping("/cleanup/force")
    public Result<String> forceCleanup(@RequestParam(defaultValue = "false") boolean ignoreWorkingStatus) {
        try {
            log.warn("收到强制清理Chrome进程请求，忽略工作状态: {}", ignoreWorkingStatus);

            // 获取清理前状态
            String beforeStatus = poolManager.getEnhancedPoolStatus();
            int beforeProcessCount = processMonitor.getMonitoringReport().getCurrentProcessCount();
            int workingCount = poolManager.getWorkingDriverCount();

            // 安全检查
            if (!ignoreWorkingStatus && workingCount > 0) {
                String warningMsg = String.format(
                    "当前有 %d 个WebDriver正在工作，不建议强制清理。如需强制执行，请设置 ignoreWorkingStatus=true",
                    workingCount
                );
                log.warn(warningMsg);
                poolManager.logWorkingDrivers();
                return Result.error(warningMsg);
            }

            // 执行强制清理
            poolManager.forceCleanupAllChromeProcesses(ignoreWorkingStatus);

            // 等待清理完成
            Thread.sleep(5000);

            // 获取清理后状态
            String afterStatus = poolManager.getEnhancedPoolStatus();
            int afterProcessCount = processMonitor.getMonitoringReport().getCurrentProcessCount();

            String result = String.format(
                "强制清理完成。清理前: %s (进程数: %d, 工作中: %d), 清理后: %s (进程数: %d)",
                beforeStatus, beforeProcessCount, workingCount, afterStatus, afterProcessCount
            );

            log.info(result);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("强制清理Chrome进程失败", e);
            return Result.error("强制清理失败: " + e.getMessage());
        }
    }

    /**
     * 安全清理Chrome进程（等待工作完成）
     */
    @ApiOperation(value = "安全清理Chrome进程", notes = "等待正在工作的WebDriver完成后再清理")
    @PostMapping("/cleanup/safe")
    public Result<String> safeCleanup(@RequestParam(defaultValue = "120") int timeoutSeconds) {
        try {
            log.info("收到安全清理Chrome进程请求，超时时间: {}秒", timeoutSeconds);

            int workingCount = poolManager.getWorkingDriverCount();
            if (workingCount > 0) {
                log.info("等待 {} 个WebDriver完成工作...", workingCount);
                poolManager.logWorkingDrivers();

                boolean completed = poolManager.waitForWorkingDriversToComplete(timeoutSeconds);
                if (!completed) {
                    return Result.error("等待超时，仍有WebDriver在工作。请使用强制清理或增加超时时间");
                }
            }

            // 执行清理
            poolManager.forceCleanupAllChromeProcesses(false);

            Thread.sleep(3000);

            String result = "安全清理完成，当前状态: " + poolManager.getEnhancedPoolStatus();
            log.info(result);
            return Result.OK(result);

        } catch (Exception e) {
            log.error("安全清理Chrome进程失败", e);
            return Result.error("安全清理失败: " + e.getMessage());
        }
    }

    /**
     * 获取Chrome进程详细信息
     */
    @ApiOperation(value = "获取Chrome进程信息", notes = "获取当前系统中所有Chrome进程的详细信息")
    @GetMapping("/processes/chrome")
    public Result<Map<String, Object>> getChromeProcesses() {
        try {
            Map<String, Object> processInfo = new HashMap<>();
            
            // 获取监控报告
            WebDriverProcessMonitor.MonitoringReport report = processMonitor.getMonitoringReport();
            processInfo.put("currentProcessCount", report.getCurrentProcessCount());
            processInfo.put("totalCleanupCount", report.getTotalCleanupCount());
            
            // 获取最近的监控事件
            processInfo.put("recentEvents", processMonitor.getRecentEvents(20));
            
            // 系统进程信息
            try {
                String os = System.getProperty("os.name").toLowerCase();
                String command;
                
                if (os.contains("win")) {
                    command = "tasklist /FI \"IMAGENAME eq chrome.exe\" /FO CSV";
                } else {
                    command = "ps aux | grep chrome | grep -v grep";
                }
                
                Process process = Runtime.getRuntime().exec(command);
                java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream())
                );
                
                StringBuilder processOutput = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    processOutput.append(line).append("\n");
                }
                
                processInfo.put("systemProcessList", processOutput.toString());
                
            } catch (Exception e) {
                processInfo.put("systemProcessList", "获取系统进程信息失败: " + e.getMessage());
            }
            
            return Result.OK(processInfo);
            
        } catch (Exception e) {
            log.error("获取Chrome进程信息失败", e);
            return Result.error("获取进程信息失败: " + e.getMessage());
        }
    }

    /**
     * 重启WebDriver池
     */
    @ApiOperation(value = "重启WebDriver池", notes = "完全重启WebDriver池，包括清理所有进程和重新初始化")
    @PostMapping("/pool/restart")
    public Result<String> restartPool() {
        try {
            log.warn("收到重启WebDriver池请求");
            
            // 获取重启前状态
            String beforeStatus = poolManager.getPoolStatus();
            
            // 执行重启
            poolManager.forceCleanupAllChromeProcesses();
            
            // 等待重启完成
            Thread.sleep(3000);
            
            // 获取重启后状态
            String afterStatus = poolManager.getPoolStatus();
            
            String result = String.format(
                "WebDriver池重启完成。重启前: %s, 重启后: %s",
                beforeStatus, afterStatus
            );
            
            log.info(result);
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("重启WebDriver池失败", e);
            return Result.error("重启失败: " + e.getMessage());
        }
    }

    /**
     * 获取监控配置信息
     */
    @ApiOperation(value = "获取监控配置", notes = "获取WebDriver监控的配置参数")
    @GetMapping("/config")
    public Result<Map<String, Object>> getConfig() {
        try {
            Map<String, Object> config = new HashMap<>();
            
            // 这里可以添加配置信息
            config.put("poolSize", "从配置中获取");
            config.put("monitorInterval", "从配置中获取");
            config.put("maxProcesses", "从配置中获取");
            config.put("cleanupThreshold", "从配置中获取");
            
            // 系统信息
            config.put("osName", System.getProperty("os.name"));
            config.put("javaVersion", System.getProperty("java.version"));
            config.put("availableProcessors", Runtime.getRuntime().availableProcessors());
            config.put("maxMemory", Runtime.getRuntime().maxMemory() / 1024 / 1024 + " MB");
            config.put("totalMemory", Runtime.getRuntime().totalMemory() / 1024 / 1024 + " MB");
            config.put("freeMemory", Runtime.getRuntime().freeMemory() / 1024 / 1024 + " MB");
            
            return Result.OK(config);
            
        } catch (Exception e) {
            log.error("获取配置信息失败", e);
            return Result.error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @ApiOperation(value = "健康检查", notes = "检查WebDriver池和监控系统的健康状态")
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = new HashMap<>();
            
            // 池健康状态
            String poolStatus = poolManager.getPoolStatus();
            health.put("poolStatus", poolStatus);
            health.put("poolHealthy", !poolStatus.contains("错误") && !poolStatus.contains("异常"));
            
            // 进程健康状态
            WebDriverProcessMonitor.MonitoringReport report = processMonitor.getMonitoringReport();
            int processCount = report.getCurrentProcessCount();
            health.put("processCount", processCount);
            health.put("processHealthy", processCount <= 10); // 假设10个以下为健康
            
            // 整体健康状态
            boolean overallHealthy = (boolean) health.get("poolHealthy") && (boolean) health.get("processHealthy");
            health.put("overallHealthy", overallHealthy);
            health.put("status", overallHealthy ? "HEALTHY" : "UNHEALTHY");
            
            return Result.OK(health);
            
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return Result.error("健康检查失败: " + e.getMessage());
        }
    }
}
