package org.jeecg.modules.summary.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.summary.entity.SummaryAdvice;
import org.jeecg.modules.summary.mapper.SummaryAdviceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Lucene Index Batch Service
 * Handle large-scale index operations with pagination
 */
@Service
@Slf4j
public class LuceneIndexBatchService {

    @Autowired
    private SummaryAdviceMapper summaryAdviceMapper;

    @Autowired
    private LuceneIndexManager luceneIndexManager;

    @Autowired
    private SummaryAdviceDocumentBuilder documentBuilder;

    private static final int BATCH_SIZE = 1000; // 每批处理1000条记录

    /**
     * Rebuild index with pagination
     */
    public void rebuildIndexWithPagination(ProgressCallback callback) {
        try {
            // 获取总记录数
            long totalCount = summaryAdviceMapper.getTotalCount();
            log.info("开始分页重建索引，总记录数: {}", totalCount);

            if (callback != null) {
                callback.onProgress(0, totalCount, "开始重建索引...");
            }

            // 清空现有索引
            luceneIndexManager.rebuildIndex(null);

            AtomicLong processedCount = new AtomicLong(0);
            AtomicInteger currentPage = new AtomicInteger(1);
            
            while (true) {
                int page = currentPage.get();
                int offset = (page - 1) * BATCH_SIZE;
                
                // 分页查询数据
                List<SummaryAdvice> batchData = summaryAdviceMapper.listByPage(offset, BATCH_SIZE);
                
                if (batchData.isEmpty()) {
                    break; // 没有更多数据
                }

                // 处理当前批次
                processBatch(batchData, processedCount, totalCount, callback);
                
                currentPage.incrementAndGet();
                
                // 避免内存溢出，适当休息
                Thread.sleep(100);
            }

            if (callback != null) {
                callback.onProgress(totalCount, totalCount, "索引重建完成");
                callback.onCompleted("索引重建成功完成，共处理 " + totalCount + " 条记录");
            }

            log.info("分页重建索引完成，共处理 {} 条记录", processedCount.get());

        } catch (Exception e) {
            log.error("分页重建索引失败", e);
            if (callback != null) {
                callback.onError("索引重建失败: " + e.getMessage());
            }
            throw new RuntimeException("分页重建索引失败", e);
        }
    }

    /**
     * Process a batch of data
     */
    private void processBatch(List<SummaryAdvice> batchData, AtomicLong processedCount, 
                             long totalCount, ProgressCallback callback) {
        try {
            // 构建文档列表
            List<org.apache.lucene.document.Document> documents = new java.util.ArrayList<>();
            
            for (SummaryAdvice advice : batchData) {
                if (documentBuilder.isValidForIndexing(advice)) {
                    org.apache.lucene.document.Document document = documentBuilder.buildDocument(advice);
                    documents.add(document);
                }
            }

            // 批量添加到索引
            if (!documents.isEmpty()) {
                boolean success = luceneIndexManager.addDocuments(documents);
                if (success) {
                    long processed = processedCount.addAndGet(batchData.size());
                    
                    if (callback != null) {
                        int progress = (int) ((processed * 100) / totalCount);
                        callback.onProgress(processed, totalCount, 
                            String.format("已处理 %d/%d 条记录 (%d%%)", processed, totalCount, progress));
                    }
                    
                    log.debug("成功处理批次，记录数: {}, 累计处理: {}", batchData.size(), processed);
                } else {
                    throw new RuntimeException("批次索引添加失败");
                }
            }

        } catch (Exception e) {
            log.error("处理批次数据失败", e);
            throw new RuntimeException("处理批次数据失败", e);
        }
    }

    /**
     * Progress callback interface
     */
    public interface ProgressCallback {
        void onProgress(long processed, long total, String message);
        void onCompleted(String message);
        void onError(String error);
    }

    /**
     * Simple progress callback implementation
     */
    public static class SimpleProgressCallback implements ProgressCallback {
        @Override
        public void onProgress(long processed, long total, String message) {
            log.info("进度更新: {}", message);
        }

        @Override
        public void onCompleted(String message) {
            log.info("任务完成: {}", message);
        }

        @Override
        public void onError(String error) {
            log.error("任务失败: {}", error);
        }
    }
}
