package org.jeecg.modules.summary.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.summary.entity.SummaryAdvice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Lucene Batch Update Service
 * Optimized batch processing for large datasets
 */
@Service
@Slf4j
public class LuceneBatchUpdateService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 批处理大小
    private static final int BATCH_SIZE = 1000;
    // 并发线程数
    private static final int THREAD_POOL_SIZE = 4;

    /**
     * 优化的批量更新索引时间
     * 方案一：分批处理
     */
    public void batchUpdateIndexTime(List<SummaryAdvice> adviceList) {
        if (adviceList == null || adviceList.isEmpty()) {
            return;
        }

        log.info("开始批量更新索引时间，总数据量: {}", adviceList.size());
        long startTime = System.currentTimeMillis();

        try {
            Date now = new Date();
            String updateSql = "UPDATE summary_advice SET index_time = ? WHERE id = ?";
            
            // 分批处理
            int totalSize = adviceList.size();
            int batchCount = (totalSize + BATCH_SIZE - 1) / BATCH_SIZE;
            
            for (int i = 0; i < batchCount; i++) {
                int startIndex = i * BATCH_SIZE;
                int endIndex = Math.min(startIndex + BATCH_SIZE, totalSize);
                
                List<SummaryAdvice> batchList = adviceList.subList(startIndex, endIndex);
                processBatch(batchList, updateSql, now);
                
                log.debug("完成第 {}/{} 批处理，当前批次大小: {}", i + 1, batchCount, batchList.size());
                
                // 避免过度占用资源，适当休息
                if (i % 10 == 0 && i > 0) {
                    Thread.sleep(50); // 休息50ms
                }
            }
            
            long endTime = System.currentTimeMillis();
            log.info("批量更新索引时间完成，总数据量: {}, 耗时: {}ms", totalSize, endTime - startTime);
            
        } catch (Exception e) {
            log.error("批量更新索引时间失败", e);
            throw new RuntimeException("批量更新索引时间失败", e);
        }
    }

    /**
     * 异步批量更新索引时间
     * 方案二：异步处理
     */
    @Async
    public CompletableFuture<Void> asyncBatchUpdateIndexTime(List<SummaryAdvice> adviceList) {
        return CompletableFuture.runAsync(() -> {
            batchUpdateIndexTime(adviceList);
        });
    }

    /**
     * 并发批量更新索引时间
     * 方案三：并发处理
     */
    public void concurrentBatchUpdateIndexTime(List<SummaryAdvice> adviceList) {
        if (adviceList == null || adviceList.isEmpty()) {
            return;
        }

        log.info("开始并发批量更新索引时间，总数据量: {}", adviceList.size());
        long startTime = System.currentTimeMillis();

        try {
            Date now = new Date();
            String updateSql = "UPDATE summary_advice SET index_time = ? WHERE id = ?";
            
            // 分割数据为多个子列表
            int totalSize = adviceList.size();
            int chunkSize = (totalSize + THREAD_POOL_SIZE - 1) / THREAD_POOL_SIZE;
            
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            AtomicInteger processedCount = new AtomicInteger(0);
            
            for (int i = 0; i < THREAD_POOL_SIZE; i++) {
                int startIndex = i * chunkSize;
                int endIndex = Math.min(startIndex + chunkSize, totalSize);
                
                if (startIndex >= totalSize) {
                    break;
                }
                
                List<SummaryAdvice> chunk = adviceList.subList(startIndex, endIndex);
                
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        batchUpdateChunk(chunk, updateSql, now);
                        int processed = processedCount.addAndGet(chunk.size());
                        log.debug("线程完成处理，当前进度: {}/{}", processed, totalSize);
                    } catch (Exception e) {
                        log.error("并发处理块失败", e);
                        throw new RuntimeException(e);
                    }
                });
                
                futures.add(future);
            }
            
            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            long endTime = System.currentTimeMillis();
            log.info("并发批量更新索引时间完成，总数据量: {}, 耗时: {}ms", totalSize, endTime - startTime);
            
        } catch (Exception e) {
            log.error("并发批量更新索引时间失败", e);
            throw new RuntimeException("并发批量更新索引时间失败", e);
        }
    }

    /**
     * 优化的批量更新策略
     * 方案四：多策略组合优化
     */
    public void optimizedBatchUpdate(List<SummaryAdvice> adviceList) {
        if (adviceList == null || adviceList.isEmpty()) {
            return;
        }

        log.info("开始优化批量更新，总数据量: {}", adviceList.size());
        long startTime = System.currentTimeMillis();

        try {
            Date now = new Date();
            int totalSize = adviceList.size();

            // 根据数据量选择最优策略
            if (totalSize <= 100) {
                // 小批量：直接使用PreparedStatement批处理
                directBatchUpdate(adviceList, now);
            } else if (totalSize <= 5000) {
                // 中等批量：使用优化的分批处理
                optimizedBatchProcess(adviceList, now);
            } else {
                // 大批量：使用临时表方案
                tempTableBatchUpdate(adviceList, now);
            }

            long endTime = System.currentTimeMillis();
            log.info("优化批量更新完成，总数据量: {}, 耗时: {}ms", totalSize, endTime - startTime);

        } catch (Exception e) {
            log.error("优化批量更新失败", e);
            throw new RuntimeException("优化批量更新失败", e);
        }
    }

    /**
     * 处理单个批次
     */
    @Transactional
    public void processBatch(List<SummaryAdvice> batchList, String updateSql, Date now) {
        List<Object[]> batchArgs = new ArrayList<>();
        
        for (SummaryAdvice advice : batchList) {
            Object[] values = new Object[]{now, advice.getId()};
            batchArgs.add(values);
        }
        
        jdbcTemplate.batchUpdate(updateSql, batchArgs);
    }

    /**
     * 并发处理数据块
     */
    @Transactional
    public void batchUpdateChunk(List<SummaryAdvice> chunk, String updateSql, Date now) {
        // 进一步分批处理，避免单个事务过大
        int chunkSize = chunk.size();
        int subBatchCount = (chunkSize + BATCH_SIZE - 1) / BATCH_SIZE;
        
        for (int i = 0; i < subBatchCount; i++) {
            int startIndex = i * BATCH_SIZE;
            int endIndex = Math.min(startIndex + BATCH_SIZE, chunkSize);
            
            List<SummaryAdvice> subBatch = chunk.subList(startIndex, endIndex);
            processBatch(subBatch, updateSql, now);
        }
    }

    /**
     * 直接批处理更新（小数据量）
     */
    @Transactional
    public void directBatchUpdate(List<SummaryAdvice> adviceList, Date now) {
        String updateSql = "UPDATE summary_advice SET index_time = ? WHERE id = ?";
        List<Object[]> batchArgs = new ArrayList<>();

        for (SummaryAdvice advice : adviceList) {
            Object[] values = new Object[]{now, advice.getId()};
            batchArgs.add(values);
        }

        jdbcTemplate.batchUpdate(updateSql, batchArgs);
        log.debug("直接批处理完成，数据量: {}", adviceList.size());
    }

    /**
     * 优化的分批处理（中等数据量）
     */
    private void optimizedBatchProcess(List<SummaryAdvice> adviceList, Date now) {
        String updateSql = "UPDATE summary_advice SET index_time = ? WHERE id = ?";
        int totalSize = adviceList.size();
        int optimalBatchSize = Math.min(BATCH_SIZE, 500); // 限制最大批次大小
        int batchCount = (totalSize + optimalBatchSize - 1) / optimalBatchSize;

        for (int i = 0; i < batchCount; i++) {
            int startIndex = i * optimalBatchSize;
            int endIndex = Math.min(startIndex + optimalBatchSize, totalSize);

            List<SummaryAdvice> batchList = adviceList.subList(startIndex, endIndex);
            processBatchOptimized(batchList, updateSql, now);

            // 每10批休息一下，避免数据库压力过大
            if (i % 10 == 0 && i > 0) {
                try {
                    Thread.sleep(10); // 休息10ms
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    /**
     * 临时表批量更新（大数据量）
     */
    @Transactional
    public void tempTableBatchUpdate(List<SummaryAdvice> adviceList, Date now) {
        try {
            // 创建临时表
            String createTempTable = """
                CREATE TEMPORARY TABLE temp_update_ids (
                    id VARCHAR(32) PRIMARY KEY,
                    update_time DATETIME
                )
                """;
            jdbcTemplate.execute(createTempTable);

            // 分批插入临时表
            String insertTempSql = "INSERT INTO temp_update_ids (id, update_time) VALUES (?, ?)";
            int batchSize = 1000;
            int totalSize = adviceList.size();

            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<SummaryAdvice> batch = adviceList.subList(i, endIndex);

                List<Object[]> batchArgs = new ArrayList<>();
                for (SummaryAdvice advice : batch) {
                    Object[] values = new Object[]{advice.getId(), now};
                    batchArgs.add(values);
                }

                jdbcTemplate.batchUpdate(insertTempSql, batchArgs);
            }

            // 使用JOIN更新主表
            String updateSql = """
                UPDATE summary_advice sa
                JOIN temp_update_ids tu ON sa.id = tu.id
                SET sa.index_time = tu.update_time
                """;
            int updatedRows = jdbcTemplate.update(updateSql);

            log.info("临时表批量更新完成，更新行数: {}", updatedRows);

        } catch (Exception e) {
            log.error("临时表批量更新失败", e);
            throw e;
        }
    }

    /**
     * 优化的批次处理
     */
    @Transactional
    public void processBatchOptimized(List<SummaryAdvice> batchList, String updateSql, Date now) {
        List<Object[]> batchArgs = new ArrayList<>(batchList.size());

        for (SummaryAdvice advice : batchList) {
            Object[] values = new Object[]{now, advice.getId()};
            batchArgs.add(values);
        }

        jdbcTemplate.batchUpdate(updateSql, batchArgs);
    }

    /**
     * 获取推荐的批处理方法（优化版）
     */
    public void recommendedBatchUpdate(List<SummaryAdvice> adviceList) {
        int size = adviceList.size();

        if (size <= 500) {
            // 小数据量，直接批量更新
            batchUpdateIndexTime(adviceList);
        } else if (size <= 5000) {
            // 中等数据量，使用优化批处理
            optimizedBatchUpdate(adviceList);
        } else if (size <= 15000) {
            // 大数据量，使用并发处理
            concurrentBatchUpdateIndexTime(adviceList);
        } else {
            // 超大数据量，使用临时表方案
            tempTableBatchUpdate(adviceList, new Date());
        }
    }
}
