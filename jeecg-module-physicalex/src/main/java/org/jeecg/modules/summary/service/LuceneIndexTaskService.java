package org.jeecg.modules.summary.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Lucene Index Task Service
 * Handle long-running index operations asynchronously
 */
@Service
@Slf4j
public class LuceneIndexTaskService {

    @Autowired
    private ISummaryAdviceService summaryAdviceService;

    // Task status storage
    private final ConcurrentHashMap<String, TaskStatus> taskStatusMap = new ConcurrentHashMap<>();
    private final AtomicLong taskIdGenerator = new AtomicLong(1);

    // Current running task
    private volatile String currentTaskId = null;

    /**
     * Start rebuild task asynchronously
     */
    public String startRebuildTask() {
        // Check if there's already a running task
        if (currentTaskId != null && isTaskRunning(currentTaskId)) {
            throw new RuntimeException("另一个重建索引的任务正在执行中: " + currentTaskId);
        }

        String taskId = "rebuild_" + taskIdGenerator.getAndIncrement();
        currentTaskId = taskId;

        TaskStatus taskStatus = new TaskStatus();
        taskStatus.setTaskId(taskId);
        taskStatus.setTaskType("REBUILD_ALL_INDEX");
        taskStatus.setStatus("RUNNING");
        taskStatus.setStartTime(System.currentTimeMillis());
        taskStatus.setMessage("Index rebuild task started");

        taskStatusMap.put(taskId, taskStatus);

        // Execute asynchronously
        executeRebuildTask(taskId);

        return taskId;
    }

    /**
     * Start create index task asynchronously
     */
    public String startCreateTask() {
        if (currentTaskId != null && isTaskRunning(currentTaskId)) {
            throw new RuntimeException("另一个任务正在运行: " + currentTaskId);
        }

        String taskId = "create_" + taskIdGenerator.getAndIncrement();
        currentTaskId = taskId;

        TaskStatus taskStatus = new TaskStatus();
        taskStatus.setTaskId(taskId);
        taskStatus.setTaskType("CREATE_INDEX");
        taskStatus.setStatus("RUNNING");
        taskStatus.setStartTime(System.currentTimeMillis());
        taskStatus.setMessage("Index creation task started");

        taskStatusMap.put(taskId, taskStatus);

        executeCreateTask(taskId);

        return taskId;
    }

    /**
     * Start update necessary index task asynchronously
     */
    public String startUpdateTask() {
        if (currentTaskId != null && isTaskRunning(currentTaskId)) {
            throw new RuntimeException("Another task is already running: " + currentTaskId);
        }

        String taskId = "update_" + taskIdGenerator.getAndIncrement();
        currentTaskId = taskId;

        TaskStatus taskStatus = new TaskStatus();
        taskStatus.setTaskId(taskId);
        taskStatus.setTaskType("UPDATE_NECESSARY_INDEX");
        taskStatus.setStatus("RUNNING");
        taskStatus.setStartTime(System.currentTimeMillis());
        taskStatus.setMessage("Index update task started");

        taskStatusMap.put(taskId, taskStatus);

        executeUpdateTask(taskId);

        return taskId;
    }

    /**
     * Execute rebuild task asynchronously
     */
    @Async
    public void executeRebuildTask(String taskId) {
        TaskStatus taskStatus = taskStatusMap.get(taskId);
        try {
            log.info("Starting rebuild task: {}", taskId);
            taskStatus.setMessage("重建所有索引...");

            summaryAdviceService.updateLuceneIndexAll();

            taskStatus.setStatus("COMPLETED");
            taskStatus.setMessage("Index rebuild completed successfully");
            taskStatus.setEndTime(System.currentTimeMillis());

            log.info("Rebuild task completed: {}", taskId);

        } catch (Exception e) {
            log.error("Rebuild task failed: {}", taskId, e);
            taskStatus.setStatus("FAILED");
            taskStatus.setMessage("重建索引失败: " + e.getMessage());
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setError(e.getMessage());
        } finally {
            if (taskId.equals(currentTaskId)) {
                currentTaskId = null;
            }
        }
    }

    /**
     * Execute create task asynchronously
     */
    @Async
    public void executeCreateTask(String taskId) {
        TaskStatus taskStatus = taskStatusMap.get(taskId);
        try {
            log.info("Starting create task: {}", taskId);
            taskStatus.setMessage("创建索引...");

            summaryAdviceService.createLuceneIndex();

            taskStatus.setStatus("COMPLETED");
            taskStatus.setMessage("索引创建完成");
            taskStatus.setEndTime(System.currentTimeMillis());

            log.info("Create task completed: {}", taskId);

        } catch (Exception e) {
            log.error("Create task failed: {}", taskId, e);
            taskStatus.setStatus("FAILED");
            taskStatus.setMessage("索引创建失败: " + e.getMessage());
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setError(e.getMessage());
        } finally {
            if (taskId.equals(currentTaskId)) {
                currentTaskId = null;
            }
        }
    }

    /**
     * Execute update task asynchronously
     */
    @Async
    public void executeUpdateTask(String taskId) {
        TaskStatus taskStatus = taskStatusMap.get(taskId);
        try {
            log.info("Starting update task: {}", taskId);
            taskStatus.setMessage("更新索引...");

            summaryAdviceService.updateLuceneIndexNessary();

            taskStatus.setStatus("COMPLETED");
            taskStatus.setMessage("索引更新完成");
            taskStatus.setEndTime(System.currentTimeMillis());

            log.info("Update task completed: {}", taskId);

        } catch (Exception e) {
            log.error("Update task failed: {}", taskId, e);
            taskStatus.setStatus("FAILED");
            taskStatus.setMessage("索引更新失败: " + e.getMessage());
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setError(e.getMessage());
        } finally {
            if (taskId.equals(currentTaskId)) {
                currentTaskId = null;
            }
        }
    }

    /**
     * Get task status
     */
    public TaskStatus getTaskStatus() {
        if (currentTaskId != null) {
            return taskStatusMap.get(currentTaskId);
        }
        
        // Return the latest completed task if no running task
        return taskStatusMap.values().stream()
                .filter(task -> "COMPLETED".equals(task.getStatus()) || "FAILED".equals(task.getStatus()))
                .max((t1, t2) -> Long.compare(t1.getEndTime(), t2.getEndTime()))
                .orElse(null);
    }

    /**
     * Get task status by ID
     */
    public TaskStatus getTaskStatus(String taskId) {
        return taskStatusMap.get(taskId);
    }

    /**
     * Check if task is running
     */
    public boolean isTaskRunning(String taskId) {
        TaskStatus status = taskStatusMap.get(taskId);
        return status != null && "RUNNING".equals(status.getStatus());
    }

    /**
     * Check if any task is running
     */
    public boolean hasRunningTask() {
        return currentTaskId != null && isTaskRunning(currentTaskId);
    }

    /**
     * Get current running task ID
     */
    public String getCurrentTaskId() {
        return currentTaskId;
    }

    /**
     * Clear completed tasks (keep only recent 10 tasks)
     */
    public void cleanupOldTasks() {
        if (taskStatusMap.size() > 10) {
            taskStatusMap.entrySet().removeIf(entry -> {
                TaskStatus status = entry.getValue();
                return !entry.getKey().equals(currentTaskId) && 
                       ("COMPLETED".equals(status.getStatus()) || "FAILED".equals(status.getStatus())) &&
                       (System.currentTimeMillis() - status.getEndTime()) > 24 * 60 * 60 * 1000; // 24 hours
            });
        }
    }

    /**
     * Task Status class
     */
    public static class TaskStatus {
        private String taskId;
        private String taskType;
        private String status; // RUNNING, COMPLETED, FAILED
        private String message;
        private String error;
        private long startTime;
        private long endTime;
        private int progress; // 0-100

        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }

        public String getTaskType() { return taskType; }
        public void setTaskType(String taskType) { this.taskType = taskType; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public String getError() { return error; }
        public void setError(String error) { this.error = error; }

        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }

        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }

        public int getProgress() { return progress; }
        public void setProgress(int progress) { this.progress = progress; }

        public long getDuration() {
            if (endTime > 0) {
                return endTime - startTime;
            }
            return System.currentTimeMillis() - startTime;
        }
    }
}
