package org.jeecg.modules.summary.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Lucene Operation Statistics Service
 * Independent service to track Lucene operations without circular dependencies
 */
@Service
@Slf4j
public class LuceneOperationStatsService {

    // Search operation counters
    private final AtomicLong totalSearchCount = new AtomicLong(0);
    private final AtomicLong failedSearchCount = new AtomicLong(0);

    // Index operation counters
    private final AtomicLong totalIndexCount = new AtomicLong(0);
    private final AtomicLong failedIndexCount = new AtomicLong(0);

    /**
     * Record search operation
     */
    public void recordSearchOperation(boolean success) {
        totalSearchCount.incrementAndGet();
        if (!success) {
            failedSearchCount.incrementAndGet();
        }
        log.debug("Search operation recorded: success={}, total={}, failed={}", 
            success, totalSearchCount.get(), failedSearchCount.get());
    }

    /**
     * Record index operation
     */
    public void recordIndexOperation(boolean success) {
        totalIndexCount.incrementAndGet();
        if (!success) {
            failedIndexCount.incrementAndGet();
        }
        log.debug("Index operation recorded: success={}, total={}, failed={}", 
            success, totalIndexCount.get(), failedIndexCount.get());
    }

    /**
     * Get search statistics
     */
    public SearchStats getSearchStats() {
        long total = totalSearchCount.get();
        long failed = failedSearchCount.get();
        double successRate = total > 0 ? (double)(total - failed) / total : 1.0;
        
        return new SearchStats(total, failed, successRate);
    }

    /**
     * Get index statistics
     */
    public IndexStats getIndexStats() {
        long total = totalIndexCount.get();
        long failed = failedIndexCount.get();
        double successRate = total > 0 ? (double)(total - failed) / total : 1.0;
        
        return new IndexStats(total, failed, successRate);
    }

    /**
     * Reset all metrics
     */
    public void resetMetrics() {
        totalSearchCount.set(0);
        failedSearchCount.set(0);
        totalIndexCount.set(0);
        failedIndexCount.set(0);
        log.info("All operation metrics have been reset");
    }

    /**
     * Get all statistics
     */
    public OperationStats getAllStats() {
        return new OperationStats(getSearchStats(), getIndexStats());
    }

    /**
     * Search Statistics
     */
    public static class SearchStats {
        private final long totalCount;
        private final long failedCount;
        private final double successRate;

        public SearchStats(long totalCount, long failedCount, double successRate) {
            this.totalCount = totalCount;
            this.failedCount = failedCount;
            this.successRate = successRate;
        }

        public long getTotalCount() { return totalCount; }
        public long getFailedCount() { return failedCount; }
        public double getSuccessRate() { return successRate; }
    }

    /**
     * Index Statistics
     */
    public static class IndexStats {
        private final long totalCount;
        private final long failedCount;
        private final double successRate;

        public IndexStats(long totalCount, long failedCount, double successRate) {
            this.totalCount = totalCount;
            this.failedCount = failedCount;
            this.successRate = successRate;
        }

        public long getTotalCount() { return totalCount; }
        public long getFailedCount() { return failedCount; }
        public double getSuccessRate() { return successRate; }
    }

    /**
     * Combined Operation Statistics
     */
    public static class OperationStats {
        private final SearchStats searchStats;
        private final IndexStats indexStats;

        public OperationStats(SearchStats searchStats, IndexStats indexStats) {
            this.searchStats = searchStats;
            this.indexStats = indexStats;
        }

        public SearchStats getSearchStats() { return searchStats; }
        public IndexStats getIndexStats() { return indexStats; }
    }
}
