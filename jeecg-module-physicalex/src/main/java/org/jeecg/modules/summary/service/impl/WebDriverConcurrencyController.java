package org.jeecg.modules.summary.service.impl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * WebDriver并发控制器
 * 管理WebDriver的并发访问，防止资源竞争和无限等待
 * 
 * <AUTHOR>
 * @since 2024-08-07
 */
@Component
public class WebDriverConcurrencyController {
    private static final Logger log = LoggerFactory.getLogger(WebDriverConcurrencyController.class);
    
    // 并发控制配置
    @Value("${webdriver.concurrency.max.concurrent:3}")
    private int maxConcurrentRequests;
    
    @Value("${webdriver.concurrency.queue.size:10}")
    private int queueSize;
    
    @Value("${webdriver.concurrency.timeout.seconds:60}")
    private int timeoutSeconds;
    
    @Value("${webdriver.concurrency.retry.max:3}")
    private int maxRetryCount;
    
    @Value("${webdriver.concurrency.retry.delay.ms:1000}")
    private int retryDelayMs;
    
    // 并发控制组件
    private Semaphore concurrencyLimiter;
    private BlockingQueue<ConcurrencyRequest> requestQueue;
    private ExecutorService requestProcessor;
    private ScheduledExecutorService timeoutChecker;
    
    // 统计信息
    private final AtomicInteger activeRequests = new AtomicInteger(0);
    private final AtomicInteger queuedRequests = new AtomicInteger(0);
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final AtomicLong timeoutRequests = new AtomicLong(0);
    
    private volatile boolean isShutdown = false;
    
    @PostConstruct
    public void init() {
        log.info("初始化WebDriver并发控制器 - 最大并发: {}, 队列大小: {}, 超时: {}秒", 
            maxConcurrentRequests, queueSize, timeoutSeconds);
        
        // 初始化并发控制组件
        concurrencyLimiter = new Semaphore(maxConcurrentRequests, true);
        requestQueue = new LinkedBlockingQueue<>(queueSize);
        
        // 初始化线程池
        requestProcessor = Executors.newFixedThreadPool(maxConcurrentRequests + 2, r -> {
            Thread t = new Thread(r, "WebDriverConcurrency-Processor");
            t.setDaemon(true);
            return t;
        });
        
        timeoutChecker = Executors.newScheduledThreadPool(1, r -> {
            Thread t = new Thread(r, "WebDriverConcurrency-TimeoutChecker");
            t.setDaemon(true);
            return t;
        });
        
        // 启动超时检查任务
        startTimeoutChecker();
        
        log.info("WebDriver并发控制器初始化完成");
    }
    
    /**
     * 执行WebDriver操作（带并发控制）
     */
    public <T> T executeWithConcurrencyControl(String operationName, 
                                             WebDriverOperation<T> operation) throws Exception {
        return executeWithConcurrencyControl(operationName, operation, maxRetryCount);
    }
    
    /**
     * 执行WebDriver操作（带并发控制和重试）
     */
    public <T> T executeWithConcurrencyControl(String operationName, 
                                             WebDriverOperation<T> operation, 
                                             int retryCount) throws Exception {
        totalRequests.incrementAndGet();
        
        ConcurrencyRequest<T> request = new ConcurrencyRequest<>(
            operationName, operation, retryCount, LocalDateTime.now());
        
        try {
            // 尝试加入队列
            if (!requestQueue.offer(request)) {
                failedRequests.incrementAndGet();
                throw new ConcurrencyException("请求队列已满，无法处理新的WebDriver操作请求");
            }
            
            queuedRequests.incrementAndGet();
            log.debug("WebDriver操作请求已加入队列: {}, 当前队列大小: {}", 
                operationName, requestQueue.size());
            
            // 提交处理任务
            Future<T> future = requestProcessor.submit(() -> processRequest(request));
            
            // 等待结果（带超时）
            try {
                T result = future.get(timeoutSeconds, TimeUnit.SECONDS);
                successfulRequests.incrementAndGet();
                return result;
            } catch (TimeoutException e) {
                timeoutRequests.incrementAndGet();
                future.cancel(true);
                throw new ConcurrencyException("WebDriver操作超时: " + operationName, e);
            }
            
        } catch (Exception e) {
            failedRequests.incrementAndGet();
            throw e;
        } finally {
            queuedRequests.decrementAndGet();
        }
    }
    
    /**
     * 处理并发请求
     */
    private <T> T processRequest(ConcurrencyRequest<T> request) throws Exception {
        boolean acquired = false;
        try {
            // 获取并发许可
            acquired = concurrencyLimiter.tryAcquire(timeoutSeconds, TimeUnit.SECONDS);
            if (!acquired) {
                throw new ConcurrencyException("获取并发许可超时: " + request.getOperationName());
            }
            
            activeRequests.incrementAndGet();
            log.debug("开始执行WebDriver操作: {}, 当前活跃请求: {}", 
                request.getOperationName(), activeRequests.get());
            
            // 执行操作（带重试）
            return executeWithRetry(request);
            
        } finally {
            if (acquired) {
                concurrencyLimiter.release();
                activeRequests.decrementAndGet();
                log.debug("WebDriver操作完成: {}, 当前活跃请求: {}", 
                    request.getOperationName(), activeRequests.get());
            }
        }
    }
    
    /**
     * 带重试的执行操作
     */
    private <T> T executeWithRetry(ConcurrencyRequest<T> request) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= request.getRetryCount(); attempt++) {
            try {
                log.debug("执行WebDriver操作: {}, 尝试次数: {}/{}", 
                    request.getOperationName(), attempt, request.getRetryCount());
                
                return request.getOperation().execute();
                
            } catch (Exception e) {
                lastException = e;
                log.warn("WebDriver操作失败: {}, 尝试次数: {}/{}, 错误: {}", 
                    request.getOperationName(), attempt, request.getRetryCount(), e.getMessage());
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < request.getRetryCount()) {
                    try {
                        Thread.sleep(retryDelayMs * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new ConcurrencyException("重试等待被中断", ie);
                    }
                }
            }
        }
        
        throw new ConcurrencyException("WebDriver操作重试失败: " + request.getOperationName(), lastException);
    }
    
    /**
     * 启动超时检查任务
     */
    private void startTimeoutChecker() {
        timeoutChecker.scheduleWithFixedDelay(() -> {
            if (isShutdown) {
                return;
            }
            
            try {
                checkAndCleanupTimeoutRequests();
            } catch (Exception e) {
                log.error("超时检查任务失败", e);
            }
        }, 30, 30, TimeUnit.SECONDS); // 每30秒检查一次
    }
    
    /**
     * 检查和清理超时请求
     */
    private void checkAndCleanupTimeoutRequests() {
        LocalDateTime cutoff = LocalDateTime.now().minus(timeoutSeconds * 2, ChronoUnit.SECONDS);
        
        // 清理队列中的超时请求
        requestQueue.removeIf(request -> {
            if (request.getCreateTime().isBefore(cutoff)) {
                log.warn("清理超时的队列请求: {}", request.getOperationName());
                return true;
            }
            return false;
        });
    }
    
    /**
     * 获取并发控制状态
     */
    public ConcurrencyStatus getStatus() {
        return new ConcurrencyStatus(
            activeRequests.get(),
            queuedRequests.get(),
            concurrencyLimiter.availablePermits(),
            totalRequests.get(),
            successfulRequests.get(),
            failedRequests.get(),
            timeoutRequests.get(),
            LocalDateTime.now()
        );
    }
    
    /**
     * 强制清理所有等待的请求
     */
    public void clearAllPendingRequests() {
        log.warn("强制清理所有等待的WebDriver请求");
        int clearedCount = requestQueue.size();
        requestQueue.clear();
        queuedRequests.set(0);
        log.info("已清理{}个等待的请求", clearedCount);
    }
    
    @PreDestroy
    public void shutdown() {
        log.info("关闭WebDriver并发控制器");
        isShutdown = true;
        
        // 清理等待的请求
        clearAllPendingRequests();
        
        // 关闭线程池
        if (requestProcessor != null && !requestProcessor.isShutdown()) {
            requestProcessor.shutdown();
            try {
                if (!requestProcessor.awaitTermination(10, TimeUnit.SECONDS)) {
                    requestProcessor.shutdownNow();
                }
            } catch (InterruptedException e) {
                requestProcessor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (timeoutChecker != null && !timeoutChecker.isShutdown()) {
            timeoutChecker.shutdown();
            try {
                if (!timeoutChecker.awaitTermination(5, TimeUnit.SECONDS)) {
                    timeoutChecker.shutdownNow();
                }
            } catch (InterruptedException e) {
                timeoutChecker.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("WebDriver并发控制器已关闭");
    }
    
    /**
     * WebDriver操作接口
     */
    @FunctionalInterface
    public interface WebDriverOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 并发请求类
     */
    private static class ConcurrencyRequest<T> {
        private final String operationName;
        private final WebDriverOperation<T> operation;
        private final int retryCount;
        private final LocalDateTime createTime;
        
        public ConcurrencyRequest(String operationName, WebDriverOperation<T> operation, 
                                int retryCount, LocalDateTime createTime) {
            this.operationName = operationName;
            this.operation = operation;
            this.retryCount = retryCount;
            this.createTime = createTime;
        }
        
        public String getOperationName() { return operationName; }
        public WebDriverOperation<T> getOperation() { return operation; }
        public int getRetryCount() { return retryCount; }
        public LocalDateTime getCreateTime() { return createTime; }
    }
    
    /**
     * 并发状态类
     */
    public static class ConcurrencyStatus {
        private final int activeRequests;
        private final int queuedRequests;
        private final int availablePermits;
        private final long totalRequests;
        private final long successfulRequests;
        private final long failedRequests;
        private final long timeoutRequests;
        private final LocalDateTime statusTime;
        
        public ConcurrencyStatus(int activeRequests, int queuedRequests, int availablePermits,
                               long totalRequests, long successfulRequests, long failedRequests,
                               long timeoutRequests, LocalDateTime statusTime) {
            this.activeRequests = activeRequests;
            this.queuedRequests = queuedRequests;
            this.availablePermits = availablePermits;
            this.totalRequests = totalRequests;
            this.successfulRequests = successfulRequests;
            this.failedRequests = failedRequests;
            this.timeoutRequests = timeoutRequests;
            this.statusTime = statusTime;
        }
        
        // Getters
        public int getActiveRequests() { return activeRequests; }
        public int getQueuedRequests() { return queuedRequests; }
        public int getAvailablePermits() { return availablePermits; }
        public long getTotalRequests() { return totalRequests; }
        public long getSuccessfulRequests() { return successfulRequests; }
        public long getFailedRequests() { return failedRequests; }
        public long getTimeoutRequests() { return timeoutRequests; }
        public LocalDateTime getStatusTime() { return statusTime; }
        
        @Override
        public String toString() {
            return String.format("并发状态 - 活跃: %d, 队列: %d, 可用许可: %d, 总请求: %d, 成功: %d, 失败: %d, 超时: %d",
                activeRequests, queuedRequests, availablePermits, totalRequests, 
                successfulRequests, failedRequests, timeoutRequests);
        }
    }
    
    /**
     * 并发异常类
     */
    public static class ConcurrencyException extends Exception {
        public ConcurrencyException(String message) {
            super(message);
        }
        
        public ConcurrencyException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
