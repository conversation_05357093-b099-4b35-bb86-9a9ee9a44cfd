package org.jeecg.modules.summary.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.DictTranslateUtil;
import org.jeecg.modules.basicinfo.entity.OrgInfo;
import org.jeecg.modules.basicinfo.entity.ReportSettingGroup;
import org.jeecg.modules.basicinfo.service.IOrgInfoService;
import org.jeecg.modules.basicinfo.service.IReportSettingGroupService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.occu.entity.ZyConclusion;
import org.jeecg.modules.occu.entity.ZyConclusionDetail;
import org.jeecg.modules.occu.entity.ZyInquiry;
import org.jeecg.modules.occu.mapper.ZyConclusionDetailMapper;
import org.jeecg.modules.occu.mapper.ZyConclusionMapper;
import org.jeecg.modules.occu.service.IZyIndustryService;
import org.jeecg.modules.occu.service.IZyInquiryService;
import org.jeecg.modules.reg.entity.*;
import org.jeecg.modules.reg.mapper.CompanyRegMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.mapper.CustomerRegDepartSummaryMapper;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.summary.bo.*;
import org.jeecg.modules.summary.entity.AdviceBean;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.entity.Report4SmallApp;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.service.ICustomerRegReportService;
import org.jeecg.modules.summary.service.SystemUserUtilService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.ISysDepartService;
import org.jeecg.modules.mobile.reg.entity.CustomerAccount;
import org.jeecg.modules.mobile.utils.FileUrlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-05-20
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustomerRegReportServiceImpl implements ICustomerRegReportService {

    @Autowired
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Autowired
    private CustomerRegDepartSummaryMapper customerRegDepartSummaryMapper;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;
    @Autowired
    private ICustomerRegItemResultService customerRegItemResultService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ZyConclusionDetailMapper zyConclusionDetailMapper;
    @Autowired
    private ZyConclusionMapper zyConclusionMapper;
    @Autowired
    private SystemUserUtilService systemUserUtilService;
    @Autowired
    private IReportSettingGroupService reportSettingGroupService;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private CompanyRegMapper companyRegMapper;
    @Autowired
    private ICustomerAccountService customerAccountService;
    @Autowired
    private IOrgInfoService orgInfoService;
    @Autowired
    private IZyInquiryService zyInquiryService;
    @Autowired
    private DictTranslateUtil dictTranslateUtil;


    @Override
    public ReportBean getReportBean(String customerRegId) {
        long start = System.currentTimeMillis();
        //1、获取体检登记信息
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            return null;
        }
        dictTranslateUtil.translate(customerReg);
        customerReg.setRegDate(DateUtil.formatDate(customerReg.getCreateTime()));
        customerReg.setGenderDesc(StringUtils.contains(customerReg.getGender(), "男") ? "先生" : "女士");
        String companyRegId = customerReg.getCompanyRegId();
        if (StringUtils.isNotBlank(companyRegId)) {
            CompanyReg companyReg = companyRegMapper.selectById(companyRegId);
            customerReg.setCompanyReg(companyRegMapper.selectById(companyRegId));
            if (companyReg != null && StringUtils.equals(companyReg.getCnameDisplayInReport(), "0")) {
                customerReg.setCompanyName("");
            }
        }
        //查询当前年度体检次数
        Long count = customerRegMapper.selectCount(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getIdCard, customerReg.getIdCard()).between(CustomerReg::getRegTime, DateUtil.beginOfYear(customerReg.getRegTime()), DateUtil.endOfYear(customerReg.getRegTime())));
        customerReg.setAnnualCount(Objects.nonNull(count) ? count.intValue() : 1);


        ReportBean reportBean = new ReportBean();

        //查询团检医院
        OrgInfo defultOrgInfo = orgInfoService.getDefultOrgInfo();
        if (StringUtils.isNotBlank(defultOrgInfo.getQualityPicture())) {
            defultOrgInfo.setQualityPictureList(Arrays.asList(StringUtils.split(defultOrgInfo.getQualityPicture(), ",")));
        }
        reportBean.setOrgInfo(defultOrgInfo);

        reportBean.setCustomerReg(customerReg);
        //2、获取总检信息
        CustomerRegSummary customerRegSummary = customerRegSummaryMapper.getByRegId(customerRegId);

        if (customerRegSummary != null) {
            List<AdviceBean> rawList = customerRegSummary.getSummaryJson();
            List<AdviceBean> summaryJson = JSONObject.parseArray(JSON.toJSONString(rawList), AdviceBean.class);
            if (CollectionUtils.isNotEmpty(summaryJson)) {
                String reportAdviceSeparator = sysSettingService.getValueByCode("report_advice_separator");
                String adviceText= "";
                if (StringUtils.isBlank(reportAdviceSeparator)) {
                    adviceText = summaryJson.stream()
                            .map(advice -> String.format("%s %s：%s", "*", advice.getName(), advice.getContent()))
                            .collect(Collectors.joining(System.lineSeparator()));
                }else {
                    if (StringUtils.equals(reportAdviceSeparator, "seq")) {
                        adviceText = summaryJson.stream()
                                .map(advice -> String.format("%s、%s：%s", advice.getSeq(), advice.getName(), advice.getContent()))
                                .collect(Collectors.joining(System.lineSeparator()));
                    } else {
                        adviceText = summaryJson.stream()
                                .map(advice -> String.format("%s %s：%s", reportAdviceSeparator, advice.getName(), advice.getContent()))
                                .collect(Collectors.joining(System.lineSeparator()));
                    }
                }

                customerRegSummary.setAdviceText(adviceText);
            }
            if (StringUtils.isNotBlank(customerRegSummary.getCreateBy())) {
                String creatorSignPic = systemUserUtilService.getSignPicByUsername(customerRegSummary.getCreateBy());
                customerRegSummary.setCreatorSignPic(creatorSignPic);
            }
            if (StringUtils.isNotBlank(customerRegSummary.getAuditeBy())) {
                String auditorSignPic = systemUserUtilService.getSignPicByUsername(customerRegSummary.getAuditeBy());
                customerRegSummary.setAuditorSignPic(auditorSignPic);
            }
            if (StringUtils.isNotBlank(customerRegSummary.getPreAuditBy())) {
                String preAuditorSignPic = systemUserUtilService.getSignPicByUsername(customerRegSummary.getPreAuditBy());
                customerRegSummary.setPreAuditorSignPic(preAuditorSignPic);
            }

            if (customerRegSummary.getConfirmTime() != null) {
                customerRegSummary.setAuditDate(DateUtil.format(customerRegSummary.getConfirmTime(), "yyyy-MM-dd"));
            }
            if (customerRegSummary.getChiefTime() != null) {
                customerRegSummary.setFormatChiefTime(DateUtil.format(customerRegSummary.getCreateTime(), "yyyy年MM月dd日"));
            }

        }
        reportBean.setSummaryAdvice(customerRegSummary);
        //3、获取科室总结
        String departSummaryAbnormalOnly = sysSettingService.getValueByCode("depart_summary_abnormal_only");
        departSummaryAbnormalOnly = StringUtils.isBlank(departSummaryAbnormalOnly) ? "1" : departSummaryAbnormalOnly;
        List<CustomerRegDepartSummary> departSummaryList = customerRegDepartSummaryMapper.listByCustomerReg4Report(customerRegId, departSummaryAbnormalOnly);
        for (int i = 0; i < departSummaryList.size(); i++) {
            CustomerRegDepartSummary customerRegDepartSummary = departSummaryList.get(i);
            if (StringUtils.isNotBlank(customerRegDepartSummary.getCreateBy())) {
                String creatorSignPic = systemUserUtilService.getSignPicByUsername(customerRegDepartSummary.getCreateBy());
                customerRegDepartSummary.setCreatorSignPic(creatorSignPic);
            }
            if (StringUtils.isNotBlank(customerRegDepartSummary.getAuditBy())) {
                String auditorSignPic = systemUserUtilService.getSignPicByUsername(customerRegDepartSummary.getAuditBy());
                customerRegDepartSummary.setAuditorSignPic(auditorSignPic);
            }
            customerRegDepartSummary.setSort(i + 1);
        }

        reportBean.setDepartSummaryList(departSummaryList);
        //4、获取小项结果
        List<CustomerRegItemResult> resultList = customerRegItemResultService.listByRegId(customerRegId);
        //将resultList按照itemGroupId进行分组
        Map<String, List<CustomerRegItemResult>> groupedByGroupIdResults = resultList.stream().collect(Collectors.groupingBy(result -> result.getItemGroupId() + (StringUtils.isNotBlank(result.getCheckPartCode()) ? "-" + result.getCheckPartCode() : "")));

        //5、获取大项结果
        List<CustomerRegItemGroup> groupList = customerRegItemGroupService.listWithItemGroupByReg(customerRegId, null, false);
        groupList.forEach(group -> {
            List<TextBean> reportPicBeanList = new ArrayList<>();
            if (group.getReportPics() != null && !group.getReportPics().isEmpty()) {
                for (int i = 0; i < group.getReportPics().size(); i++) {
                    TextBean textBean = new TextBean();
                    textBean.setSeq(String.valueOf(i + 1));
                    textBean.setText(group.getReportPics().get(i));
                    reportPicBeanList.add(textBean);
                }
            }
            group.setReportPicBeanList(reportPicBeanList);
        });
        //过滤掉已退费和已减项的大项
        groupList = groupList.stream().filter(group -> group.getAddMinusFlag() != -1 && !StringUtils.equals(group.getPayStatus(), ExConstants.REFUND_STATE_退款成功)).toList();
        //为groupList设置itemResultList
        groupList.forEach(group -> {
            List<CustomerRegItemResult> itemResultList = groupedByGroupIdResults.get(group.getItemGroupId()+ (StringUtils.isNotBlank(group.getCheckPartCode()) ? "-" + group.getCheckPartCode() : ""));
            group.setResultList(Objects.requireNonNullElseGet(itemResultList, ArrayList::new));
            Date checkTime = group.getCheckTime();
            if (checkTime != null) {
                group.setCheckDate(DateUtil.format(checkTime, "yyyy-MM-dd"));
            }
        });

        //6、设置组合结果集合
        List<GroupBean> groupBeanList = groupList.stream().map(group -> {
            GroupBean groupBean = new GroupBean();
            groupBean.setGroupId(group.getId());
            groupBean.setGroupName(group.getItemGroupName());
            groupBean.setCheckStatus(group.getCheckStatus());
            groupBean.setCheckStatusDesc(StringUtils.equals(group.getCheckStatus(), "未检") ? "未完成" : "已完成");
            groupBean.setCheckConclusion(group.getCheckConclusion());
            groupBean.setAuditDoctorCode(group.getAuditDoctorCode());
            groupBean.setAuditDoctorName(group.getAuditDoctorName());
            groupBean.setCheckDoctorCode(group.getCheckDoctorCode());
            groupBean.setCheckDoctorName(group.getCheckDoctorName());
            groupBean.setReportDoctorCode(group.getReportDoctorCode());
            groupBean.setReportDoctorName(group.getReportDoctorName());
            groupBean.setPic(group.getPic());
            groupBean.setReportPics(group.getReportPics());
            groupBean.setDepartmentId(group.getDepartmentId());
            groupBean.setDepartmentName(group.getDepartmentName());
            groupBean.setItemResultList(group.getResultList());
            groupBean.setClassCode(group.getClassCode());
            groupBean.setReportPicBeanList(group.getReportPicBeanList());
            groupBean.setCheckPartCode(group.getCheckPartCode());
            groupBean.setCheckPartName(group.getCheckPartName());
            Date checkTime = group.getCheckTime();
            if (checkTime != null) {
                groupBean.setCheckTime(DateUtil.format(checkTime, "yyyy-MM-dd HH:mm:ss"));
                groupBean.setCheckDate(DateUtil.format(checkTime, "yyyy-MM-dd"));
            }

            return groupBean;
        }).toList();

        reportBean.setGroupBeanList(groupBeanList);

        //对groupList按照departmentId进行分组
        Map<String, List<CustomerRegItemGroup>> groupedByDepartIdGroups = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getDepartmentId));

        //拼装departAndGroupBean的List
        List<DepartAndGroupBean> departAndGroupBeanList = groupedByDepartIdGroups.entrySet().stream().map(entry -> {
            DepartAndGroupBean departAndGroupBean = new DepartAndGroupBean();
            SysDepart depart = sysDepartService.getById(entry.getKey());
            departAndGroupBean.setDepart(depart);
            departAndGroupBean.setGroupList(entry.getValue());
            List<CustomerRegItemGroup> summarizingGroupList = Lists.newArrayList();
            Map<String, List<CustomerRegItemGroup>> groupMap = entry.getValue().stream().filter(i -> StringUtils.isNotBlank(i.getCheckConclusion())).collect(Collectors.groupingBy(CustomerRegItemGroup::getCheckConclusion));
            for (String checkConclusion : groupMap.keySet()) {
                List<CustomerRegItemGroup> itemGroups = groupMap.get(checkConclusion);
                CustomerRegItemGroup customerRegItemGroup = itemGroups.get(0);
                customerRegItemGroup.setItemGroupName(itemGroups.stream().map(i -> i.getItemGroupName()).collect(Collectors.joining("\n")));
                summarizingGroupList.add(customerRegItemGroup);
            }
            departAndGroupBean.setSummarizingGroupList(CollectionUtils.isNotEmpty(summarizingGroupList) ? summarizingGroupList : entry.getValue());
            return departAndGroupBean;
        }).sorted(Comparator.comparingInt(o -> o.getDepart().getReportSort() != null ? o.getDepart().getReportSort() : 0)).collect(Collectors.toCollection(ArrayList::new));

        reportBean.setDepartAndGroupList(departAndGroupBeanList);

        //获取报告中大项的分组设置
        List<ReportSettingGroup> reportSettingGroupList = reportSettingGroupService.listReportSettingGroup();
        //Map<String, DepartAndGroupBean> departAndGroupBeanMap = departAndGroupBeanList.stream().collect(Collectors.toMap(departAndGroupBean -> departAndGroupBean.getDepart().getId(), departAndGroupBean -> departAndGroupBean));

        Map<String, List<CustomerRegItemGroup>> groupByReportSetting = new TreeMap<>();
        List<CustomerRegItemGroup> finalGroupList = groupList;
        reportSettingGroupList.forEach(reportSettingGroup -> {
            List<CustomerRegItemGroup> temp = new ArrayList<>();

            reportSettingGroup.getDepartList().forEach(reportSettingDepart -> {
                temp.addAll(Optional.ofNullable(groupedByDepartIdGroups.get(reportSettingDepart.getDepartmentId())).orElseGet(ArrayList::new));
            });
            reportSettingGroup.getItemgroupList().forEach(reportSettingItemgroup -> {
                //从groupList中获取itemGroupId为reportSettingItemgroup.getItemGroupId()的大项，如果temp中不存在则添加
                List<CustomerRegItemGroup> groupListByItemGroupId = finalGroupList.stream().filter(group -> StringUtils.equals(group.getItemGroupId(), reportSettingItemgroup.getItemgroupId())).toList();
                //如果temp中不存在则添加
                groupListByItemGroupId.forEach(group -> {
                    if (!temp.contains(group)) {
                        temp.add(group);
                    }
                });
            });

            groupByReportSetting.put(reportSettingGroup.getCode(), temp);
        });

        //设置按功能分组的大项
        reportBean.setGroupByFunctionMap(groupByReportSetting);


        //设置按功能分组的大项图片
        Map<String, List<TextBean>> groupByFunctionPicMap = new TreeMap<>();
        groupByReportSetting.forEach((key, value) -> {
            List<TextBean> reportPicBeanList = new ArrayList<>();
            Set<String> uniqueReportPdfInterfaces = new HashSet<>();
            value.forEach(group -> {
                if (group.getReportPics() != null && !group.getReportPics().isEmpty()) {
                    for (int i = 0; i < group.getReportPics().size(); i++) {
                        //String reportPdfInterface = group.getReportPdfInterface();
                        String pic = group.getReportPics().get(i);
                        if (uniqueReportPdfInterfaces.add(pic)) {
                            TextBean textBean = new TextBean();
                            textBean.setSeq(String.valueOf(i + 1));
                            textBean.setText(group.getReportPics().get(i));
                            reportPicBeanList.add(textBean);
                        }
                    }
                }
            });
            groupByFunctionPicMap.put(key, reportPicBeanList);
        });
        reportBean.setGroupByFunctionPicMap(groupByFunctionPicMap);
        //从departAndGroupBeanList中获取异常的组合结果
        /* List<DepartAndGroupBean> abnormalDepartAndGroupBeanList = departAndGroupBeanList.stream().filter(departAndGroupBean -> {
            List<CustomerRegItemGroup> abnormalGroups = departAndGroupBean.getGroupList().stream().filter(group -> StringUtils.equals(group.getAbnormalFlag(), "1")).toList();
            return !abnormalGroups.isEmpty();
        }).toList();

       reportBean.setAbnormalDepartAndGroupList(abnormalDepartAndGroupBeanList);
        //根据abnormalDepartAndGroupBeanList生成格式为：科室：小项名结论或数值
        List<String> abnormalGroupStrList = abnormalDepartAndGroupBeanList.stream().map(departAndGroupBean -> {
            String departName = departAndGroupBean.getDepart().getDepartName();
            List<String> groupStrList = departAndGroupBean.getGroupList().stream().filter(group -> StringUtils.equals(group.getAbnormalFlag(), "1")).map(group -> {
                String abnormalItemStr = group.getResultList().stream().filter(item -> StringUtils.equals(item.getAbnormalFlag(), "1")).map(result -> {
                    String itemName = result.getItemName();
                    String value = result.getValue();
                    String unit = StringUtils.defaultString(result.getUnit());
                    String checkConclusion = StringUtils.trimToEmpty(result.getCheckConclusion());
                    return itemName + " " + StringUtils.trimToEmpty(result.getAbnormalFlagDesc()) + (StringUtils.isNotBlank(checkConclusion) ? checkConclusion : value + " " + unit);
                    // return result.getItemName() + " " + result.getValue() + " " + StringUtils.defaultString(result.getUnit()) + " " + StringUtils.trimToEmpty(result.getCheckConclusion());
                }).collect(Collectors.joining("，"));

                //return departName + "：" + abnormalItemStr;
                return abnormalItemStr;
            }).toList();
            return groupStrList;
        }).flatMap(Collection::stream).toList();
        List<TextBean> abnormalGroupTextBeanList = new ArrayList<>();
        for (int i = 0; i < abnormalGroupStrList.size(); i++) {
            TextBean textBean = new TextBean();
            textBean.setSeq(String.valueOf(i + 1));
            textBean.setText(abnormalGroupStrList.get(i));
            abnormalGroupTextBeanList.add(textBean);
        }
        reportBean.setAbnormalTextList(abnormalGroupTextBeanList);*/


        //7、获取历史组合结果
        List<CustomerRegItemResult> historyGroupResultList = customerRegItemResultService.listHistoryResultByIdCard(customerReg.getIdCard(), 3);
        reportBean.setHistoryGroupResultList(historyGroupResultList);
        //统计historyGroupResultList内年份的唯一值数量
        long yearCount = historyGroupResultList.stream().map(CustomerRegItemResult::getCheckYear).distinct().count();
        reportBean.setShowHistory(yearCount > 1);
        reportBean.setShowKeyGroup(yearCount > 1);

        //8、获取关键组合结果
        //从historyGroupResultList中筛选出异常的组合ID，去重，再从resultList中筛选出这些组合ID对应的组合结果
        // 从historyGroupResultList中筛选出异常的组合ID
        Set<String> abnormalGroupIds = historyGroupResultList.stream().filter(result -> StringUtils.equals(result.getAbnormalFlag(), "1")).map(CustomerRegItemResult::getItemGroupId).collect(Collectors.toSet());

        // 从resultList中筛选出这些异常的组合ID对应的组合结果
        List<CustomerRegItemResult> abnormalResults = resultList.stream().filter(result -> abnormalGroupIds.contains(result.getItemGroupId())).toList();
        reportBean.setKeyGroupResultList(abnormalResults);

        //9.整理出所有有图片的组合结果
        List<CustomerRegItemGroup> imageGroupResultList = groupList.stream().filter(group -> {
            return StringUtils.equals(group.getItemGroup().getReportPicAttachable(), "1") && group.getReportPics() != null && !group.getReportPics().isEmpty();
        }).toList();
        // Convert to a modifiable list if necessary
        List<CustomerRegItemGroup> modifiableImageGroupResultList = new ArrayList<>(imageGroupResultList);
        //按照reportPdfInterface去重
//        modifiableImageGroupResultList = modifiableImageGroupResultList.stream().collect(Collectors.toMap(CustomerRegItemGroup::getReportPics, group -> group, (group1, group2) -> group1)).values().stream().toList();
//        modifiableImageGroupResultList = modifiableImageGroupResultList.stream().collect(Collectors.toMap(CustomerRegItemGroup::getReportPdfInterface, group -> group, (group1, group2) -> group1)).values().stream().toList();
        Map<List<String>, CustomerRegItemGroup> reportPicsMap = modifiableImageGroupResultList.stream().filter(group -> StringUtils.isBlank(group.getReportPdfInterface())).collect(Collectors.toMap(CustomerRegItemGroup::getReportPics, group -> group, (group1, group2) -> group1));
        Map<String, CustomerRegItemGroup> reportPdfInterfaceMap = modifiableImageGroupResultList.stream().filter(group -> StringUtils.isNotBlank(group.getReportPdfInterface())).collect(Collectors.toMap(CustomerRegItemGroup::getReportPdfInterface, group -> group, (group1, group2) -> group1));
        modifiableImageGroupResultList = new ArrayList<>(reportPdfInterfaceMap.values());
        modifiableImageGroupResultList.addAll(reportPicsMap.values());

        List<CustomerRegItemGroup> modifiableImageGroupResultList2 = new ArrayList<>(modifiableImageGroupResultList);
        // Sort the modifiable list
        modifiableImageGroupResultList2.sort(Comparator.comparingInt(group -> group.getItemGroup().getSort() == null ? 0 : group.getItemGroup().getSort()));

        // If needed, assign back to the original list reference
        imageGroupResultList = modifiableImageGroupResultList2;


        List<String> reportImgList = imageGroupResultList.stream().map(CustomerRegItemGroup::getReportPics).flatMap(Collection::stream).toList();
        //将reportImgList转换为List<TextBean>
        List<TextBean> reportImgTextBeanList = new ArrayList<>();
        for (int i = 0; i < reportImgList.size(); i++) {
            //判断reportImgTextBeanList中是否已经存在该图片
            int finalI = i;
            if (reportImgTextBeanList.stream().noneMatch(textBean -> StringUtils.equals(textBean.getText(), reportImgList.get(finalI)))) {
                TextBean textBean = new TextBean();
                textBean.setSeq(String.valueOf(i + 1));
                textBean.setText(reportImgList.get(i));
                reportImgTextBeanList.add(textBean);
            }
            /*TextBean textBean = new TextBean();
            textBean.setSeq(String.valueOf(i + 1));
            textBean.setText(reportImgList.get(i));
            reportImgTextBeanList.add(textBean);*/
        }
        reportBean.setReportImgList(reportImgTextBeanList);

        //10.获取职业检结论
        //10.1职业检总检结论
        ZyConclusion zyConclusion = zyConclusionMapper.selectOne(new LambdaQueryWrapper<ZyConclusion>().eq(ZyConclusion::getCustomerRegId, customerRegId).last("limit 1"));
        reportBean.setZyConclusion(zyConclusion);
        //10.2 职业检明细结论
        List<ZyConclusionDetail> zyConclusionList = zyConclusionDetailMapper.listByReg(customerRegId);
        reportBean.setZyConclusionDetailList(zyConclusionList);

        //11.groupResultList，统计组合的检查状态
        List<String> checked = new ArrayList<>();
        List<String> unchecked = new ArrayList<>();
        List<String> abandoned = new ArrayList<>();
        groupList.forEach(customerRegItemGroup -> {
            if (StringUtils.equals(customerRegItemGroup.getCheckStatus(), ExConstants.CHECK_STATUS_已检)) {
                checked.add(customerRegItemGroup.getItemGroupName());
            } else if (StringUtils.equals(customerRegItemGroup.getCheckStatus(), ExConstants.CHECK_STATUS_未检)) {
                unchecked.add(customerRegItemGroup.getItemGroupName());
            } else if (customerRegItemGroup.getAbandonFlag() != null && customerRegItemGroup.getAbandonFlag() == 1) {
                abandoned.add(customerRegItemGroup.getItemGroupName());
            }
        });
        CheckStat checkStat = new CheckStat();
        checkStat.setChecked(checked);
        checkStat.setUnchecked(unchecked);
        checkStat.setAbandoned(abandoned);
        reportBean.setCheckStat(checkStat);

        //根据classCode分组groupList
        Map<String, List<CustomerRegItemGroup>> groupListByClassCode = groupList.stream().filter(g -> StringUtils.isNotBlank(g.getClassCode())).collect(Collectors.groupingBy(CustomerRegItemGroup::getClassCode));
        reportBean.setGroupByClassCodeMap(groupListByClassCode);
        //统计项目增减情况
        List<String> addItems = new ArrayList<>();
        List<String> minusItems = new ArrayList<>();
        groupList.forEach(customerRegItemGroup -> {
            if (Objects.equals(customerRegItemGroup.getAddMinusFlag(), 1)) {
                addItems.add(customerRegItemGroup.getItemGroupName());
            } else if (Objects.equals(customerRegItemGroup.getAddMinusFlag(), -1)) {
                minusItems.add(customerRegItemGroup.getItemGroupName());
            }
        });
        AddMinusStat addMinusStat = new AddMinusStat();
        addMinusStat.setAddItems(addItems);
        addMinusStat.setMinusItems(minusItems);
        checkStat.setAbandoned(abandoned);
        reportBean.setAddMinusStat(addMinusStat);
        try {
            //获取职业病问卷
            ZyInquiry inquiry = zyInquiryService.getInquiryByRegId(customerRegId);
            reportBean.setZyInquiry(inquiry);
        } catch (Exception e) {
            log.error("获取职业病问卷失败");
        }
        long end = System.currentTimeMillis();
        log.info("生成报告耗时：" + (end - start) + "ms");
        return reportBean;
    }

    @Override
    public Report4SmallApp getReport4SmallApp(String customerId) {
        //1、获取体检登记信息
        List<CustomerReg> regs = customerRegMapper.selectList(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getCustomerId, customerId).orderByDesc(CustomerReg::getExamNo));
        Validate.notEmpty(regs, "未查询到相关登记信息");
        CustomerReg customerReg = regs.get(0);
        String customerRegId = customerReg.getId();
      /*  CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            return null;
        }*/
        customerReg.setRegDate(DateUtil.formatDate(customerReg.getCreateTime()));
        String companyRegId = customerReg.getCompanyRegId();
        if (StringUtils.isNotBlank(companyRegId)) {
            CompanyReg companyReg = companyRegMapper.selectById(companyRegId);
            customerReg.setCompanyReg(companyRegMapper.selectById(companyRegId));
            if (companyReg != null && StringUtils.equals(companyReg.getCnameDisplayInReport(), "0")) {
                customerReg.setCompanyName("");
            }
        }

        //2、获取总检信息
        CustomerRegSummary customerRegSummary = customerRegSummaryMapper.getByRegId(customerRegId);
        if (customerRegSummary != null && StringUtils.equals(customerRegSummary.getStatus(), "审核通过")) {
            Report4SmallApp report4SmallApp = new Report4SmallApp();
            report4SmallApp.setCustomerReg(customerReg);
            String creatorSignPic = systemUserUtilService.getSignPicByUsername(customerRegSummary.getCreateBy());
            String auditorSignPic = systemUserUtilService.getSignPicByUsername(customerRegSummary.getAuditeBy());
            String preAuditorSignPic = systemUserUtilService.getSignPicByUsername(customerRegSummary.getPreAuditBy());
            customerRegSummary.setCreatorSignPic(creatorSignPic);
            customerRegSummary.setAuditorSignPic(auditorSignPic);
            customerRegSummary.setPreAuditorSignPic(preAuditorSignPic);
            if (customerRegSummary.getConfirmTime() != null) {
                customerRegSummary.setAuditDate(DateUtil.format(customerRegSummary.getConfirmTime(), "yyyy-MM-dd"));
            }
            //拼接建议
            List<AdviceBean> summaryJson = JSONObject.parseArray(JSON.toJSONString(customerRegSummary.getSummaryJson()), AdviceBean.class);
            if (CollectionUtils.isNotEmpty(summaryJson)) {
                List<String> advices = summaryJson.stream().map(i -> {
//                    AdviceBean adviceBean = JSON.parseObject(JSON.toJSONString(i), AdviceBean.class);
                    return i.getContent();
                }).collect(Collectors.toList());
                List<String> adviceList = Lists.newArrayList();

                for (int i = 1; i <= advices.size(); i++) {
                    adviceList.add(i + "、" + advices.get(i - 1));
                }

                customerRegSummary.setAdviceSummary(adviceList.stream().collect(Collectors.joining("\n")));
            }
        /*    //异常个数
            String[] split = StringUtils.split(customerRegSummary.getCharacterSummary(), "\n");
            customerRegSummary.setAbnormalCount(split.length);*/


            report4SmallApp.setSummaryAdvice(customerRegSummary);
            //4、获取小项结果
            List<CustomerRegItemResult> resultList = customerRegItemResultService.listByRegId(customerRegId);

            //将resultList按照itemGroupId进行分组
            Map<String, List<CustomerRegItemResult>> groupedByGroupIdResults = resultList.stream().collect(Collectors.groupingBy(CustomerRegItemResult::getItemGroupId));

            //5、获取大项结果

            List<CustomerRegItemGroup> groupList = customerRegItemGroupService.listWithItemGroupByReg(customerRegId, null, false);
            //计算大项异常个数
            Long abnormalCount = groupList.stream().filter(g -> StringUtils.equals(g.getAbnormalFlag(), "1")).count();
            customerRegSummary.setAbnormalCount(abnormalCount.intValue());
            //获取报告图片
            String openFileUrl = sysSettingService.getValueByCode("open_file_url");
            groupList.forEach(group -> {
                List<TextBean> reportPicBeanList = new ArrayList<>();
                if (group.getReportPics() != null && !group.getReportPics().isEmpty()) {
                    for (int i = 0; i < group.getReportPics().size(); i++) {
                        TextBean textBean = new TextBean();
                        textBean.setSeq(String.valueOf(i + 1));
                        textBean.setText(FileUrlUtils.replaceUrl(group.getReportPics().get(i), openFileUrl));
                        reportPicBeanList.add(textBean);
                    }
                }
                group.setReportPicBeanList(reportPicBeanList);

            });
            //过滤掉已退费和已减项的大项
            groupList = groupList.stream().filter(group -> group.getAddMinusFlag() != -1 && !StringUtils.equals(group.getPayStatus(), ExConstants.REFUND_STATE_退款成功)).toList();
            //为groupList设置itemResultList
            groupList.forEach(group -> {
                List<CustomerRegItemResult> itemResultList = groupedByGroupIdResults.get(group.getItemGroupId());
                group.setResultList(Objects.requireNonNullElseGet(itemResultList, ArrayList::new));
                Date checkTime = group.getCheckTime();
                if (checkTime != null) {
                    group.setCheckDate(DateUtil.format(checkTime, "yyyy-MM-dd"));
                }
            });
            report4SmallApp.setGroupList(groupList);

            return report4SmallApp;
        }
        return null;
    }

    @Override
    public Report4SmallApp getReport4SmallAppByRegId(String customerRegId) throws Exception {

        //1、获取体检登记信息
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            throw new Exception("未查询到相关体检记录");
        }

        customerReg.setRegDate(DateUtil.formatDate(customerReg.getRegTime()));

        //2、获取总检信息
        CustomerRegSummary customerRegSummary = customerRegSummaryMapper.getByRegId(customerRegId);
        if (customerRegSummary == null) {
            throw new Exception("未查询到相关总检信息");
        }
        if (!StringUtils.equals(customerRegSummary.getStatus(), "审核通过")) {
            throw new Exception("总检信息正在审核中，请稍后查看");
        }
        Report4SmallApp report4SmallApp = new Report4SmallApp();
        report4SmallApp.setCustomerId(customerReg.getCustomerId());
        report4SmallApp.setCustomerRegId(customerRegId);
        report4SmallApp.setName(customerReg.getName());
        report4SmallApp.setReportTimeStr(customerReg.getSummaryAuditTime());

        report4SmallApp.setCustomerReg(customerReg);
        if (customerRegSummary.getConfirmTime() != null) {
            customerRegSummary.setAuditDate(DateUtil.format(customerRegSummary.getConfirmTime(), "yyyy-MM-dd"));
        }

        report4SmallApp.setSummaryAdvice(customerRegSummary);
        //4、获取小项结果
        List<CustomerRegItemResult> resultList = customerRegItemResultService.listByRegId(customerRegId);

        //统计小项异常个数
        long abnormalItemCount = resultList.stream().filter(r -> StringUtils.equals(r.getAbnormalFlag(), "1")).count();
        report4SmallApp.setAbnormalItemCount((int) abnormalItemCount);

        //将resultList按照itemGroupId进行分组
        Map<String, List<CustomerRegItemResult>> groupedByGroupIdResults = resultList.stream().collect(Collectors.groupingBy(CustomerRegItemResult::getItemGroupId));

        //5、获取大项结果
        List<CustomerRegItemGroup> groupList = customerRegItemGroupService.listWithItemGroupByReg(customerRegId, null, false);
        //计算大项异常个数
        Long abnormalGroupCount = groupList.stream().filter(g -> StringUtils.equals(g.getAbnormalFlag(), "1")).count();
        customerRegSummary.setAbnormalCount(abnormalGroupCount.intValue());
        report4SmallApp.setAbnormalGroupCount(abnormalGroupCount.intValue());
        //获取报告图片
        String openFileUrl = sysSettingService.getValueByCode("open_file_url");
        groupList.forEach(group -> {
            List<TextBean> reportPicBeanList = new ArrayList<>();
            if (group.getReportPics() != null && !group.getReportPics().isEmpty()) {
                for (int i = 0; i < group.getReportPics().size(); i++) {
                    TextBean textBean = new TextBean();
                    textBean.setSeq(String.valueOf(i + 1));
                    textBean.setText(FileUrlUtils.replaceUrl(group.getReportPics().get(i), openFileUrl));
                    reportPicBeanList.add(textBean);
                }
            }
            group.setReportPicBeanList(reportPicBeanList);
        });
        //过滤掉已退费和已减项的大项
        groupList = groupList.stream().filter(group -> group.getAddMinusFlag() != -1 && !StringUtils.equals(group.getPayStatus(), ExConstants.REFUND_STATE_退款成功)).toList();
        //为groupList设置itemResultList
        groupList.forEach(group -> {
            List<CustomerRegItemResult> itemResultList = groupedByGroupIdResults.get(group.getItemGroupId());
            group.setResultList(Objects.requireNonNullElseGet(itemResultList, ArrayList::new));
            Date checkTime = group.getCheckTime();
            if (checkTime != null) {
                group.setCheckDate(DateUtil.format(checkTime, "yyyy-MM-dd"));
            }
        });
        report4SmallApp.setGroupList(groupList);

        return report4SmallApp;
    }


    @Override
    public List<Report4SmallApp> getReportsByOpenId(String openId) {
        CustomerAccount account = customerAccountService.getOne(new LambdaQueryWrapper<CustomerAccount>().eq(CustomerAccount::getOpenId, openId));
        Validate.notNull(account, "用户尚未注册，请注册后操作！");
        List<Customer> customers = customerAccountService.getCustomersByAccountId(account.getId(), null);

        List<Report4SmallApp> reports = Lists.newArrayList();
        for (Customer customer : customers) {
            List<CustomerReg> regs = customerRegMapper.selectList(new LambdaQueryWrapper<CustomerReg>().eq(CustomerReg::getCustomerId, customer.getId()).orderByDesc(CustomerReg::getRegTime));
//            Validate.notEmpty(regs, "未查询到相关登记信息");
            if (CollectionUtils.isEmpty(regs)) {
                continue;
            }
            CustomerReg customerReg = regs.get(0);

            List<CustomerRegItemGroup> groupList = customerRegItemGroupService.listWithItemGroupByReg(customerReg.getId(), null, false);
            //计算大项异常个数
            Long abnormalCount = groupList.stream().filter(g -> StringUtils.equals(g.getAbnormalFlag(), "1")).count();
            //2、获取总检信息
            CustomerRegSummary customerRegSummary = customerRegSummaryMapper.getByRegId(customerReg.getId());
            if (customerRegSummary != null && StringUtils.equals(customerRegSummary.getStatus(), "审核通过")) {
                Report4SmallApp report = new Report4SmallApp();
                report.setCustomerRegId(customerReg.getId());
                report.setCustomerId(customerReg.getCustomerId());
                report.setName(customerReg.getName());
                //异常个数
                report.setAbnormalGroupCount(abnormalCount.intValue());
                report.setRelationType(customer.getRelationType());

                OrgInfo defultOrgInfo = orgInfoService.getDefultOrgInfo();
                report.setExamOrg(defultOrgInfo.getOrgName() + "体检中心");
                if (customerRegSummary.getConfirmTime() != null) {
                    report.setReportTime(customerRegSummary.getConfirmTime());
                }
                reports.add(report);
            }
        }
        return reports;
    }

    @Override
    public CustomerReg getEReportByRegId(String customerRegId/*, HttpServletResponse response*/) {
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (Objects.nonNull(customerReg)) {
            String openFileUrl = sysSettingService.getValueByCode("open_file_url");
            customerReg.setEReportUrl(FileUrlUtils.replaceUrl(customerReg.getEReportUrl(), openFileUrl));
        }

        return customerReg;
    }

    @Override
    public String getEReportUrlByRegId(String customerRegId) throws Exception {

        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (Objects.isNull(customerReg)) {
            throw new Exception("未查询到相关体检记录");
        }
        if (!(StringUtils.equals(customerReg.getEReportStatus(), "待发送") || StringUtils.equals(customerReg.getEReportStatus(), "已发送"))) {
            throw new Exception("电子报告尚未生成，请稍后查看");
        }
        if (StringUtils.isBlank(customerReg.getEReportUrl())) {
            throw new Exception("电子报告尚未生成，请稍后查看");
        }
        String openFileUrl = sysSettingService.getValueByCode("open_file_url");

        return FileUrlUtils.replaceUrl(customerReg.getEReportUrl(), openFileUrl);
    }

}
