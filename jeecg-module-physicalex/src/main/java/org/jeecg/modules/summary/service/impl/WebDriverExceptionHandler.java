package org.jeecg.modules.summary.service.impl;

import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebDriverException;
import org.openqa.selenium.chrome.ChromeDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * WebDriver异常处理工具类
 * 专门处理WebDriver相关的异常和进程清理
 * 
 * <AUTHOR>
 * @since 2024-08-07
 */
@Component
public class WebDriverExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(WebDriverExceptionHandler.class);
    
    // Chrome进程PID提取正则表达式
    private static final Pattern CHROME_PID_PATTERN = Pattern.compile("chrome.*?--user-data-dir.*?(\\d+)");
    
    /**
     * 安全关闭WebDriver实例
     * 包含多重保护机制确保进程被完全清理
     */
    public boolean safeQuitWebDriver(WebDriver driver) {
        if (driver == null) {
            return true;
        }
        
        String sessionId = null;
        String processInfo = null;
        
        try {
            // 获取会话信息用于日志记录
            if (driver instanceof ChromeDriver) {
                ChromeDriver chromeDriver = (ChromeDriver) driver;
                sessionId = chromeDriver.getSessionId() != null ? chromeDriver.getSessionId().toString() : "unknown";
            }
            
            // 尝试获取进程信息
            processInfo = getDriverProcessInfo(driver);
            
            log.debug("开始关闭WebDriver实例，会话ID: {}, 进程信息: {}", sessionId, processInfo);
            
            // 第一步：正常关闭
            driver.quit();
            
            // 等待一段时间让进程正常退出
            Thread.sleep(1000);
            
            // 第二步：验证进程是否已关闭
            if (isDriverProcessStillRunning(processInfo)) {
                log.warn("WebDriver进程未正常退出，尝试强制终止");
                forceKillDriverProcess(processInfo);
                
                // 再次等待和验证
                Thread.sleep(2000);
                if (isDriverProcessStillRunning(processInfo)) {
                    log.error("强制终止WebDriver进程失败，进程可能成为僵尸进程");
                    return false;
                }
            }
            
            log.debug("WebDriver实例已成功关闭");
            return true;
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("关闭WebDriver时被中断", e);
            return false;
        } catch (Exception e) {
            log.warn("关闭WebDriver时发生异常，会话ID: {}", sessionId, e);
            
            // 异常情况下尝试强制清理
            try {
                forceKillDriverProcess(processInfo);
                return true;
            } catch (Exception ex) {
                log.error("强制清理WebDriver进程失败", ex);
                return false;
            }
        }
    }
    
    /**
     * 获取WebDriver进程信息
     */
    private String getDriverProcessInfo(WebDriver driver) {
        try {
            if (driver instanceof ChromeDriver) {
                ChromeDriver chromeDriver = (ChromeDriver) driver;
                // 尝试通过反射获取进程信息
                // 这里简化处理，实际可以通过更复杂的方式获取PID
                return "chrome_session_" + chromeDriver.getSessionId();
            }
        } catch (Exception e) {
            log.debug("获取WebDriver进程信息失败", e);
        }
        return null;
    }
    
    /**
     * 检查WebDriver进程是否仍在运行
     */
    private boolean isDriverProcessStillRunning(String processInfo) {
        if (processInfo == null) {
            return false;
        }
        
        try {
            String os = System.getProperty("os.name").toLowerCase();
            String command;
            
            if (os.contains("win")) {
                command = "tasklist /FI \"IMAGENAME eq chrome.exe\" /FO CSV";
            } else {
                command = "ps aux | grep chrome";
            }
            
            Process process = Runtime.getRuntime().exec(command);
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains(processInfo) || (processInfo.contains("chrome") && line.toLowerCase().contains("chrome"))) {
                    reader.close();
                    process.destroy();
                    return true;
                }
            }
            
            reader.close();
            process.waitFor(5, TimeUnit.SECONDS);
            return false;
            
        } catch (Exception e) {
            log.debug("检查WebDriver进程状态时发生异常", e);
            return false;
        }
    }
    
    /**
     * 强制终止WebDriver进程
     */
    private void forceKillDriverProcess(String processInfo) {
        if (processInfo == null) {
            return;
        }
        
        try {
            String os = System.getProperty("os.name").toLowerCase();
            
            if (os.contains("win")) {
                // Windows: 终止所有chrome.exe进程
                Runtime.getRuntime().exec("taskkill /F /IM chrome.exe");
            } else {
                // Linux/Unix: 终止chrome进程
                Runtime.getRuntime().exec("pkill -f chrome");
            }
            
            log.info("已发送强制终止Chrome进程命令");
            
        } catch (Exception e) {
            log.error("强制终止WebDriver进程失败", e);
        }
    }
    
    /**
     * 分析WebDriver异常类型并提供处理建议
     */
    public WebDriverExceptionType analyzeException(Exception exception) {
        if (exception == null) {
            return WebDriverExceptionType.UNKNOWN;
        }
        
        String message = exception.getMessage();
        if (message == null) {
            message = exception.getClass().getSimpleName();
        }
        
        message = message.toLowerCase();
        
        // 会话相关异常
        if (message.contains("no such session") || message.contains("session not found")) {
            return WebDriverExceptionType.SESSION_LOST;
        }
        
        // 连接相关异常
        if (message.contains("connection refused") || message.contains("connection reset") || 
            message.contains("socket") || message.contains("connection")) {
            return WebDriverExceptionType.CONNECTION_ERROR;
        }
        
        // 超时异常
        if (message.contains("timeout") || message.contains("timed out")) {
            return WebDriverExceptionType.TIMEOUT;
        }
        
        // 内存相关异常
        if (message.contains("out of memory") || message.contains("memory") || 
            message.contains("heap")) {
            return WebDriverExceptionType.MEMORY_ERROR;
        }
        
        // 进程相关异常
        if (message.contains("process") || message.contains("chrome") || 
            message.contains("driver")) {
            return WebDriverExceptionType.PROCESS_ERROR;
        }
        
        // JavaScript执行异常
        if (message.contains("javascript") || message.contains("script")) {
            return WebDriverExceptionType.SCRIPT_ERROR;
        }
        
        return WebDriverExceptionType.UNKNOWN;
    }
    
    /**
     * 根据异常类型获取处理策略
     */
    public ExceptionHandlingStrategy getHandlingStrategy(WebDriverExceptionType exceptionType) {
        switch (exceptionType) {
            case SESSION_LOST:
            case PROCESS_ERROR:
                return ExceptionHandlingStrategy.RECREATE_DRIVER;
                
            case CONNECTION_ERROR:
            case TIMEOUT:
                return ExceptionHandlingStrategy.RETRY_WITH_NEW_DRIVER;
                
            case MEMORY_ERROR:
                return ExceptionHandlingStrategy.FORCE_CLEANUP_AND_RECREATE;
                
            case SCRIPT_ERROR:
                return ExceptionHandlingStrategy.RETRY_SAME_DRIVER;
                
            default:
                return ExceptionHandlingStrategy.RECREATE_DRIVER;
        }
    }
    
    /**
     * WebDriver异常类型枚举
     */
    public enum WebDriverExceptionType {
        SESSION_LOST,       // 会话丢失
        CONNECTION_ERROR,   // 连接错误
        TIMEOUT,           // 超时
        MEMORY_ERROR,      // 内存错误
        PROCESS_ERROR,     // 进程错误
        SCRIPT_ERROR,      // 脚本执行错误
        UNKNOWN            // 未知错误
    }
    
    /**
     * 异常处理策略枚举
     */
    public enum ExceptionHandlingStrategy {
        RETRY_SAME_DRIVER,           // 使用相同driver重试
        RETRY_WITH_NEW_DRIVER,       // 使用新driver重试
        RECREATE_DRIVER,             // 重新创建driver
        FORCE_CLEANUP_AND_RECREATE   // 强制清理并重新创建
    }
    
    /**
     * 执行异常恢复操作
     */
    public boolean executeRecoveryAction(WebDriver driver, ExceptionHandlingStrategy strategy, 
                                       WebDriverPoolManager poolManager) {
        try {
            switch (strategy) {
                case RETRY_SAME_DRIVER:
                    // 简单重试，不做特殊处理
                    return true;
                    
                case RETRY_WITH_NEW_DRIVER:
                case RECREATE_DRIVER:
                    // 安全关闭当前driver
                    safeQuitWebDriver(driver);
                    return true;
                    
                case FORCE_CLEANUP_AND_RECREATE:
                    // 强制清理所有Chrome进程
                    safeQuitWebDriver(driver);
                    poolManager.forceCleanupAllChromeProcesses();
                    return true;
                    
                default:
                    return false;
            }
        } catch (Exception e) {
            log.error("执行异常恢复操作失败", e);
            return false;
        }
    }
}
