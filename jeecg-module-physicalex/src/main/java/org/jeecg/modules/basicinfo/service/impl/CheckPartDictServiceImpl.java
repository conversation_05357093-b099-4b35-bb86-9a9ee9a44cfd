package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.basicinfo.entity.CheckPartDict;
import org.jeecg.modules.basicinfo.entity.CheckPartUsageStat;
import org.jeecg.modules.basicinfo.mapper.CheckPartDictMapper;
import org.jeecg.modules.basicinfo.service.ICheckPartDictService;
import org.jeecg.modules.basicinfo.service.ICheckPartUsageStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 检查部位字典
 * @Author: jeecg-boot
 * @Date: 2024-12-19
 * @Version: V1.0
 */
@Service
@Slf4j
public class CheckPartDictServiceImpl extends ServiceImpl<CheckPartDictMapper, CheckPartDict> implements ICheckPartDictService {

    @Autowired
    private ICheckPartUsageStatService checkPartUsageStatService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_KEY_PREFIX = "check_part:";
    private static final String FREQUENCY_CACHE_PREFIX = "check_part_freq:";
    private static final int CACHE_EXPIRE_HOURS = 24;

    @Override
    //@Cacheable(value = "checkPartByGroup", key = "#itemGroupId + ':' + (#keyword != null ? #keyword : 'all')")
    public List<CheckPartDict> listByItemGroupWithFrequency(String itemGroupId, String keyword) {
        // 1. 获取基础部位数据
        List<CheckPartDict> parts = getCheckPartsFromCache(keyword);
        
        // 2. 获取使用频次数据
        Map<String, Long> frequencyMap = getFrequencyMap(itemGroupId);
        
        // 3. 设置频次并排序
        parts.forEach(part -> {
            part.setFrequency(frequencyMap.getOrDefault(part.getId(), 0L));
        });

        // 4. 按频次降序，频次相同按名称升序
        return parts.stream()
            .sorted(Comparator.comparing(CheckPartDict::getFrequency).reversed()
                .thenComparing(CheckPartDict::getName))
            .collect(Collectors.toList());
    }

    @Override
    public List<CheckPartDict> searchByKeyword(String keyword) {
        LambdaQueryWrapper<CheckPartDict> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CheckPartDict::getEnableFlag, "1");
        
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like(CheckPartDict::getName, keyword)
                .or()
                .like(CheckPartDict::getHelpChar, keyword)
                .or()
                .like(CheckPartDict::getCode, keyword)
            );
        }

        queryWrapper.orderByAsc(CheckPartDict::getSortOrder)
                   .orderByAsc(CheckPartDict::getName);
        
        return list(queryWrapper);
    }

    @Override
    @Async
    public void updateUsageFrequency(String itemGroupId, List<String> checkPartIds) {
        try {
            // 异步更新数据库统计
            checkPartIds.forEach(partId -> {
                checkPartUsageStatService.incrementUsage(itemGroupId, partId);
            });
            
            // 清除相关缓存
            String cacheKey = FREQUENCY_CACHE_PREFIX + itemGroupId;
            redisTemplate.delete(cacheKey);
            
            log.info("Updated usage frequency for itemGroup: {}, parts: {}", itemGroupId, checkPartIds);
        } catch (Exception e) {
            log.error("Failed to update usage frequency for itemGroup: {}, parts: {}", itemGroupId, checkPartIds, e);
        }
    }

    /**
     * 从缓存获取基础部位数据
     */
    private List<CheckPartDict> getCheckPartsFromCache(String keyword) {
        String cacheKey = CACHE_KEY_PREFIX + (keyword != null ? keyword : "all");
        
        @SuppressWarnings("unchecked")
        List<CheckPartDict> cachedParts = (List<CheckPartDict>) redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedParts != null) {
            return cachedParts;
        }
        
        // 缓存未命中，从数据库查询
        List<CheckPartDict> parts = searchByKeyword(keyword);
        
        // 存入缓存
        redisTemplate.opsForValue().set(cacheKey, parts, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
        
        return parts;
    }

    /**
     * 获取使用频次映射
     */
    private Map<String, Long> getFrequencyMap(String itemGroupId) {
        String cacheKey = FREQUENCY_CACHE_PREFIX + itemGroupId;
        
        @SuppressWarnings("unchecked")
        Map<String, Long> cachedFrequency = (Map<String, Long>) redisTemplate.opsForValue().get(cacheKey);
        
        if (cachedFrequency != null) {
            return cachedFrequency;
        }
        
        // 缓存未命中，从数据库查询
        List<CheckPartUsageStat> stats = checkPartUsageStatService.listByItemGroupId(itemGroupId);
        Map<String, Long> frequencyMap = stats.stream()
            .collect(Collectors.toMap(
                CheckPartUsageStat::getCheckPartId,
                CheckPartUsageStat::getUsageCount,
                (existing, replacement) -> existing
            ));
        
        // 存入缓存
        redisTemplate.opsForValue().set(cacheKey, frequencyMap, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
        
        return frequencyMap;
    }
}


