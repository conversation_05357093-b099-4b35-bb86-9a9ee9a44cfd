package org.jeecg.modules.basicinfo.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.bo.ItemGroupLabel;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemGroupInterface;
import org.jeecg.modules.basicinfo.entity.ItemInfo;

import java.util.List;

/**
 * @Description: 项目组合
 * @Author: jeecg-boot
 * @Date: 2024-01-31
 * @Version: V1.0
 */
public interface IItemGroupService extends IService<ItemGroup> {

    void saveOrderOfGroup(String groupId, List<JSONObject> items);

    List<ItemGroup> listByKeyword(String keyword);

    void removeForever(List<String> ids);

    void batchRecover(List<String> ids);

    Page<ItemGroup> pageItemGroup(Page<ItemGroup> page,String name,String helpChar,String feeType, String keyword, String departmentId,String enableFlag,String chargeItemOnlyFlag);

    Page<ItemGroup> pageRecycleBinItemGroup(Page<ItemGroup> page, String keyword, String departmentId);

    List<ItemInfo> listItemByGroupId(String groupId);

    void setItemOfGroup(String groupId, JSONArray items);

    List<ItemGroup> listAll(String departmentId, String name);

    List<ItemGroupLabel> listAllByDepartment(String name, String answerId,String suitId);

    void setDepartmentNameAndCheckPartName(ItemGroup itemGroup);

    Integer addBathByHisItems(List<ItemGroupInterface> itemGroupInterfaces);

    void evictCached();

    void batchUpdateEnableFlag(List<String> idList,String enableFlag);

    List<ItemGroup> listByRiskFactor(List<String> riskFactorCodes,String post);

    String jionAllGroup2String();

    void evictCachedGroupString();

    List<ItemGroup> listItemGroupByNames(List<String> names);

    void updateItemGroup(ItemGroup itemGroup);

    List<ItemGroup> listAll();

    boolean checkGroupCacheFlag();

    ItemGroup getItemGroupById(String groupId);

    List<ItemGroup> getItemGroupByHisCode(String hisCode);

}
