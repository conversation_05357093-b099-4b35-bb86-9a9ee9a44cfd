<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.ItemInfoMapper">

    <select id="listItemByDepartId" resultType="org.jeecg.modules.basicinfo.entity.ItemInfo"
            parameterType="java.lang.String">
        select * from item_info where department_id=#{departId} and enable_flag=1 and del_flag=0
    </select>

    <select id="listItemByGroupId" resultType="org.jeecg.modules.basicinfo.entity.ItemInfo"
            parameterType="java.lang.String">
        select i.* from item_info i join itemgroup_item igi on i.id = igi.item_id
        where igi.group_id = #{groupId} and i.enable_flag=1 and i.del_flag=0
        <if test="sumableFlag != null"> and i.sumableFlag = #{sumableFlag}</if>
    </select>
    <select id="listByKeyword" resultType="org.jeecg.modules.basicinfo.entity.ItemInfo">
        select * from item_info where enable_flag=1 and del_flag=0 <if test="keyword!=null and keyword!=''">and name like concat('%',#{keyword},'%') or help_char like concat('%',#{keyword},'%') or his_code like concat('%',#{keyword},'%')</if>
        <if test="departmentId != null"> and department_id=#{departmentId}</if>
    </select>
    <select id="listItemByGroupIds" resultType="org.jeecg.modules.basicinfo.entity.ItemInfo">
        select i.*,igi.group_id as groupId from item_info i join itemgroup_item igi on i.id = igi.item_id
        <where>
             i.enable_flag=1
             and i.del_flag=0
             <if test="groupIds!=null and groupIds.size()>0">
                and igi.group_id in
                 <foreach collection="groupIds" item="item" open="(" close=")" separator=",">
                     #{item}
                 </foreach>

             </if>


        </where>
    </select>

    <select id="getItemByGroupId" resultType="org.jeecg.modules.basicinfo.entity.ItemInfo"
            parameterType="java.lang.String">
        select i.* from item_info i join itemgroup_item igi on i.id = igi.item_id
        where igi.group_id = #{groupId} and i.enable_flag=1 and i.del_flag=0
        order by i.sort, i.name
    </select>

</mapper>