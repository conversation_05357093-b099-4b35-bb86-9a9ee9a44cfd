package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.modules.basicinfo.entity.SysUserInterface;
import org.jeecg.modules.basicinfo.mapper.SysUserInterfaceMapper;
import org.jeecg.modules.basicinfo.service.ISysUserInterfaceService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Objects;

/**
 * @Description: sys_user_interface
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
@Service
public class SysUserInterfaceServiceImpl extends ServiceImpl<SysUserInterfaceMapper, SysUserInterface> implements ISysUserInterfaceService {


    @Override
    public String getAdminDeptCodeById(String id) {
        SysUserInterface userInterface = getById(id);
        if (Objects.nonNull(userInterface)){
           return userInterface.getAdminDeptCode();
        }
        return null;
    }

    @Override
    public String getEmployeNoByNameAndAdminDeptCode(String name, String adminDeptCode) {
        SysUserInterface userInterface = this.getOne(new LambdaQueryWrapper<SysUserInterface>().eq(SysUserInterface::getStaffName, name)
                .eq(SysUserInterface::getAdminDeptCode, adminDeptCode).last("limit 1"));
        if (Objects.nonNull(userInterface)){
            return userInterface.getEmployeNo();
        }
        return null;
    }
}
