<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.ItemSuitMapper">

    <select id="getItemGroupOfSuit" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup"
            parameterType="java.lang.String">

    </select>
    <select id="getSuitGroup" resultType="org.jeecg.modules.basicinfo.entity.SuitGroup"
            parameterType="java.lang.String">
        select sg.*, ig.has_check_part
        from suit_group sg
        left join item_group ig on sg.group_id = ig.id
        where sg.suit_id = #{suitId}
        order by sg.id
    </select>
    <select id="getGroupOfSuit" resultType="org.jeecg.modules.basicinfo.entity.ItemGroup"
            parameterType="java.lang.String">
        select ig.*,
               sg.price_after_dis as price_after_dis_of_suit,
               sg.min_discount_rate as min_discount_rate_of_suit,
               sg.price_dis_diff_amount as price_dis_diff_amount_of_suit,
               sg.dis_rate as dis_rate_of_suit,
               sg.check_part_id,
               sg.check_part_name,
               sg.check_part_code
        from suit_group sg
        join item_group ig on sg.group_id = ig.id
         <where>
             <if test="suitId !=null and suitId!=''">
                 sg.suit_id = #{suitId}
             </if>
         </where>
         order by sg.id
    </select>
    <select id="getSysSuitGroupIds" resultType="java.lang.String">
        SELECT g.group_id FROM item_suit s join suit_group g on s.id=g.suit_id where s.suit_type='系统套餐'
    </select>
    <select id="getSuitByKeyword" resultType="org.jeecg.modules.basicinfo.entity.ItemSuit">
        select * from item_suit
        <where>
            del_flag = '0' and enable_flag = '1'
                <if test="keyword!=null and keyword!=''">AND (name like concat('%',#{keyword},'%') or help_char like
                    concat('%',#{keyword},'%'))
                </if>
        </where>
        limit 50
    </select>
    <select id="getDeletedSuitByKeyword" resultType="org.jeecg.modules.basicinfo.entity.ItemSuit">
        select * from item_suit
        <where>
            <if test="delFlag != null and delFlag !=''">and del_flag=#{delFlag}</if>
            <if test="keyword!=null and keyword!=''">AND (name like concat('%',#{keyword},'%') or help_char like
                concat('%',#{keyword},'%'))
            </if>
        </where>
        order by update_time desc
    </select>
    <select id="pageSuit4H5" resultType="org.jeecg.modules.basicinfo.entity.ItemSuit">
        select * from item_suit where del_flag = '0' and enable_flag = '1' and pub_available='1' <if
            test="categoryId!=null and categoryId!=''">AND category_id=#{categoryId}</if> <if test="gender!=null"> and sex_limit=#{gender}</if>
                                <if test="priceStart!=null"> and price &gt;= #{priceStart}</if> <if test="priceEnd!=null"> and price &lt;= #{priceEnd}</if>
        order by sort
    </select>
</mapper>