package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.SysUserInterface;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * @Description: sys_user_interface
 * @Author: jeecg-boot
 * @Date:   2024-07-12
 * @Version: V1.0
 */
public interface ISysUserInterfaceService extends IService<SysUserInterface> {

    String getAdminDeptCodeById(String id );

    String getEmployeNoByNameAndAdminDeptCode(String name,String adminDeptCode);

}
