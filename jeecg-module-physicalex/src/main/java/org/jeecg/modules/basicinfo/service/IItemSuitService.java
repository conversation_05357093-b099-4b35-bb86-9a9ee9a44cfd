package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemSuit;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.SuitGroup;

import java.util.List;

/**
 * @Description: 体检套餐
 * @Author: jeecg-boot
 * @Date: 2024-02-03
 * @Version: V1.0
 */
public interface IItemSuitService extends IService<ItemSuit> {

    Integer getNextSort();

    List<SuitGroup> getGroupOfSuit(String suitId, Boolean withGroup);

    List<ItemGroup> getItemGroupDiffSuit(List<SuitGroup> suitGroupList);

    void updateSuitGroup(String suitId, List<SuitGroup> suitGroupList);

    List<ItemGroup> getGroupOfSuit(String suitId);

    List<ItemSuit> listByKeyword(String keyword);

    void evcitCache();

    List<ItemGroup> getGroupsBySuitId(String suitId);

    void removeForever(List<String> ids);

    void batchRecover(List<String> ids);

    Page<ItemSuit> pageRecycleBinItemGroup(Page<ItemSuit> page, String keyword);

    void pageSuit4H5(Page<ItemSuit> page, String categoryId, String priceRange, String gender);

    void batchUpdateEnableFlag(List<String> idList, String enableFlag);

    /**
     * 添加项目到套餐（含附属项目和赠送项目处理）
     * @param suitId 套餐ID
     * @param itemGroups 要添加的项目列表
     */
    void addItemGroupsToSuit(String suitId, List<SuitGroup> itemGroups);

    /**
     * 添加项目到套餐（可控制是否处理附属项目和赠送项目）
     * @param suitId 套餐ID
     * @param itemGroups 要添加的项目列表
     * @param processRelatedItems 是否处理附属项目和赠送项目
     */
    void addItemGroupsToSuit(String suitId, List<SuitGroup> itemGroups, Boolean processRelatedItems);

}
