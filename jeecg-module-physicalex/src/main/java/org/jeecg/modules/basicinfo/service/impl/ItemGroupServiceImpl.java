package org.jeecg.modules.basicinfo.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.basicinfo.bo.ItemGroupLabel;
import org.jeecg.modules.basicinfo.entity.*;
import org.jeecg.modules.basicinfo.mapper.*;
import org.jeecg.modules.basicinfo.service.IItemGroupService;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;
import org.jeecg.modules.reg.mapper.CompanyTeamItemGroupMapper;
import org.jeecg.modules.survey.service.IDiseaseSurveyAnswerService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 项目组合
 * @Author: jeecg-boot
 * @Date: 2024-01-31
 * @Version: V1.0
 */
@Service
@Slf4j
@CacheConfig(cacheNames = "basicinfo::itemGroup")
public class ItemGroupServiceImpl extends ServiceImpl<ItemGroupMapper, ItemGroup> implements IItemGroupService {

    @Autowired
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private IDiseaseSurveyAnswerService diseaseSurveyAnswerService;
    @Autowired
    private ItemSuitMapper itemSuitMapper;
    @Autowired
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Autowired
    private SuitGroupMapper suitGroupMapper;
    @Autowired
    private CompanyTeamItemGroupMapper companyTeamItemGroupMapper;
    @Autowired
    private BarcodeSettingGroupMapper barcodeSettingGroupMapper;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CheckPartDictMapper checkPartDictMapper;
    @Autowired
    private EquipTypeDictMapper equipTypeDictMapper;
    @Autowired
    private CheckMethodDictMapper checkMethodDictMapper;

    @Override
    public void saveOrderOfGroup(String groupId, List<JSONObject> items) {
        if (StringUtils.isBlank(groupId) || CollectionUtils.isEmpty(items)) {
            return;
        }

        //1、直接更新seq字段
        String sql = "update itemgroup_item set seq=? where group_id=? and item_id=?";
        jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                JSONObject item = items.get(i);
                ps.setInt(1, item.getInteger("seq"));
                ps.setString(2, groupId);
                ps.setString(3, item.getString("id"));
            }

            @Override
            public int getBatchSize() {
                return items.size();
            }
        });
    }

    @Cacheable(key = "#keyword", unless = "#result == null")
    @Override
    public List<ItemGroup> listByKeyword(String keyword) {
        return itemGroupMapper.listByKeyword(keyword);
    }

    @Override
    public void removeForever(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        jdbcTemplate.batchUpdate("delete from item_group where id=?", new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, ids.get(i));
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });

        jdbcTemplate.batchUpdate("delete from itemgroup_item where group_id=?", new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, ids.get(i));
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });
    }

    @Override
    public void batchRecover(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        jdbcTemplate.batchUpdate("update item_group set del_flag=0 where id=?", new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, ids.get(i));
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });
    }

    @Override
    public Page<ItemGroup> pageItemGroup(Page<ItemGroup> page, String name, String helpChar, String feeType, String keyword, String departmentId, String enableFlag, String chargeItemOnlyFlag) {

        return itemGroupMapper.pageItemGroup(page, name, helpChar, feeType, keyword, departmentId, "0", enableFlag, chargeItemOnlyFlag);
    }

    @Override
    public Page<ItemGroup> pageRecycleBinItemGroup(Page<ItemGroup> page, String keyword, String departmentId) {

        return itemGroupMapper.pageItemGroup(page, null, null, null, keyword, departmentId, "1", null, null);
    }

    @Override
    public List<ItemInfo> listItemByGroupId(String groupId) {
        return itemGroupMapper.listItemByGroupId(groupId);
    }

    @Override
    public void setItemOfGroup(String groupId, JSONArray items) {
        //1.先删除子表数据
        jdbcTemplate.update("delete from itemgroup_item where group_id=?", groupId);
        //2.使用jdbctemplate批量插入
        String sql = "insert into itemgroup_item(group_id,item_id,seq) values(?,?,?)";
        jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                JSONObject item = items.getJSONObject(i);
                ps.setString(1, groupId);
                ps.setString(2, item.getString("id"));
                ps.setInt(3, item.getInteger("seq"));
            }

            @Override
            public int getBatchSize() {
                return items.size();
            }
        });
    }

    //@Cacheable(cacheNames = "allGroup",unless = "#result == null")
    @Override
    public List<ItemGroup> listAll(String departmentId, String name) {
        QueryWrapper<ItemGroup> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(departmentId)) {
            queryWrapper.eq("department_id", departmentId);
        }
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like("name", name);
        }

        return list(queryWrapper);
    }


    @Cacheable(cacheNames = "allGroup",unless = "#result == null")
    @Override
    public List<ItemGroup> listAll() {
        //向缓存中存入缓存是否有效的标志
        redisUtil.set("allGroup-valid", "true");

        QueryWrapper<ItemGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable_flag", 1).ne("del_flag", 1);
        return list(queryWrapper);
    }

    @Override
    public boolean checkGroupCacheFlag() {
        return redisUtil.get("allGroup-valid") != null && "true".equals(redisUtil.get("allGroup-valid"));
    }

    @Override
    public void setDepartmentNameAndCheckPartName(ItemGroup itemGroup) {
        if (itemGroup == null) {
            return;
        }
        if (StringUtils.isNotBlank(itemGroup.getDepartmentId())) {
            SysDepart depart = sysDepartMapper.selectById(itemGroup.getDepartmentId());
            if (depart != null) {
                itemGroup.setDepartmentName(depart.getDepartName());
                itemGroup.setDepartmentCode(depart.getHisCode());
                if (StringUtils.isBlank(itemGroup.getClassCode()) && StringUtils.isNotBlank(depart.getDepartFunCategory())) {
                    itemGroup.setClassCode(depart.getDepartFunCategory());
                }
                if (StringUtils.isBlank(itemGroup.getCombineSummaryFormat()) && StringUtils.isNotBlank(depart.getSumFormat())) {
                    itemGroup.setCombineSummaryFormat(depart.getSumFormat());
                }
            }
        }
        if (StringUtils.isNotBlank(itemGroup.getCheckSiteCode())) {
            CheckPartDict checkPartDict = checkPartDictMapper.selectOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, itemGroup.getCheckSiteCode()).last("limit 1"));
            if (Objects.nonNull(checkPartDict)){
                itemGroup.setCheckSiteName(checkPartDict.getName());
            }
        }else{
            itemGroup.setCheckSiteName(null);
            itemGroup.setCheckSiteCode(null);
        }
        if (StringUtils.isNotBlank(itemGroup.getEquipTypeCode())) {
            EquipTypeDict equipTypeDict = equipTypeDictMapper.selectOne(new LambdaQueryWrapper<EquipTypeDict>().eq(EquipTypeDict::getEquipTypeCode, itemGroup.getEquipTypeCode()).last("limit 1"));
            if (Objects.nonNull(equipTypeDict)){
                itemGroup.setEquipTypeName(equipTypeDict.getEquipTypeName());
            }
        }else{
            itemGroup.setEquipTypeName(null);
            itemGroup.setEquipTypeCode(null);
        }
        if (StringUtils.isNotBlank(itemGroup.getCheckMethodCode())) {
            CheckMethodDict checkMethodDict = checkMethodDictMapper.selectOne(new LambdaQueryWrapper<CheckMethodDict>().eq(CheckMethodDict::getCode, itemGroup.getCheckMethodCode()).last("limit 1"));
            if (Objects.nonNull(checkMethodDict)){
                itemGroup.setCheckMethodName(checkMethodDict.getName());
            }
        }else{
            itemGroup.setCheckMethodName(null);
            itemGroup.setCheckMethodCode(null);
        }
    }

    @Override
    public Integer addBathByHisItems(List<ItemGroupInterface> itemGroupInterfaces) {
        if (itemGroupInterfaces == null || itemGroupInterfaces.isEmpty()) {
            return 0;
        }
        //转换为ItemGroup
        List<ItemGroup> itemGroups = itemGroupInterfaces.stream().map(itemGroupInterface -> {
            ItemGroup itemGroup = new ItemGroup();
            itemGroup.setDepartmentId(itemGroupInterface.getDepartmentId());
            itemGroup.setName(itemGroupInterface.getItemName());
            itemGroup.setHisCode(itemGroupInterface.getItemCode());
            itemGroup.setHisName(itemGroupInterface.getItemName());
            itemGroup.setHelpChar(StringUtils.lowerCase(itemGroupInterface.getSpellCode()));
            itemGroup.setCostPrice(itemGroupInterface.getItemPrice());
            itemGroup.setPrice(itemGroupInterface.getItemPrice());
            itemGroup.setMinDiscountRate(BigDecimal.ONE);
            itemGroup.setPlatCode(itemGroupInterface.getItemCode());
            itemGroup.setPlatName(itemGroupInterface.getItemName());
            itemGroup.setUnit(itemGroupInterface.getUnitName());
            itemGroup.setEnableFlag(1);
            return itemGroup;
        }).toList();

        Integer successCount = 0;

        Integer maxSeq = jdbcTemplate.queryForObject("select max(sort) from item_group where department_id=?", Integer.class, itemGroupInterfaces.get(0).getDepartmentId());
        maxSeq = maxSeq == null ? 0 : maxSeq;
        for (int i = 0; i < itemGroups.size(); i++) {
            try {
                ItemGroup itemGroup = itemGroups.get(i);
                Integer existCount = jdbcTemplate.queryForObject("select count(1) from item_group where his_code=?", Integer.class, itemGroup.getHisCode());
                existCount = existCount == null ? 0 : existCount;
                if (existCount == 0) {
                    setDepartmentNameAndCheckPartName(itemGroup);
                    itemGroup.setSort(maxSeq + i + 1);
                    save(itemGroup);
                    successCount++;
                }
            } catch (Exception ignored) {
            }
        }

        return successCount;
    }


    @Override
    public List<ItemGroupLabel> listAllByDepartment(String name, String answerId, String suitId) {
        LambdaQueryWrapper<ItemGroup> queryWrapper = new LambdaQueryWrapper<ItemGroup>().eq(ItemGroup::getEnableFlag, 1).ne(ItemGroup::getChargeItemOnlyFlag, 1);
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.like(ItemGroup::getName, name);
        }
        List<ItemGroupLabel> labels = new LinkedList<>();
        //查询推荐项目
        List<ItemGroup> recommendGroups = Lists.newArrayList();
        Set<String> recommendIds = Sets.newHashSet();
        if (StringUtils.isNotBlank(answerId)) {
            recommendGroups = diseaseSurveyAnswerService.queryRecommendGroups(answerId);
            //获取系统套餐
            List<String> suitGroupIds = itemSuitMapper.getSysSuitGroupIds("系统套餐");
            recommendIds = recommendGroups.stream().map(ItemGroup::getId).collect(Collectors.toSet());
            recommendIds.addAll(suitGroupIds);
            LambdaQueryWrapper<ItemGroup> recommendQueryWrapper = new LambdaQueryWrapper<ItemGroup>().eq(ItemGroup::getEnableFlag, 1).ne(ItemGroup::getChargeItemOnlyFlag, 1);
            if (StringUtils.isNotBlank(name)) {
                recommendQueryWrapper.like(ItemGroup::getName, name);
            }
            List<ItemGroup> recommendList = itemGroupMapper.selectList(recommendQueryWrapper.in(ItemGroup::getId, recommendIds));
            recommendList.forEach(i -> {
                i.setIsRecommendObj(ImmutableMap.of("selected", true, "value", i.getId()));
                i.setIsRecommend(true);
            });
            ItemGroupLabel recommendLabel = new ItemGroupLabel();
            recommendLabel.setLabel("推荐项目");
            recommendLabel.setItemList(recommendList);
            labels.add(recommendLabel);
        }
        if (StringUtils.isNotBlank(suitId)) {
            //获取选中套餐
            List<SuitGroup> suitGroups = itemSuitMapper.getSuitGroup(suitId);
            if (CollectionUtils.isNotEmpty(suitGroups)) {
                Set<String> suitGroupIds = suitGroups.stream().map(SuitGroup::getGroupId).collect(Collectors.toSet());
                recommendIds.addAll(suitGroupIds);
                LambdaQueryWrapper<ItemGroup> suitGroupQueryWrapper = new LambdaQueryWrapper<ItemGroup>().eq(ItemGroup::getEnableFlag, 1).ne(ItemGroup::getChargeItemOnlyFlag, 1);
                if (StringUtils.isNotBlank(name)) {
                    suitGroupQueryWrapper.like(ItemGroup::getName, name);
                }
                List<ItemGroup> suitGroupList = itemGroupMapper.selectList(suitGroupQueryWrapper.in(ItemGroup::getId, suitGroupIds));
                suitGroupList.forEach(i -> {
                    i.setIsRecommendObj(ImmutableMap.of("selected", true, "value", i.getId()));
                    i.setIsRecommend(true);
                });
                ItemSuit itemSuit = itemSuitMapper.selectById(suitId);
                ItemGroupLabel recommendLabel = new ItemGroupLabel();
                recommendLabel.setLabel(Objects.nonNull(itemSuit) ? itemSuit.getName() : "套餐项目");
                recommendLabel.setItemList(suitGroupList);
                labels.add(recommendLabel);
            }
        }


        List<ItemGroup> itemGroups = itemGroupMapper.selectList(queryWrapper);
        for (ItemGroup g : itemGroups) {
            if (recommendIds.contains(g.getId())) {
                g.setIsRecommend(true);
                g.setIsRecommendObj(ImmutableMap.of("selected", true, "value", g.getId()));

            } else {
                g.setIsRecommend(false);
                g.setIsRecommendObj(ImmutableMap.of("selected", false, "value", g.getId()));

            }
        }
        if (CollectionUtils.isNotEmpty(itemGroups)) {
            Map<String, List<ItemGroup>> groupMap = itemGroups.stream().collect(Collectors.groupingBy(ItemGroup::getDepartmentName));
            groupMap.forEach((departmentName, groups) -> {
                ItemGroupLabel label = new ItemGroupLabel();
                label.setLabel(departmentName);
                label.setItemList(groups);
                labels.add(label);
            });
        }


        return labels;
    }

    @CacheEvict(cacheNames = "allGroup", allEntries = true)
    @Override
    public void evictCached() {
        redisUtil.set("allGroup-valid", "false");
    }

    @Override
    public void batchUpdateEnableFlag(List<String> idList, String enableFlag) {
        LambdaUpdateWrapper<ItemGroup> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ItemGroup::getEnableFlag, enableFlag).in(ItemGroup::getId, idList);
        update(updateWrapper);
    }

    @Override
    public List<ItemGroup> listByRiskFactor(List<String> riskFactorCodes, String post) {
        //根据code取出id
        List<ZyRiskFactor> zyRiskFactors = zyRiskFactorMapper.selectByIdsOrCodes(riskFactorCodes);
        List<String> riskFactorIds = zyRiskFactors.stream().map(ZyRiskFactor::getId).collect(Collectors.toList());

        return itemGroupMapper.listByRiskFactor(riskFactorIds, post);
    }


    @Cacheable(cacheNames = "allGroupString", unless = "#result == null")
    @Override
    public String jionAllGroup2String() {
        LambdaQueryWrapper<ItemGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ItemGroup::getEnableFlag, 1).ne(ItemGroup::getChargeItemOnlyFlag, 1);
        List<ItemGroup> itemGroups = itemGroupMapper.selectList(queryWrapper);
        StringBuilder sb = new StringBuilder();
        for (ItemGroup itemGroup : itemGroups) {
            sb.append(itemGroup.getName()).append(",");
        }
        return sb.toString();
    }

    @CacheEvict(cacheNames = "allGroupString", allEntries = true)
    @Override
    public void evictCachedGroupString() {
        redisUtil.removeAll("basicinfo::itemGroup");
    }

    @Override
    public List<ItemGroup> listItemGroupByNames(List<String> names) {
        return itemGroupMapper.listItemGroupByNames(names);
    }

    @Override
    public void updateItemGroup(ItemGroup itemGroup) {
        // 1、更新项目组合
        itemGroupMapper.updateById(itemGroup);
        //2、更新套餐关联的大项代码和名称
        LambdaUpdateWrapper<SuitGroup> suitGroupUpdateWrapper = new LambdaUpdateWrapper<>();
        suitGroupUpdateWrapper.set(SuitGroup::getGroupName, itemGroup.getName()).set(SuitGroup::getDepartmentId, itemGroup.getDepartmentId()).set(SuitGroup::getDepartmentName, itemGroup.getDepartmentName()).eq(SuitGroup::getGroupId, itemGroup.getId());
        suitGroupMapper.update(null, suitGroupUpdateWrapper);

        //3、更新单位分组关联的大项代码和名称
        LambdaUpdateWrapper<CompanyTeamItemGroup> teamGroupUpdateWrapper = new LambdaUpdateWrapper<>();
        teamGroupUpdateWrapper.set(CompanyTeamItemGroup::getItemGroupName, itemGroup.getName()).set(CompanyTeamItemGroup::getDepartmentId, itemGroup.getDepartmentId()).set(CompanyTeamItemGroup::getDepartmentName, itemGroup.getDepartmentName()).eq(CompanyTeamItemGroup::getItemGroupId, itemGroup.getId());
        companyTeamItemGroupMapper.update(null, teamGroupUpdateWrapper);

        //4、更新条码设置关联的大项代码和名称
        LambdaUpdateWrapper<BarcodeSettingGroup> settingUpdateWrapper = new LambdaUpdateWrapper<>();
        settingUpdateWrapper.set(BarcodeSettingGroup::getGroupName, itemGroup.getName()).set(BarcodeSettingGroup::getHisCode, itemGroup.getHisCode()).set(BarcodeSettingGroup::getHisName, itemGroup.getHisName()).eq(BarcodeSettingGroup::getGroupId, itemGroup.getId());
        barcodeSettingGroupMapper.update(null, settingUpdateWrapper);
    }

    @Override
    public ItemGroup getItemGroupById(String groupId) {
        return itemGroupMapper.getItemGroupById(groupId);
    }

    @Override
    public List<ItemGroup> getItemGroupByHisCode(String hisCode) {
        return itemGroupMapper.getItemGroupByHisCode(hisCode);
    }
}
