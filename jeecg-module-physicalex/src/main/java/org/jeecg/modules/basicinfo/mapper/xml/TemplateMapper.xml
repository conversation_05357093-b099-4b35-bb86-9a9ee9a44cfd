<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.TemplateMapper">

    <select id="listTemplate" resultType="org.jeecg.modules.basicinfo.entity.Template">
        SELECT * FROM template
        <where>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="examCategory != null and examCategory != ''">
                AND exam_category = #{examCategory}
            </if>
            <if test="regType != null and regType != ''">
                AND reg_type = #{regType}
            </if>
            <if test="defaultFlag != null and defaultFlag != ''">
                AND default_flag = #{defaultFlag}
            </if>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
        </where>
    </select>
    <select id="getApplyTemplateIdsByReg" resultType="java.lang.String">
        SELECT distinct(t.id)  FROM template t
        join template_group g on t.id = g.template_id
        join customer_reg_item_group c on (
            g.group_id = c.item_group_id
            AND (c.check_part_code IS NULL OR c.check_part_code = '' OR c.check_part_code = g.check_part_code)
        )
        WHERE t.type = #{templateType} and c.customer_reg_id = #{customerRegId} and t.del_flag = 0 and c.add_minus_flag !=-1 and c.pay_status != '退款成功'
        <if test="reprintFlag=false">
         and   c.apply_print_times=0
        </if>
    </select>
    <select id="listByRegType" resultType="org.jeecg.modules.basicinfo.entity.Template">
        select id, name, type, exam_category, reg_type, enable_flag, group_id, key_item_id,department_id from template where del_flag = '0' and enable_flag='1' <if test="regType != null and regType != ''"> and reg_type = #{regType} </if> <if test="type!=null and type!=''"> and type=#{type}</if>
    </select>
</mapper>