package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.entity.ItemStandard;
import org.jeecg.modules.basicinfo.mapper.ItemStandardMapper;
import org.jeecg.modules.basicinfo.mapper.ItemInfoMapper;
import org.jeecg.modules.basicinfo.service.IItemInfoService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 体检项目
 * @Author: jeecg-boot
 * @Date:   2024-01-31
 * @Version: V1.0
 */
@Service
public class ItemInfoServiceImpl extends ServiceImpl<ItemInfoMapper, ItemInfo> implements IItemInfoService {

	@Autowired
	private ItemInfoMapper itemInfoMapper;
	@Autowired
	private ItemStandardMapper itemStandardMapper;
	@Autowired
	private SysDepartMapper sysDepartMapper;

	@Override
	public void fillDepartName(ItemInfo itemInfo) {
		if(StringUtils.isNotBlank(itemInfo.getDepartmentId())) {
			SysDepart depart =sysDepartMapper.getDepartById(itemInfo.getDepartmentId());
			if(depart!=null)
			{
				itemInfo.setDepartmentName(depart.getDepartName());
				itemInfo.setDepartmentCode(depart.getHisCode());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(ItemInfo itemInfo, List<ItemStandard> itemStandardList) {
		itemInfoMapper.insert(itemInfo);
		if(itemStandardList!=null && itemStandardList.size()>0) {
			for(ItemStandard entity:itemStandardList) {
				//外键设置
				entity.setItemId(itemInfo.getId());
				itemStandardMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(ItemInfo itemInfo,List<ItemStandard> itemStandardList) {
		itemInfoMapper.updateById(itemInfo);
		
		//1.先删除子表数据
		itemStandardMapper.deleteByMainId(itemInfo.getId());
		
		//2.子表数据重新插入
		if(itemStandardList!=null && itemStandardList.size()>0) {
			for(ItemStandard entity:itemStandardList) {
				//外键设置
				entity.setItemId(itemInfo.getId());
				itemStandardMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		itemStandardMapper.deleteByMainId(id);
		itemInfoMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			itemStandardMapper.deleteByMainId(id.toString());
			itemInfoMapper.deleteById(id);
		}
	}

	@Override
	public List<ItemInfo> listItemByDepartId(String departId) {
		return itemInfoMapper.listItemByDepartId(departId);
	}

	@Override
	public Page<ItemInfo> listByKeyword(Page<ItemInfo> page, String keyword,String departmentId) {

		return itemInfoMapper.listByKeyword(page,keyword,departmentId);
	}

	@Override
	public List<ItemInfo> getItemByGroupId(String groupId) {
		return itemInfoMapper.getItemByGroupId(groupId);
	}

}
