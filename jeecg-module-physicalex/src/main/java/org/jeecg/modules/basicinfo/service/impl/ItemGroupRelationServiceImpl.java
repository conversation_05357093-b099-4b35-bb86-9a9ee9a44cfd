package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.GroupRelationVO;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.mapper.ItemGroupRelationMapper;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: item_group_relation
 * @Author: jeecg-boot
 * @Date: 2024-12-02
 * @Version: V1.0
 */
@Slf4j
@Service
public class ItemGroupRelationServiceImpl extends ServiceImpl<ItemGroupRelationMapper, ItemGroupRelation> implements IItemGroupRelationService {
    @Autowired
    private ItemGroupRelationMapper itemGroupRelationMapper;
    @Autowired
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;

    @Override
    public GroupRelationVO getRelationGroupsByMainId(String mainId) {
        GroupRelationVO groupRelationVO = new GroupRelationVO();
        groupRelationVO.setGroupId(mainId);
        //附属关系
        List<ItemGroupRelation> attachGroups = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, mainId).eq(ItemGroupRelation::getRelation, "附属"));
        groupRelationVO.setAttachGroups(attachGroups);
        //赠送关系
        List<ItemGroupRelation> giftGroups = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, mainId).eq(ItemGroupRelation::getRelation, "赠送"));
        groupRelationVO.setGiftGroups(giftGroups);
        //互斥关系
        List<ItemGroupRelation> exclusiveGroups = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, mainId).eq(ItemGroupRelation::getRelation, "互斥"));
        List<ItemGroupRelation> exclusiveGroups2 = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getRelationGroupId, mainId).eq(ItemGroupRelation::getRelation, "互斥"));
        List<String> exclusiveIds = exclusiveGroups.stream().map(ItemGroupRelation::getRelationGroupId).toList();
        List<String> exclusiveId2s = exclusiveGroups2.stream().map(ItemGroupRelation::getGroupId).toList();
        if (CollectionUtils.isNotEmpty(exclusiveId2s)) {
            exclusiveIds.addAll(exclusiveId2s);
        }
        if (CollectionUtils.isNotEmpty(exclusiveIds)) {
            groupRelationVO.setExclusiveGroups(exclusiveIds);
        }
        //依赖关系
        List<ItemGroupRelation> dependentGroups = itemGroupRelationMapper.selectList(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, mainId).eq(ItemGroupRelation::getRelation, "依赖"));
        groupRelationVO.setDependentGroups(dependentGroups);
        return groupRelationVO;
    }


    @Override
    public void saveRelationGroupBatch(GroupRelationVO groupRelationVO) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> groupIds = Lists.newArrayList();

        // 收集附属关系的groupId
        List<String> attachGroupIds = groupRelationVO.getAttachGroups().stream().map(ItemGroupRelation::getRelationGroupId).toList();
        if (CollectionUtils.isNotEmpty(attachGroupIds)) {
            groupIds.addAll(attachGroupIds);
        }

        // 收集赠送关系的groupId
        if (CollectionUtils.isNotEmpty(groupRelationVO.getGiftGroups())) {
            List<String> giftGroupIds = groupRelationVO.getGiftGroups().stream().map(ItemGroupRelation::getRelationGroupId).filter(StringUtils::isNotBlank).toList();
            groupIds.addAll(giftGroupIds);
        }

        // 收集互斥关系的groupId
        if (CollectionUtils.isNotEmpty(groupRelationVO.getExclusiveGroups())) {
            groupIds.addAll(groupRelationVO.getExclusiveGroups());
        }

        // 收集依赖关系的groupId
        if (CollectionUtils.isNotEmpty(groupRelationVO.getDependentGroups())) {
            List<String> dependentGroupIds = groupRelationVO.getDependentGroups().stream().map(ItemGroupRelation::getRelationGroupId).filter(StringUtils::isNotBlank).toList();
            groupIds.addAll(dependentGroupIds);
        }

        // 只有当groupIds不为空时才查询，避免空IN子句错误
        log.info("收集到的所有关联项目ID: {}", groupIds);
        final Map<String, List<ItemGroup>> map;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            List<ItemGroup> itemGroups = itemGroupMapper.selectBatchIds(groupIds);
            map = itemGroups.stream().collect(Collectors.groupingBy(ItemGroup::getId));
            log.info("查询到的项目信息: {}", itemGroups.stream().map(g -> g.getId() + ":" + g.getName()).toList());
        } else {
            map = new HashMap<>();
            log.info("没有需要查询的关联项目ID");
        }
        // 处理附属关系 - 先删除旧数据，再保存新数据
        int deletedAttachCount = remove(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupRelationVO.getGroupId()).eq(ItemGroupRelation::getRelation, "附属")) ? 1 : 0;
        log.info("删除项目{}的附属关系数据，删除成功: {}", groupRelationVO.getGroupId(), deletedAttachCount > 0);

        if (CollectionUtils.isNotEmpty(groupRelationVO.getAttachGroups())) {
            groupRelationVO.getAttachGroups().forEach(item -> {
                item.setGroupId(groupRelationVO.getGroupId());
                item.setGroupName(groupRelationVO.getGroupName());

                // 安全获取关联项目名称
                List<ItemGroup> relatedGroups = map.get(item.getRelationGroupId());
                if (relatedGroups != null && !relatedGroups.isEmpty()) {
                    item.setRelationGroupName(relatedGroups.get(0).getName());
                } else {
                    item.setRelationGroupName("未知项目");
                    log.warn("找不到ID为{}的项目信息", item.getRelationGroupId());
                }

                item.setRelation("附属");
                item.setCreateBy(loginUser.getUsername());
                item.setCreateTime(new java.util.Date());
            });
            saveBatch(groupRelationVO.getAttachGroups());
            log.info("保存项目{}的附属关系数据，数量: {}", groupRelationVO.getGroupId(), groupRelationVO.getAttachGroups().size());
        } else {
            log.info("项目{}没有附属关系数据需要保存", groupRelationVO.getGroupId());
        }

        // 处理赠送关系 - 先删除旧数据，再保存新数据
        int deletedGiftCount = remove(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupRelationVO.getGroupId()).eq(ItemGroupRelation::getRelation, "赠送")) ? 1 : 0;
        log.info("删除项目{}的赠送关系数据，删除成功: {}", groupRelationVO.getGroupId(), deletedGiftCount > 0);

        if (CollectionUtils.isNotEmpty(groupRelationVO.getGiftGroups())) {
            groupRelationVO.getGiftGroups().forEach(item -> {
                item.setGroupId(groupRelationVO.getGroupId());
                item.setGroupName(groupRelationVO.getGroupName());

                // 安全获取关联项目名称
                List<ItemGroup> relatedGroups = map.get(item.getRelationGroupId());
                if (relatedGroups != null && !relatedGroups.isEmpty()) {
                    item.setRelationGroupName(relatedGroups.get(0).getName());
                } else {
                    item.setRelationGroupName("未知项目");
                    log.warn("找不到ID为{}的项目信息", item.getRelationGroupId());
                }

                item.setRelation("赠送");
                item.setCreateBy(loginUser.getUsername());
                item.setCreateTime(new java.util.Date());
            });
            saveBatch(groupRelationVO.getGiftGroups());
            log.info("保存项目{}的赠送关系数据，数量: {}", groupRelationVO.getGroupId(), groupRelationVO.getGiftGroups().size());
        } else {
            log.info("项目{}没有赠送关系数据需要保存", groupRelationVO.getGroupId());
        }

        // 处理互斥关系 - 先删除旧数据，再保存新数据
        int deletedExclusiveCount = remove(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupRelationVO.getGroupId()).eq(ItemGroupRelation::getRelation, "互斥")) ? 1 : 0;
        log.info("删除项目{}的互斥关系数据，删除成功: {}", groupRelationVO.getGroupId(), deletedExclusiveCount > 0);

        if (CollectionUtils.isNotEmpty(groupRelationVO.getExclusiveGroups())) {
            List<ItemGroupRelation> itemGroupRelations = Lists.newArrayList();
            groupRelationVO.getExclusiveGroups().forEach(item -> {
                ItemGroupRelation itemGroupRelation = new ItemGroupRelation();
                itemGroupRelation.setGroupId(groupRelationVO.getGroupId());
                itemGroupRelation.setGroupName(groupRelationVO.getGroupName());
                itemGroupRelation.setRelationGroupId(item);

                // 安全获取关联项目名称
                List<ItemGroup> relatedGroups = map.get(item);
                if (relatedGroups != null && !relatedGroups.isEmpty()) {
                    itemGroupRelation.setRelationGroupName(relatedGroups.get(0).getName());
                } else {
                    itemGroupRelation.setRelationGroupName("未知项目");
                    log.warn("找不到ID为{}的项目信息", item);
                }

                itemGroupRelation.setRelation("互斥");
                itemGroupRelation.setCreateBy(loginUser.getUsername());
                itemGroupRelation.setCreateTime(new java.util.Date());
                itemGroupRelations.add(itemGroupRelation);
            });
            saveBatch(itemGroupRelations);
            log.info("保存项目{}的互斥关系数据，数量: {}", groupRelationVO.getGroupId(), itemGroupRelations.size());
        } else {
            log.info("项目{}没有互斥关系数据需要保存", groupRelationVO.getGroupId());
        }

        // 处理依赖关系 - 先删除旧数据，再保存新数据
        int deletedDependentCount = remove(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, groupRelationVO.getGroupId()).eq(ItemGroupRelation::getRelation, "依赖")) ? 1 : 0;
        log.info("删除项目{}的依赖关系数据，删除成功: {}", groupRelationVO.getGroupId(), deletedDependentCount > 0);

        if (CollectionUtils.isNotEmpty(groupRelationVO.getDependentGroups())) {
            groupRelationVO.getDependentGroups().forEach(item -> {
                item.setGroupId(groupRelationVO.getGroupId());
                item.setGroupName(groupRelationVO.getGroupName());

                // 设置关联大项名称
                if (StringUtils.isNotBlank(item.getRelationGroupId())) {
                    List<ItemGroup> relatedGroups = map.get(item.getRelationGroupId());
                    if (relatedGroups != null && !relatedGroups.isEmpty()) {
                        item.setRelationGroupName(relatedGroups.get(0).getName());
                    } else {
                        item.setRelationGroupName("未知项目");
                        log.warn("找不到ID为{}的项目信息", item.getRelationGroupId());
                    }
                }

                // 如果是小项依赖，设置小项名称
                if ("ITEM".equals(item.getRelationItemType()) && StringUtils.isNotBlank(item.getRelationItemId())) {
                    // 从前端传来的名称直接使用，或者查询数据库获取
                    if (StringUtils.isBlank(item.getRelationItemName())) {
                        // 这里可以添加查询ItemInfo的逻辑
                        item.setRelationItemName("未知小项");
                    }
                }

                item.setRelation("依赖");
                item.setCreateBy(loginUser.getUsername());
                item.setCreateTime(new java.util.Date());
            });
            saveBatch(groupRelationVO.getDependentGroups());
            log.info("保存项目{}的依赖关系数据，数量: {}", groupRelationVO.getGroupId(), groupRelationVO.getDependentGroups().size());
        } else {
            log.info("项目{}没有依赖关系数据需要保存", groupRelationVO.getGroupId());
        }
    }

    @Override
    public List<CustomerRegItemGroup> getAttachGroups(List<CustomerRegItemGroup> addingItemGroups) {
        Set<String> groupIds = addingItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Map<String, List<ItemGroupRelation>> relationGroupMap = list(new LambdaQueryWrapper<ItemGroupRelation>().in(ItemGroupRelation::getGroupId, groupIds).eq(ItemGroupRelation::getRelation, "附属")).stream().collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));
        List<CustomerRegItemGroup> relationGroupList = Lists.newArrayList();
        addingItemGroups.forEach(group -> {
            List<ItemGroupRelation> itemGroupRelations = relationGroupMap.get(group.getItemGroupId());
            if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                // 根据部位逻辑筛选匹配的附属项目
                List<ItemGroupRelation> matchedRelations = filterAttachGroupsByPart(itemGroupRelations, group);
                itemGroupRelations = matchedRelations;

                if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                    itemGroupRelations.forEach(relation -> {
                        if (StringUtils.isNotBlank(relation.getRelationGroupId())) {
                            ItemGroup itemGroup = itemGroupMapper.selectById(relation.getRelationGroupId());
                            if (Objects.nonNull(itemGroup)) {
                                Integer quantity = Objects.nonNull(relation.getQuantity()) ? relation.getQuantity() : 1;
                                for (int i = 0; i < quantity; i++) {
                                    CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();
                                    BeanUtils.copyProperties(group, customerRegItemGroup);
                                    customerRegItemGroup.setId(null);
                                    customerRegItemGroup.setItemGroupId(itemGroup.getId());
                                    customerRegItemGroup.setItemGroupName(itemGroup.getName());
                                    customerRegItemGroup.setHisCode(itemGroup.getHisCode());
                                    customerRegItemGroup.setHisName(itemGroup.getHisName());
                                    customerRegItemGroup.setPlatCode(itemGroup.getPlatCode());
                                    customerRegItemGroup.setPlatName(itemGroup.getPlatName());
                                    customerRegItemGroup.setDepartmentId(itemGroup.getDepartmentId());
                                    customerRegItemGroup.setDepartmentCode(itemGroup.getDepartmentCode());
                                    customerRegItemGroup.setDepartmentName(itemGroup.getDepartmentName());
                                    customerRegItemGroup.setClassCode(itemGroup.getClassCode());
                                    customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                    customerRegItemGroup.setPrice(itemGroup.getPrice());
                                    customerRegItemGroup.setPriceAfterDis(itemGroup.getPrice());
                                    customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                    customerRegItemGroup.setAttachBaseId(group.getId());

                                    // 设置附属项目的部位信息
                                    setAttachGroupPartInfo(customerRegItemGroup, relation, group);

                                    relationGroupList.add(customerRegItemGroup);
                                }
                            }
                        }

                    });
                }
            }
        });
        return relationGroupList;
    }

    @Override
    public List<CustomerRegItemGroup> getGiftGroups(List<CustomerRegItemGroup> addingItemGroups) {
        Set<String> groupIds = addingItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Map<String, List<ItemGroupRelation>> relationGroupMap = list(new LambdaQueryWrapper<ItemGroupRelation>().in(ItemGroupRelation::getGroupId, groupIds).eq(ItemGroupRelation::getRelation, "赠送")).stream().collect(Collectors.groupingBy(ItemGroupRelation::getGroupId));
        List<CustomerRegItemGroup> relationGroupList = Lists.newArrayList();
        addingItemGroups.forEach(group -> {
            List<ItemGroupRelation> itemGroupRelations = relationGroupMap.get(group.getItemGroupId());
            if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                // 根据部位逻辑筛选匹配的赠送项目
                List<ItemGroupRelation> matchedRelations = filterGiftGroupsByPart(itemGroupRelations, group);
                itemGroupRelations = matchedRelations;

                if (CollectionUtils.isNotEmpty(itemGroupRelations)) {
                    itemGroupRelations.forEach(relation -> {
                        if (StringUtils.isNotBlank(relation.getRelationGroupId())) {
                            ItemGroup itemGroup = itemGroupMapper.selectById(relation.getRelationGroupId());
                            if (Objects.nonNull(itemGroup)) {
                                Integer quantity = Objects.nonNull(relation.getQuantity()) ? relation.getQuantity() : 1;
                                for (int i = 0; i < quantity; i++) {
                                    CustomerRegItemGroup customerRegItemGroup = new CustomerRegItemGroup();
                                    BeanUtils.copyProperties(group, customerRegItemGroup);
                                    customerRegItemGroup.setId(null);
                                    customerRegItemGroup.setItemGroupId(itemGroup.getId());
                                    customerRegItemGroup.setItemGroupName(itemGroup.getName());
                                    customerRegItemGroup.setHisCode(itemGroup.getHisCode());
                                    customerRegItemGroup.setHisName(itemGroup.getHisName());
                                    customerRegItemGroup.setPlatCode(itemGroup.getPlatCode());
                                    customerRegItemGroup.setPlatName(itemGroup.getPlatName());
                                    customerRegItemGroup.setDepartmentId(itemGroup.getDepartmentId());
                                    customerRegItemGroup.setDepartmentCode(itemGroup.getDepartmentCode());
                                    customerRegItemGroup.setDepartmentName(itemGroup.getDepartmentName());
                                    customerRegItemGroup.setClassCode(itemGroup.getClassCode());
                                    customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                    customerRegItemGroup.setPrice(itemGroup.getPrice());
                                    customerRegItemGroup.setPriceAfterDis(itemGroup.getPrice());
                                    customerRegItemGroup.setMinDiscountRate(itemGroup.getMinDiscountRate());
                                    // 赠送项目标记为免费
                                    customerRegItemGroup.setPrice(BigDecimal.ZERO);
                                    customerRegItemGroup.setPriceAfterDis(BigDecimal.ZERO);
                                    customerRegItemGroup.setGiftBaseId(group.getId()); // 设置赠送基础项目ID

                                    // 设置赠送项目的部位信息
                                    setGiftGroupPartInfo(customerRegItemGroup, relation, group);

                                    relationGroupList.add(customerRegItemGroup);
                                }
                            }
                        }

                    });
                }
            }
        });
        return relationGroupList;
    }

    @Override
    public void checkIsHaveMutexes(List<CustomerRegItemGroup> addingItemGroups) {
        Set<String> groupIds = addingItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Set<String> addedGroupIds = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, addingItemGroups.get(0).getCustomerRegId()).ne(CustomerRegItemGroup::getAddMinusFlag, "-1").ne(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功)).stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        //验证是否存在互斥项目
        List<ItemGroupRelation> mutexRelations = list(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getRelation, "互斥"));
        if (CollectionUtils.isEmpty(mutexRelations)) {
            return;
        }

        Set<String> allRelevantGroupIds = new HashSet<>();
        allRelevantGroupIds.addAll(groupIds);
        allRelevantGroupIds.addAll(addedGroupIds);
        List<String> msgs = new ArrayList<>();
        for (ItemGroupRelation relation : mutexRelations) {
            String baseId = relation.getGroupId();
            String relatedId = relation.getRelationGroupId();
            for (String groupId : groupIds) {
                //判断新增项目之间是否存在互斥项目
                if (groupId.equals(baseId)) {
                    //判断新增项目中有无存在relationId
                    if (groupId.contains(relatedId)) {
                        //该组互斥关系存在
                        String detailMsg = "新增项目【" + relation.getGroupName() + "】与【" + relation.getRelationGroupName() + "】冲突";
                        msgs.add(detailMsg);
                    }

                } else if (groupId.equals(relatedId)) {
                    //判断新增项目中有无存在baseId
                    if (groupId.contains(baseId)) {
                        //该组互斥关系存在
                        String detailMsg = "新增项目【" + relation.getRelationGroupName() + "】项目【" + relation.getGroupName() + "】冲突";
                        msgs.add(detailMsg);
                    }
                }
                //判断已添加项目与新增项目之间是否存在互斥项目
                if (groupId.equals(baseId)) {
                    //判断新增项目中有无存在relationId
                    if (addedGroupIds.contains(relatedId)) {
                        //该组互斥关系存在
                        String detailMsg = "新增项目【" + relation.getGroupName() + "】与已有项目【" + relation.getRelationGroupName() + "】冲突";
                        msgs.add(detailMsg);
                    }

                } else if (groupId.equals(relatedId)) {
                    //判断新增项目中有无存在baseId
                    if (addedGroupIds.contains(baseId)) {
                        //该组互斥关系存在
                        String detailMsg = "新增项目【" + relation.getRelationGroupName() + "】与已有项目【" + relation.getGroupName() + "】冲突";
                        msgs.add(detailMsg);
                    }
                }
            }

        }
        if (CollectionUtils.isNotEmpty(msgs)) {
            throw new RuntimeException(StringUtils.join(msgs, ","));
        }

    }

    @Override
    public void checkDependentGroups(List<CustomerRegItemGroup> addingItemGroups) throws Exception {
        Set<String> groupIds = addingItemGroups.stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());
        Set<String> addedGroupIds = customerRegItemGroupService.list(new LambdaQueryWrapper<CustomerRegItemGroup>().eq(CustomerRegItemGroup::getCustomerRegId, addingItemGroups.get(0).getCustomerRegId()).ne(CustomerRegItemGroup::getAddMinusFlag, "-1").ne(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功)).stream().map(CustomerRegItemGroup::getItemGroupId).collect(Collectors.toSet());

        // 查询依赖关系
        List<ItemGroupRelation> dependentRelations = list(new LambdaQueryWrapper<ItemGroupRelation>().in(ItemGroupRelation::getGroupId, groupIds).eq(ItemGroupRelation::getRelation, "依赖"));

        if (CollectionUtils.isEmpty(dependentRelations)) {
            return;
        }

        List<String> msgs = new ArrayList<>();
        for (ItemGroupRelation relation : dependentRelations) {
            String groupId = relation.getGroupId();
            String relationItemType = relation.getRelationItemType();

            // 检查是否满足依赖条件
            boolean dependencyMet = false;

            if ("GROUP".equals(relationItemType)) {
                // 大项依赖：检查依赖的大项是否已存在
                String dependentGroupId = relation.getRelationGroupId();
                dependencyMet = addedGroupIds.contains(dependentGroupId) || groupIds.contains(dependentGroupId);

                if (!dependencyMet) {
                    String msg = String.format("项目【%s】依赖项目【%s】，请先添加依赖项目", relation.getGroupName(), relation.getRelationGroupName());
                    msgs.add(msg);
                }
            } else if ("ITEM".equals(relationItemType)) {
                // 优化：跳过小项依赖检查，只在项目录入阶段检查大项依赖
                log.info("跳过小项依赖检查: 项目【{}】依赖小项【{}】（属于【{}】）", relation.getGroupName(), relation.getRelationItemName(), relation.getRelationGroupName());
                continue;
            }
        }

        if (CollectionUtils.isNotEmpty(msgs)) {
            throw new RuntimeException(StringUtils.join(msgs, "；"));
        }
    }

    /**
     * 根据部位逻辑筛选匹配的附属项目
     *
     * @param itemGroupRelations 所有附属关系
     * @param mainGroup          主项目
     * @return 匹配的附属关系列表
     */
    private List<ItemGroupRelation> filterAttachGroupsByPart(List<ItemGroupRelation> itemGroupRelations, CustomerRegItemGroup mainGroup) {
        if (CollectionUtils.isEmpty(itemGroupRelations)) {
            return itemGroupRelations;
        }

        String mainCheckPartId = mainGroup.getCheckPartId();

        // 如果主项目没有部位信息，返回所有没有部位限制的附属项目
        if (StringUtils.isBlank(mainCheckPartId)) {
            return itemGroupRelations.stream().filter(relation -> StringUtils.isBlank(relation.getMainCheckPartId())).collect(Collectors.toList());
        }

        // 如果主项目有部位信息，筛选匹配的附属项目
        List<ItemGroupRelation> matchedRelations = itemGroupRelations.stream().filter(relation -> {
            // 如果附属关系没有指定主项目部位，则匹配所有部位
            if (StringUtils.isBlank(relation.getMainCheckPartId())) {
                return true;
            }
            // 如果指定了主项目部位，则必须完全匹配
            return StringUtils.equals(mainCheckPartId, relation.getMainCheckPartId());
        }).collect(Collectors.toList());

        log.info("主项目{}(部位:{})匹配到{}个附属项目", mainGroup.getItemGroupName(), mainGroup.getCheckPartName(), matchedRelations.size());

        return matchedRelations;
    }

    /**
     * 为附属项目设置部位信息
     *
     * @param attachGroup 附属项目
     * @param relation    附属关系
     * @param mainGroup   主项目（用于获取主项目部位信息）
     */
    private void setAttachGroupPartInfo(CustomerRegItemGroup attachGroup, ItemGroupRelation relation, CustomerRegItemGroup mainGroup) {
        // 优先使用配置中指定的附属项目部位
        if (StringUtils.isNotBlank(relation.getRelationCheckPartId())) {
            attachGroup.setCheckPartId(relation.getRelationCheckPartId());
            attachGroup.setCheckPartName(relation.getRelationCheckPartName());
            attachGroup.setCheckPartCode(relation.getRelationCheckPartCode());
        }
        // 如果附属项目没有配置部位，但主项目有部位，则使用主项目的部位
        else if (StringUtils.isNotBlank(mainGroup.getCheckPartId())) {
            attachGroup.setCheckPartId(mainGroup.getCheckPartId());
            attachGroup.setCheckPartName(mainGroup.getCheckPartName());
            attachGroup.setCheckPartCode(mainGroup.getCheckPartCode());
            log.info("附属项目{}使用主项目{}的部位信息: {}", attachGroup.getItemGroupName(), mainGroup.getItemGroupName(), mainGroup.getCheckPartName());
        }
    }

    /**
     * 根据部位逻辑筛选匹配的赠送项目
     *
     * @param itemGroupRelations 所有赠送关系
     * @param mainGroup          主项目
     * @return 匹配的赠送关系列表
     */
    private List<ItemGroupRelation> filterGiftGroupsByPart(List<ItemGroupRelation> itemGroupRelations, CustomerRegItemGroup mainGroup) {
        if (CollectionUtils.isEmpty(itemGroupRelations)) {
            return itemGroupRelations;
        }

        String mainCheckPartId = mainGroup.getCheckPartId();

        // 如果主项目没有部位信息，返回所有没有部位限制的赠送项目
        if (StringUtils.isBlank(mainCheckPartId)) {
            return itemGroupRelations.stream().filter(relation -> StringUtils.isBlank(relation.getMainCheckPartId())).collect(Collectors.toList());
        }

        // 如果主项目有部位信息，筛选匹配的赠送项目
        List<ItemGroupRelation> matchedRelations = itemGroupRelations.stream().filter(relation -> {
            // 如果赠送关系没有指定主项目部位，则匹配所有部位
            if (StringUtils.isBlank(relation.getMainCheckPartId())) {
                return true;
            }
            // 如果指定了主项目部位，则必须完全匹配
            return StringUtils.equals(mainCheckPartId, relation.getMainCheckPartId());
        }).collect(Collectors.toList());

        log.info("主项目{}(部位:{})匹配到{}个赠送项目", mainGroup.getItemGroupName(), mainGroup.getCheckPartName(), matchedRelations.size());

        return matchedRelations;
    }

    /**
     * 为赠送项目设置部位信息
     *
     * @param giftGroup 赠送项目
     * @param relation  赠送关系
     * @param mainGroup 主项目（用于获取主项目部位信息）
     */
    private void setGiftGroupPartInfo(CustomerRegItemGroup giftGroup, ItemGroupRelation relation, CustomerRegItemGroup mainGroup) {
        // 优先使用配置中指定的赠送项目部位
        if (StringUtils.isNotBlank(relation.getRelationCheckPartId())) {
            giftGroup.setCheckPartId(relation.getRelationCheckPartId());
            giftGroup.setCheckPartName(relation.getRelationCheckPartName());
            giftGroup.setCheckPartCode(relation.getRelationCheckPartCode());

            // 更新项目名称包含部位信息，并标记为赠送
            String originalName = giftGroup.getItemGroupName();

            giftGroup.setItemGroupName(originalName + "(赠送)");

        }
        // 如果赠送项目没有配置部位，但主项目有部位，则使用主项目的部位
        else if (StringUtils.isNotBlank(mainGroup.getCheckPartId())) {
            giftGroup.setCheckPartId(mainGroup.getCheckPartId());
            giftGroup.setCheckPartName(mainGroup.getCheckPartName());
            giftGroup.setCheckPartCode(mainGroup.getCheckPartCode());

            // 更新项目名称包含部位信息，并标记为赠送
            String originalName = giftGroup.getItemGroupName();

            giftGroup.setItemGroupName(originalName + "(赠送)");


            log.info("赠送项目{}使用主项目{}的部位信息: {}", giftGroup.getItemGroupName(), mainGroup.getItemGroupName(), mainGroup.getCheckPartName());
        } else {
            // 没有部位信息时也要标记为赠送
            String originalName = giftGroup.getItemGroupName();
            if (!originalName.contains("(赠送)")) {
                giftGroup.setItemGroupName(originalName + "(赠送)");
            }
        }
    }
}

