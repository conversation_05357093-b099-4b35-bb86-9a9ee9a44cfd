package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.entity.ItemStandard;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 体检项目
 * @Author: jeecg-boot
 * @Date: 2024-01-31
 * @Version: V1.0
 */
public interface IItemInfoService extends IService<ItemInfo> {

    void fillDepartName(ItemInfo itemInfo);

    /**
     * 添加一对多
     *
     * @param itemInfo
     * @param itemStandardList
     */
    void saveMain(ItemInfo itemInfo, List<ItemStandard> itemStandardList);

    /**
     * 修改一对多
     *
     * @param itemInfo
     * @param itemStandardList
     */
    void updateMain(ItemInfo itemInfo, List<ItemStandard> itemStandardList);

    /**
     * 删除一对多
     *
     * @param id
     */
    void delMain(String id);

    /**
     * 批量删除一对多
     *
     * @param idList
     */
    void delBatchMain(Collection<? extends Serializable> idList);

    List<ItemInfo> listItemByDepartId(String departId);

    Page<ItemInfo> listByKeyword(Page<ItemInfo> page, String keyword, String departmentId);

    List<ItemInfo> getItemByGroupId(String groupId);
}
