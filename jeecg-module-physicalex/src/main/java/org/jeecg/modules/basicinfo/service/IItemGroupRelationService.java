package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.GroupRelationVO;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;

import java.util.List;

/**
 * @Description: item_group_relation
 * @Author: jeecg-boot
 * @Date:   2024-12-02
 * @Version: V1.0
 */
public interface IItemGroupRelationService extends IService<ItemGroupRelation> {

    GroupRelationVO getRelationGroupsByMainId(String mainId);

    void saveRelationGroupBatch(GroupRelationVO groupRelationVO);

    List<CustomerRegItemGroup> getAttachGroups(List<CustomerRegItemGroup> addingItemGroups );

    List<CustomerRegItemGroup> getGiftGroups(List<CustomerRegItemGroup> addingItemGroups );

    void checkIsHaveMutexes(List<CustomerRegItemGroup> addingItemGroups ) throws Exception;

    void checkDependentGroups(List<CustomerRegItemGroup> addingItemGroups) throws Exception;

}
