package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.CommonAPI;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.ItemGroupRelation;
import org.jeecg.modules.basicinfo.entity.ItemInfo;
import org.jeecg.modules.basicinfo.entity.ItemSuit;
import org.jeecg.modules.basicinfo.entity.SuitGroup;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.mapper.ItemInfoMapper;
import org.jeecg.modules.basicinfo.mapper.ItemSuitMapper;
import org.jeecg.modules.basicinfo.service.IItemSuitService;
import org.jeecg.modules.basicinfo.service.ISuitGroupService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.basicinfo.service.IItemGroupRelationService;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CompanyTeamItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.jeecg.modules.mobile.utils.FileUrlUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 体检套餐
 * @Author: jeecg-boot
 * @Date: 2024-02-03
 * @Version: V1.0
 */
@Slf4j
@Service
@CacheConfig(cacheNames = "itemSuit")
public class ItemSuitServiceImpl extends ServiceImpl<ItemSuitMapper, ItemSuit> implements IItemSuitService {

    @Autowired
    private ItemSuitMapper itemSuitMapper;
    @Autowired
    private ItemGroupMapper itemGroupMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ISuitGroupService suitGroupService;
    @Autowired
    private CommonAPI commonAPI;
    @Autowired
    private ItemInfoMapper itemInfoMapper;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private CompanyTeamItemGroupMapper companyTeamItemGroupMapper;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private IItemGroupRelationService itemGroupRelationService;

    @Override
    public Integer getNextSort() {
        String sql = "select max(sort) from item_suit";
        Integer maxSortNo = jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
            String maxSort = rs.getString(1);
            if (maxSort == null) {
                return 1;
            }

            return Integer.parseInt(maxSort);
        });
        return maxSortNo + 1;
    }

    @Override
    public List<SuitGroup> getGroupOfSuit(String suitId, Boolean withGroup) {

        List<SuitGroup> suitGroupList = itemSuitMapper.getSuitGroup(suitId);
        if (withGroup) {
            suitGroupList.forEach(suitGroup -> {
                ItemGroup itemGroup = itemGroupMapper.selectById(suitGroup.getGroupId());
                suitGroup.setGroup(itemGroup);
            });
        }

        return suitGroupList;
    }

    @Override
    public List<ItemGroup> getItemGroupDiffSuit(List<SuitGroup> suitGroupList) {
        QueryWrapper<ItemGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable_flag", 1);
        queryWrapper.eq("del_flag", 0);
        if (suitGroupList != null && suitGroupList.size() > 0) {
            queryWrapper.notIn("id", suitGroupList.stream().map(SuitGroup::getGroupId).toArray());
        }

        List<ItemGroup> list = itemGroupMapper.selectList(queryWrapper);

        return list;
    }

    @Override
    @Transactional
    public void updateSuitGroup(String suitId, List<SuitGroup> suitGroupList) {
        jdbcTemplate.update("delete from suit_group where suit_id=?", suitId);
        //计算总价和总折后价以及差价
        BigDecimal total = new BigDecimal(0);
        BigDecimal totalAfterDis = new BigDecimal(0);
        BigDecimal diff;
        for (SuitGroup suitGroup : suitGroupList) {
            total = total.add(suitGroup.getPrice());
            totalAfterDis = totalAfterDis.add(suitGroup.getPriceAfterDis());
        }
        diff = total.subtract(totalAfterDis);
        jdbcTemplate.update("update item_suit set cost_price=?,price=?,diff_price=? where id=?", total, totalAfterDis, diff, suitId);


        suitGroupService.saveBatch(suitGroupList);
    }

    @Override
    public List<ItemGroup> getGroupOfSuit(String suitId) {
        List<ItemGroup> groupOfSuit = itemSuitMapper.getGroupOfSuit(suitId);

        groupOfSuit.forEach(itemGroup -> {
            itemGroup.setDepartment(commonAPI.translateDictFromTable("sys_depart", "depart_name", "id", itemGroup.getDepartmentId()));
        });
        return groupOfSuit;
    }

    @Cacheable(key = "#keyword", unless = "#result == null")
    @Override
    public List<ItemSuit> listByKeyword(String keyword) {
        return itemSuitMapper.getSuitByKeyword(keyword);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void evcitCache() {
    }

    @Override
    public List<ItemGroup> getGroupsBySuitId(String suitId) {
        List<ItemGroup> itemGroups = itemSuitMapper.getGroupOfSuit(suitId);
        if (CollectionUtils.isNotEmpty(itemGroups)) {
            List<String> groupIds = itemGroups.stream().map(ItemGroup::getId).collect(Collectors.toList());
            List<ItemInfo> itemInfos = itemInfoMapper.listItemByGroupIds(groupIds);
            Map<String, List<ItemInfo>> itemGroupIdMap = itemInfos.stream().collect(Collectors.groupingBy(ItemInfo::getGroupId));
            itemGroups.forEach(group -> {
                List<ItemInfo> infos = itemGroupIdMap.get(group.getId());
                if (CollectionUtils.isNotEmpty(infos)) {
                    group.setItemNames(infos.stream().map(ItemInfo::getName).collect(Collectors.joining(",")));

                }
            });

        }
        return itemGroups;
    }

    @Override
    public void removeForever(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        Long count = customerRegItemGroupMapper.selectCount(new LambdaQueryWrapper<CustomerRegItemGroup>().in(CustomerRegItemGroup::getItemSuitId, ids));
        Long companyCont = companyTeamItemGroupMapper.selectCount(new LambdaQueryWrapper<CompanyTeamItemGroup>().in(CompanyTeamItemGroup::getItemSuitId, ids));
        if (!Objects.equals(count, 0L) || (!Objects.equals(companyCont, 0L))) {
            throw new RuntimeException("选择的套餐中存在已使用的套餐，不能删除！");
        }
        jdbcTemplate.batchUpdate("delete from item_suit where id=?", new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, ids.get(i));
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });

        jdbcTemplate.batchUpdate("delete from suit_group where suit_id=?", new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, ids.get(i));
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });
    }

    @Override
    public void batchRecover(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        jdbcTemplate.batchUpdate("update item_suit set del_flag=0 where id=?", new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setString(1, ids.get(i));
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });
    }

    @Override
    public Page<ItemSuit> pageRecycleBinItemGroup(Page<ItemSuit> page, String keyword) {
        return itemSuitMapper.getDeletedSuitByKeyword(page, null, null, keyword, "1", null);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addItemGroupsToSuit(String suitId, List<SuitGroup> itemGroups) {
        // 默认处理附属项目和赠送项目，保持向后兼容
        addItemGroupsToSuit(suitId, itemGroups, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addItemGroupsToSuit(String suitId, List<SuitGroup> itemGroups, Boolean processRelatedItems) {
        // 1. 验证参数
        if (StringUtils.isEmpty(suitId) || CollectionUtils.isEmpty(itemGroups)) {
            throw new JeecgBootException("参数不能为空");
        }

        // 2. 验证套餐是否存在
        ItemSuit suit = this.getById(suitId);
        if (suit == null) {
            throw new JeecgBootException("套餐不存在");
        }

        // 3. 设置创建人信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        itemGroups.forEach(g -> {
            g.setSuitId(suitId);
            // SuitGroup没有创建人字段，这里不需要设置
        });

        // 4. 验证项目列表
        if (CollectionUtils.isEmpty(itemGroups)) {
            throw new JeecgBootException("项目列表不能为空");
        }

        // 5. 验证是否存在互斥项目
        try {
            itemGroupRelationService.checkIsHaveMutexes(convertSuitGroupsToCustomerRegItemGroups(itemGroups));
        } catch (Exception e) {
            throw new JeecgBootException("项目互斥验证失败：" + e.getMessage());
        }

        // 5.1 验证依赖项目
        try {
            itemGroupRelationService.checkDependentGroups(convertSuitGroupsToCustomerRegItemGroups(itemGroups));
        } catch (Exception e) {
            throw new JeecgBootException("项目依赖验证失败：" + e.getMessage());
        }

        // 6. 批量保存主项目
        suitGroupService.saveBatch(itemGroups);

        // 7. 获取附属项目
        List<SuitGroup> attachItemGroups = getAttachGroupsForSuit(itemGroups, suitId);
        if (CollectionUtils.isNotEmpty(attachItemGroups)) {
            itemGroups.addAll(attachItemGroups);
            //验证是否存在互斥项目
            try {
                itemGroupRelationService.checkIsHaveMutexes(convertSuitGroupsToCustomerRegItemGroups(attachItemGroups));
            } catch (Exception e) {
                throw new JeecgBootException("附属项目互斥验证失败：" + e.getMessage());
            }
            //验证附属项目的依赖关系
            try {
                itemGroupRelationService.checkDependentGroups(convertSuitGroupsToCustomerRegItemGroups(attachItemGroups));
            } catch (Exception e) {
                throw new JeecgBootException("附属项目依赖验证失败：" + e.getMessage());
            }
            suitGroupService.saveBatch(attachItemGroups);
        }

        // 8. 获取赠送项目（需要考虑主项目和附属项目的赠送项目）
        List<SuitGroup> allItemGroups = new ArrayList<>(itemGroups);
        List<SuitGroup> giftItemGroups = getGiftGroupsForSuit(allItemGroups, suitId);
        if (CollectionUtils.isNotEmpty(giftItemGroups)) {
            itemGroups.addAll(giftItemGroups);
            //验证是否存在互斥项目
            try {
                itemGroupRelationService.checkIsHaveMutexes(convertSuitGroupsToCustomerRegItemGroups(giftItemGroups));
            } catch (Exception e) {
                throw new JeecgBootException("赠送项目互斥验证失败：" + e.getMessage());
            }
            //验证赠送项目的依赖关系
            try {
                itemGroupRelationService.checkDependentGroups(convertSuitGroupsToCustomerRegItemGroups(giftItemGroups));
            } catch (Exception e) {
                throw new JeecgBootException("赠送项目依赖验证失败：" + e.getMessage());
            }
            suitGroupService.saveBatch(giftItemGroups);
        }

        // 9. 重新计算套餐价格
        recalculateSuitPrice(suitId);

        log.info("成功添加{}个检查项目到套餐，包含{}个附属项目和{}个赠送项目",
            itemGroups.size() - (attachItemGroups != null ? attachItemGroups.size() : 0) - (giftItemGroups != null ? giftItemGroups.size() : 0),
            attachItemGroups != null ? attachItemGroups.size() : 0,
            giftItemGroups != null ? giftItemGroups.size() : 0);
    }

    /**
     * 将SuitGroup转换为CustomerRegItemGroup用于关系验证
     */
    private List<CustomerRegItemGroup> convertSuitGroupsToCustomerRegItemGroups(List<SuitGroup> suitGroups) {
        return suitGroups.stream().map(sg -> {
            CustomerRegItemGroup crig = new CustomerRegItemGroup();
            crig.setItemGroupId(sg.getGroupId());
            crig.setItemGroupName(sg.getGroupName());
            crig.setDepartmentId(sg.getDepartmentId());
            crig.setDepartmentName(sg.getDepartmentName());
            crig.setPrice(sg.getPrice());
            crig.setPriceAfterDis(sg.getPriceAfterDis());
            crig.setMinDiscountRate(sg.getMinDiscountRate());
            crig.setCheckPartId(sg.getCheckPartId());
            crig.setCheckPartName(sg.getCheckPartName());
            crig.setCheckPartCode(sg.getCheckPartCode());
            // 设置必要的默认值
            crig.setCustomerRegId(""); // 套餐场景下不需要
            crig.setPayStatus("待支付");
            crig.setAddMinusFlag(1);
            crig.setType(sg.getType());
            return crig;
        }).collect(Collectors.toList());
    }

    /**
     * 获取附属项目（套餐版本）
     */
    private List<SuitGroup> getAttachGroupsForSuit(List<SuitGroup> mainGroups, String suitId) {
        List<CustomerRegItemGroup> customerRegItemGroups = convertSuitGroupsToCustomerRegItemGroups(mainGroups);
        List<CustomerRegItemGroup> attachGroups = itemGroupRelationService.getAttachGroups(customerRegItemGroups);

        return attachGroups.stream().map(ag -> {
            SuitGroup suitGroup = new SuitGroup();
            suitGroup.setSuitId(suitId);
            suitGroup.setGroupId(ag.getItemGroupId());
            suitGroup.setGroupName(ag.getItemGroupName() + "(附属)");
            suitGroup.setDepartmentId(ag.getDepartmentId());
            suitGroup.setDepartmentName(ag.getDepartmentName());
            suitGroup.setType("健康项目");
            suitGroup.setDisRate("1");
            suitGroup.setPrice(ag.getPrice() != null ? ag.getPrice() : BigDecimal.ZERO);
            suitGroup.setPriceAfterDis(ag.getPriceAfterDis() != null ? ag.getPriceAfterDis() : BigDecimal.ZERO);
            suitGroup.setMinDiscountRate(ag.getMinDiscountRate() != null ? ag.getMinDiscountRate() : BigDecimal.ONE);
            suitGroup.setPriceDisDiffAmount(BigDecimal.ZERO);
            suitGroup.setCheckPartId(ag.getCheckPartId());
            suitGroup.setCheckPartName(ag.getCheckPartName());
            suitGroup.setCheckPartCode(ag.getCheckPartCode());
            return suitGroup;
        }).collect(Collectors.toList());
    }

    /**
     * 获取赠送项目（套餐版本）
     */
    private List<SuitGroup> getGiftGroupsForSuit(List<SuitGroup> mainGroups, String suitId) {
        List<CustomerRegItemGroup> customerRegItemGroups = convertSuitGroupsToCustomerRegItemGroups(mainGroups);
        List<CustomerRegItemGroup> giftGroups = itemGroupRelationService.getGiftGroups(customerRegItemGroups);

        return giftGroups.stream().map(gg -> {
            SuitGroup suitGroup = new SuitGroup();
            suitGroup.setSuitId(suitId);
            suitGroup.setGroupId(gg.getItemGroupId());
            suitGroup.setGroupName(gg.getItemGroupName() + "(赠送)");
            suitGroup.setDepartmentId(gg.getDepartmentId());
            suitGroup.setDepartmentName(gg.getDepartmentName());
            suitGroup.setType("健康项目");
            suitGroup.setDisRate("0"); // 赠送项目折扣率为0
            suitGroup.setPrice(BigDecimal.ZERO); // 赠送项目价格为0
            suitGroup.setPriceAfterDis(BigDecimal.ZERO); // 赠送项目折后价为0
            suitGroup.setMinDiscountRate(gg.getMinDiscountRate() != null ? gg.getMinDiscountRate() : BigDecimal.ONE);
            suitGroup.setPriceDisDiffAmount(BigDecimal.ZERO);
            suitGroup.setCheckPartId(gg.getCheckPartId());
            suitGroup.setCheckPartName(gg.getCheckPartName());
            suitGroup.setCheckPartCode(gg.getCheckPartCode());
            return suitGroup;
        }).collect(Collectors.toList());
    }

    /**
     * 重新计算套餐价格
     */
    private void recalculateSuitPrice(String suitId) {
        try {
            List<SuitGroup> allSuitGroups = this.getGroupOfSuit(suitId, false);

            BigDecimal totalPrice = BigDecimal.ZERO;
            BigDecimal totalPriceAfterDis = BigDecimal.ZERO;

            for (SuitGroup suitGroup : allSuitGroups) {
                if (suitGroup.getPrice() != null) {
                    totalPrice = totalPrice.add(suitGroup.getPrice());
                }
                if (suitGroup.getPriceAfterDis() != null) {
                    totalPriceAfterDis = totalPriceAfterDis.add(suitGroup.getPriceAfterDis());
                }
            }

            BigDecimal diffPrice = totalPrice.subtract(totalPriceAfterDis);

            // 更新套餐价格
            jdbcTemplate.update(
                "UPDATE item_suit SET cost_price = ?, price = ?, diff_price = ? WHERE id = ?",
                totalPrice, totalPriceAfterDis, diffPrice, suitId
            );

            log.info("重新计算套餐价格完成，套餐ID: {}, 总价: {}, 折后价: {}, 差价: {}",
                suitId, totalPrice, totalPriceAfterDis, diffPrice);

        } catch (Exception e) {
            log.error("重新计算套餐价格失败，套餐ID: {}", suitId, e);
            // 不抛出异常，避免影响主流程
        }
    }

    @Override
    public void pageSuit4H5(Page<ItemSuit> page, String categoryId, String priceRange, String gender) {
        //整理参数
        String priceStart = null;
        String priceEnd = null;
        if (StringUtils.isNotBlank(priceRange)) {
            String[] split = priceRange.split("-");
            priceStart = split[0];
            priceEnd = split[1];
        }
        gender = StringUtils.equals(gender, "不限") ? null : StringUtils.stripToNull(gender);
        categoryId = StringUtils.equals(categoryId, "不限") ? null : StringUtils.stripToNull(categoryId);

        itemSuitMapper.pageSuit4H5(page, categoryId, priceStart, priceEnd, gender);
        String openFileUrl = sysSettingService.getValueByCode("open_file_url");
        page.getRecords().forEach(suit -> {
            suit.setSuitPicture(FileUrlUtils.replaceUrl(suit.getSuitPicture(), openFileUrl));
            //统计已卖数量
            Integer saledCount = jdbcTemplate.queryForObject("select count(1) from customer_order where suit_id=?", Integer.class, suit.getId());
            suit.setSaledCount(saledCount);
        });
    }
    @Override
    public void batchUpdateEnableFlag(List<String> idList, String enableFlag) {
        LambdaUpdateWrapper<ItemSuit> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ItemSuit::getEnableFlag, enableFlag).in(ItemSuit::getId, idList);
        update(updateWrapper);
    }
}
