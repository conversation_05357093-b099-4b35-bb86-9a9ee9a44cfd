package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.ItemGroup;
import org.jeecg.modules.basicinfo.entity.Template;
import org.jeecg.modules.basicinfo.mapper.ItemGroupMapper;
import org.jeecg.modules.basicinfo.mapper.TemplateMapper;
import org.jeecg.modules.basicinfo.service.ITemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @Description: 模版管理
 * @Author: jeecg-boot
 * @Date: 2024-06-07
 * @Version: V1.0
 */
@Service
public class TemplateServiceImpl extends ServiceImpl<TemplateMapper, Template> implements ITemplateService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private TemplateMapper templateMapper;
    @Autowired
    private ItemGroupMapper itemGroupMapper;


    @Override
    public String getUpdateTime(String id) {
        String updateTime = "";
        try {
            updateTime = jdbcTemplate.queryForObject("select update_time from template where id = ?", String.class, id);
        } catch (Exception ignored) {
        }

        return StringUtils.isNotBlank(updateTime) ? updateTime : LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    public void preDealDefaultFlag(Template template) {
        if (StringUtils.equals(template.getDefaultFlag(), "1")) {
            jdbcTemplate.update("update template set default_flag = '0' where id != ? and type=?", template.getId(), template.getType());
        }
    }

    @Override
    public Template getDefaultOfTypes(String type) {
        QueryWrapper<Template> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", type);
        queryWrapper.eq("default_flag", "1");
        queryWrapper.last("limit 1");

        return getOne(queryWrapper);
    }

    @Override
    public String getDefaultIdOfType(String type) {
        String defaultId = "";
        try {
            defaultId = jdbcTemplate.queryForObject("select id from template where type = ? and default_flag = '1' and del_flag='0' limit 1", String.class, type);
        } catch (Exception ignored) {
        }
        return StringUtils.isNotBlank(defaultId) ? defaultId : "";
    }

    @Override
    public void setTemplateGroup(Template template) {
        if (StringUtils.isBlank(template.getId()) || StringUtils.isBlank(template.getGroupId())) {
            return;
        }
        String type = template.getType();
        if (StringUtils.equals(type, ExConstants.TEMPLATE_TYPE_申请单)) {
            jdbcTemplate.update("delete from template_group where template_id = ?", template.getId());
            if (StringUtils.isNotBlank(template.getDepartmentId())) {
                List<ItemGroup> itemGroups = itemGroupMapper.selectList(new LambdaQueryWrapper<ItemGroup>().eq(ItemGroup::getDepartmentId, template.getDepartmentId()).eq(ItemGroup::getEnableFlag, 1).ne(ItemGroup::getChargeItemOnlyFlag, 1));
                if (CollectionUtils.isNotEmpty(itemGroups)) {
                    itemGroups.forEach(itemGroup -> {
                        if (!StringUtils.equals(itemGroup.getHasCheckPart(), "1"))  {
                            jdbcTemplate.update("delete from template_group where group_id = ? and template_id=?", itemGroup.getId(), template.getId());
                            jdbcTemplate.update("insert into template_group (template_id,group_id) values (?,?)", template.getId(), itemGroup.getId());
                        }
                    });
                }
            } else {
                String[] groupIds = template.getGroupId().split(",");

                for (String groupId : groupIds) {
                    String[] groupIdAndCheckPartCode = groupId.split("_");
                    if (groupIdAndCheckPartCode.length > 1) {
                        groupId = groupIdAndCheckPartCode[0];
                        jdbcTemplate.update("delete from template_group where group_id = ? and  template_id=? and check_part_code=?", groupId,template.getId(),groupIdAndCheckPartCode[1]);
                        jdbcTemplate.update("insert into template_group (template_id,group_id,check_part_code) values (?,?,?)", template.getId(), groupId, groupIdAndCheckPartCode[1]);
                    }else {
                        jdbcTemplate.update("delete from template_group where group_id = ? and template_id=?", groupId,template.getId());
                        jdbcTemplate.update("insert into template_group (template_id,group_id) values (?,?)", template.getId(), groupId);
                    }
                }
            }
        }
    }

    @Override
    public List<Template> listTemplate(String type, String examCategory, String regType, String defaultFlag, String groupId) {

        return templateMapper.listTemplate(type, examCategory, regType, defaultFlag, groupId);
    }

    @Override
    public List<String> getApplyTemplateIdsByReg(String customerRegId,Boolean reprintFlag) {
        return templateMapper.getApplyTemplateIdsByReg(customerRegId, ExConstants.TEMPLATE_TYPE_申请单, reprintFlag);
    }

    @Override
    public List<Template> listByRegType(String regType,String type) {
        return templateMapper.listByRegType(regType,type);
    }

}
