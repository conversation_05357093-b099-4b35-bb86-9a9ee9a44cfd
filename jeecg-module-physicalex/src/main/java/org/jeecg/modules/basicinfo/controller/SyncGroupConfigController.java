package org.jeecg.modules.basicinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.modules.basicinfo.entity.*;
import org.jeecg.modules.basicinfo.service.*;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.entity.CompanyTeamItemGroup;
import org.jeecg.modules.reg.service.ICompanyTeamItemGroupService;
import org.jeecg.modules.reg.service.ICompanyTeamService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(tags = "同步大项配置")
@RestController
@RequestMapping("/basicinfo/yncGroupConfig")
@Slf4j
public class SyncGroupConfigController {
    @Autowired
    private IItemGroupService itemGroupService;
    @Autowired
    private IItemGroupRelationService itemGroupRelationService;
    @Autowired
    private ICheckPartDictService checkPartDictService;
    @Autowired
    private ISuitGroupService suitGroupService;
    @Autowired
    private IItemSuitService iItemSuitService;
    @Autowired
    private ICompanyTeamItemGroupService companyTeamItemGroupService;
    @Autowired
    private ICompanyTeamService companyTeamService;
    @ApiOperation(value="同步关联项目配置表", notes="同步关联项目配置表")
    @GetMapping(value = "/groupRelations")
    public Result<String> groupRelations() {
        List<ItemGroupRelation> list= Lists.newArrayList();
        for (ItemGroup itemGroup : itemGroupService.list(new LambdaQueryWrapper<ItemGroup>().eq(ItemGroup::getHasCheckPart, "1"))) {
            List<ItemGroup> groupList = itemGroupService.getItemGroupByHisCode(itemGroup.getHisCode());
            for (ItemGroup group : groupList) {
                List<ItemGroupRelation> relationList = itemGroupRelationService.list(new LambdaQueryWrapper<ItemGroupRelation>().eq(ItemGroupRelation::getGroupId, group.getId()).eq(ItemGroupRelation::getRelation, "附属"));
                relationList.forEach(itemGroupRelation -> {
                    itemGroupRelation.setGroupId(itemGroup.getId());
                    itemGroupRelation.setGroupName(itemGroup.getName());
                    itemGroupRelation.setMainCheckPartCode(group.getCheckPartCode());
                    itemGroupRelation.setMainCheckPartName(group.getCheckPartName());
                    CheckPartDict checkPartDict = checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, group.getCheckPartCode()));
                    itemGroupRelation.setMainCheckPartId(Objects.nonNull(checkPartDict)?checkPartDict.getId():null);
                });
                list.addAll(relationList);
            }

        }
        itemGroupRelationService.updateBatchById(list);
        return Result.OK("添加成功！");
    }

    /**
     * 1个->1个
     * 多个->1个
     * 1个->多个
     * 不考虑删除（1->无）或新增（无->1）的情况
     * @param originGroup
     * @param targetGroup
     * @return
     */
    @ApiOperation(value="同步套餐项目", notes="同步套餐项目")
    @GetMapping(value = "/suitGroup")
    public Result<String> suitGroup(@RequestParam String originGroup , @RequestParam String targetGroup) {
        List<String> originGroupIds = Arrays.asList(StringUtils.split(originGroup, ","));
        List<String> targetGroupIds = Arrays.asList(StringUtils.split(targetGroup, ","));
        if (originGroupIds.size()==1){
            //1对1的情况
            ItemGroup originItemGroup = itemGroupService.getItemGroupById(originGroupIds.get(0));
            Validate.notNull(originItemGroup,"原项目不存在");
            if (targetGroupIds.size()==1) {
                ItemGroup targetItemGroup = itemGroupService.getById(targetGroupIds.get(0));
                Validate.notNull(originItemGroup,"目标项目不存在");
                List<SuitGroup> originSuitGroupList = suitGroupService.list(new LambdaQueryWrapper<SuitGroup>().eq(SuitGroup::getGroupId, originGroupIds.get(0)));
                if (originItemGroup.getPrice().compareTo(targetItemGroup.getPrice()) == 0) {
                    originSuitGroupList.forEach(suitGroup -> {
                        suitGroup.setGroupId(targetItemGroup.getId());
                        suitGroup.setGroupName(targetItemGroup.getName());
                        suitGroup.setDepartmentId(targetItemGroup.getDepartmentId());
                        suitGroup.setDepartmentName(targetItemGroup.getDepartmentName());
                        suitGroup.setCheckPartName(StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartName() : targetItemGroup.getCheckPartName());
                        suitGroup.setCheckPartCode(StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : targetItemGroup.getCheckPartCode());

                        CheckPartDict checkPartDict = checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : targetItemGroup.getCheckPartCode()));
                        suitGroup.setCheckPartId(Objects.nonNull(checkPartDict) ? checkPartDict.getId() : null);
                    });
                    suitGroupService.updateBatchById(originSuitGroupList);
                }else{
                    List<ItemSuit> itemSuitList=Lists.newArrayList();
                    originSuitGroupList.forEach(suitGroup -> {
                        suitGroup.setGroupId(targetItemGroup.getId());
                        suitGroup.setGroupName(targetItemGroup.getName());
                        suitGroup.setDepartmentId(targetItemGroup.getDepartmentId());
                        suitGroup.setDepartmentName(targetItemGroup.getDepartmentName());
                        suitGroup.setCheckPartName(StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartName() : targetItemGroup.getCheckPartName());
                        suitGroup.setCheckPartCode(StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : targetItemGroup.getCheckPartCode());

                        CheckPartDict checkPartDict = checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : targetItemGroup.getCheckPartCode()));
                        suitGroup.setCheckPartId(Objects.nonNull(checkPartDict) ? checkPartDict.getId() : null);
                        suitGroup.setPrice(targetItemGroup.getPrice());
                        BigDecimal targetPriceAfterDis = targetItemGroup.getPrice().multiply(new BigDecimal(suitGroup.getDisRate())).setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal finalDisAmount = targetPriceAfterDis.subtract(suitGroup.getPriceAfterDis().setScale(2, BigDecimal.ROUND_HALF_UP));
                        BigDecimal finalAmount = targetItemGroup.getPrice().subtract(suitGroup.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                        suitGroup.setPriceAfterDis(targetPriceAfterDis);
                        suitGroup.setPriceDisDiffAmount(targetItemGroup.getPrice().subtract(targetPriceAfterDis).setScale(2, BigDecimal.ROUND_HALF_UP));
                        ItemSuit itemSuit = iItemSuitService.getById(suitGroup.getSuitId());
                        if (itemSuit!=null){
                            itemSuit.setPrice(itemSuit.getPrice().add(finalAmount));
                            itemSuit.setCostPrice(itemSuit.getCostPrice().add(finalAmount));
                            itemSuit.setDiffPrice(itemSuit.getDiffPrice().add(finalDisAmount));
                            itemSuitList.add(itemSuit);
                        }
                    });
                    suitGroupService.updateBatchById(originSuitGroupList);
                    iItemSuitService.updateBatchById(itemSuitList);

                }
            //1对多的情况：一个原项目对应多个目标项目
            }else{
                List<ItemGroup> targetGroupList = itemGroupService.listByIds(targetGroupIds);
                // 计算targetGroupList中所有ItemGroup的price总和
                BigDecimal totalPrice = targetGroupList.stream()
                    .filter(itemGroup -> itemGroup.getPrice() != null)
                    .map(ItemGroup::getPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 获取原项目的所有SuitGroup
                List<SuitGroup> originSuitGroupList = suitGroupService.list(new LambdaQueryWrapper<SuitGroup>().eq(SuitGroup::getGroupId, originGroupIds.get(0)));

                // 删除原来的SuitGroup
                suitGroupService.remove(new LambdaQueryWrapper<SuitGroup>().eq(SuitGroup::getGroupId, originGroupIds.get(0)));

                // 为每个目标项目创建新的SuitGroup
                List<SuitGroup> newSuitGroupList = Lists.newArrayList();
                List<ItemSuit> itemSuitList = Lists.newArrayList();

                for (SuitGroup originSuitGroup : originSuitGroupList) {
                    for (ItemGroup group : targetGroupList) {
                        SuitGroup newSuitGroup = new SuitGroup();
                        BeanUtils.copyProperties(originSuitGroup, newSuitGroup);
                        newSuitGroup.setId(null); // 清空ID，让数据库自动生成
                        newSuitGroup.setGroupId(group.getId());
                        newSuitGroup.setGroupName(group.getName());
                        newSuitGroup.setDepartmentId(group.getDepartmentId());
                        newSuitGroup.setDepartmentName(group.getDepartmentName());
                        newSuitGroup.setCheckPartName(StringUtils.equals(group.getHasCheckPart(), "1") ? originItemGroup.getCheckPartName() : group.getCheckPartName());
                        newSuitGroup.setCheckPartCode(StringUtils.equals(group.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : group.getCheckPartCode());

                        CheckPartDict checkPartDict = checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, StringUtils.equals(group.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : group.getCheckPartCode()));
                        newSuitGroup.setCheckPartId(Objects.nonNull(checkPartDict) ? checkPartDict.getId() : null);
                         newSuitGroup.setPrice(group.getPrice());

                        // 重新计算折后价格
                        BigDecimal targetPriceAfterDis = group.getPrice().multiply(new BigDecimal(newSuitGroup.getDisRate())).setScale(2, BigDecimal.ROUND_HALF_UP);
                        newSuitGroup.setPriceAfterDis(targetPriceAfterDis);
                        newSuitGroup.setPriceDisDiffAmount(group.getPrice().subtract(targetPriceAfterDis).setScale(2, BigDecimal.ROUND_HALF_UP));

                        newSuitGroupList.add(newSuitGroup);
                    }

                    // 更新ItemSuit的价格（价格差异 = 新总价 - 原价格）
                    BigDecimal priceDiff = totalPrice.subtract(originItemGroup.getPrice());
                    BigDecimal disPriceDiff = totalPrice.multiply(new BigDecimal(originSuitGroup.getDisRate())).setScale(2, BigDecimal.ROUND_HALF_UP)
                            .subtract(originSuitGroup.getPriceAfterDis());

                    ItemSuit itemSuit = iItemSuitService.getById(originSuitGroup.getSuitId());
                    itemSuit.setPrice(itemSuit.getPrice().add(priceDiff));
                    itemSuit.setCostPrice(itemSuit.getCostPrice().add(priceDiff));
                    itemSuit.setDiffPrice(itemSuit.getDiffPrice().add(disPriceDiff));
                    itemSuitList.add(itemSuit);
                }

                // 批量保存新的SuitGroup
                suitGroupService.saveBatch(newSuitGroupList);
                // 更新ItemSuit
                iItemSuitService.updateBatchById(itemSuitList);
            }

        }else{
            //多对1的情况：多个原项目对应一个目标项目
            if (targetGroupIds.size() == 1) {
                List<ItemGroup> originGroupList = itemGroupService.listByIds(originGroupIds);
                ItemGroup targetItemGroup = itemGroupService.getById(targetGroupIds.get(0));
                Validate.notNull(targetItemGroup, "目标项目不存在");

                // 获取所有原项目组的SuitGroup，按SuitId分组
                Map<String, List<SuitGroup>> suitGroupMap = Maps.newHashMap();
                for (String originGroupId : originGroupIds) {
                    List<SuitGroup> suitGroups = suitGroupService.list(new LambdaQueryWrapper<SuitGroup>().eq(SuitGroup::getGroupId, originGroupId));
                    for (SuitGroup suitGroup : suitGroups) {
                        suitGroupMap.computeIfAbsent(suitGroup.getSuitId(), k -> Lists.newArrayList()).add(suitGroup);
                    }
                }

                // 删除所有原项目的SuitGroup
                for (String originGroupId : originGroupIds) {
                    suitGroupService.remove(new LambdaQueryWrapper<SuitGroup>().eq(SuitGroup::getGroupId, originGroupId));
                }

                // 为每个套餐创建一个新的SuitGroup（合并到目标项目）
                List<SuitGroup> newSuitGroupList = Lists.newArrayList();
                List<ItemSuit> itemSuitList = Lists.newArrayList();

                for (Map.Entry<String, List<SuitGroup>> entry : suitGroupMap.entrySet()) {
                    String suitId = entry.getKey();
                    List<SuitGroup> suitGroups = entry.getValue();

                    // 使用第一个SuitGroup作为模板
                    SuitGroup templateSuitGroup = suitGroups.get(0);

                    // 创建新的SuitGroup
                    SuitGroup newSuitGroup = new SuitGroup();
                    BeanUtils.copyProperties(templateSuitGroup, newSuitGroup);
                    newSuitGroup.setId(null); // 清空ID，让数据库自动生成
                    newSuitGroup.setGroupId(targetItemGroup.getId());
                    newSuitGroup.setGroupName(targetItemGroup.getName());
                    newSuitGroup.setDepartmentId(targetItemGroup.getDepartmentId());
                    newSuitGroup.setDepartmentName(targetItemGroup.getDepartmentName());
                    newSuitGroup.setCheckPartName(targetItemGroup.getCheckPartName());
                    newSuitGroup.setCheckPartCode(targetItemGroup.getCheckPartCode());
                    newSuitGroup.setCheckPartId(checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, targetItemGroup.getCheckPartCode())).getId());
                    newSuitGroup.setPrice(targetItemGroup.getPrice());

                    // 重新计算折后价格
                    BigDecimal targetPriceAfterDis = targetItemGroup.getPrice().multiply(new BigDecimal(newSuitGroup.getDisRate())).setScale(2, BigDecimal.ROUND_HALF_UP);
                    newSuitGroup.setPriceAfterDis(targetPriceAfterDis);
                    newSuitGroup.setPriceDisDiffAmount(targetItemGroup.getPrice().subtract(targetPriceAfterDis).setScale(2, BigDecimal.ROUND_HALF_UP));

                    newSuitGroupList.add(newSuitGroup);

                    // 计算原多个项目的总价格和折后总价格
                    BigDecimal originSuitTotalPrice = suitGroups.stream()
                        .map(SuitGroup::getPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal originSuitTotalPriceAfterDis = suitGroups.stream()
                        .map(SuitGroup::getPriceAfterDis)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 更新ItemSuit的价格（价格差异 = 目标价格 - 原总价格）
                    BigDecimal priceDiff = targetItemGroup.getPrice().subtract(originSuitTotalPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal disPriceDiff = targetPriceAfterDis.subtract(originSuitTotalPriceAfterDis).setScale(2, BigDecimal.ROUND_HALF_UP);

                    ItemSuit itemSuit = iItemSuitService.getById(suitId);
                    itemSuit.setPrice(itemSuit.getPrice().add(priceDiff));
                    itemSuit.setCostPrice(itemSuit.getCostPrice().add(priceDiff));
                    itemSuit.setDiffPrice(itemSuit.getDiffPrice().add(disPriceDiff));
                    itemSuitList.add(itemSuit);
                }

                // 批量保存新的SuitGroup
                suitGroupService.saveBatch(newSuitGroupList);
                // 更新ItemSuit
                iItemSuitService.updateBatchById(itemSuitList);

            } else {
                //多对多的情况 - 暂不处理，可以根据业务需求后续扩展
                log.warn("多对多的情况暂不支持，originGroupIds: {}, targetGroupIds: {}", originGroupIds, targetGroupIds);
                return Result.error("多对多的情况暂不支持");
            }
        }

        return Result.OK("添加成功！");
    }
    /**
     * 1个->1个
     * 多个->1个
     * 1个->多个
     * 不考虑删除（1->无）或新增（无->1）的情况
     * @param originGroup
     * @param targetGroup
     * @return
     */
    @ApiOperation(value="同步团检分组项目", notes="同步团检分组项目")
    @GetMapping(value = "/companyGroup")
    public Result<String> companyGroup(@RequestParam String originGroup , @RequestParam String targetGroup) {
        List<String> originGroupIds = Arrays.asList(StringUtils.split(originGroup, ","));
        List<String> targetGroupIds = Arrays.asList(StringUtils.split(targetGroup, ","));
        if (originGroupIds.size()==1){
            //1对1的情况
            ItemGroup originItemGroup = itemGroupService.getById(originGroupIds.get(0));
            Validate.notNull(originItemGroup,"原项目不存在");
            if (targetGroupIds.size()==1) {
                ItemGroup targetItemGroup = itemGroupService.getById(targetGroupIds.get(0));
                Validate.notNull(originItemGroup,"目标项目不存在");
                List<CompanyTeamItemGroup> originCompanyGroupList = companyTeamItemGroupService.list(new LambdaQueryWrapper<CompanyTeamItemGroup>().eq(CompanyTeamItemGroup::getItemGroupId, originGroupIds.get(0)));
                if (originItemGroup.getPrice().compareTo(targetItemGroup.getPrice())==0) {
                    originCompanyGroupList.forEach(companyTeamItemGroup -> {
                        companyTeamItemGroup.setItemGroupId(targetItemGroup.getId());
                        companyTeamItemGroup.setItemGroupName(targetItemGroup.getName());
                        companyTeamItemGroup.setDepartmentId(targetItemGroup.getDepartmentId());
                        companyTeamItemGroup.setDepartmentName(targetItemGroup.getDepartmentName());
                        companyTeamItemGroup.setCheckPartName(StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartName() : targetItemGroup.getCheckPartName());
                        companyTeamItemGroup.setCheckPartCode(StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : targetItemGroup.getCheckPartCode());

                        CheckPartDict checkPartDict = checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : targetItemGroup.getCheckPartCode()));
                        companyTeamItemGroup.setCheckPartId(Objects.nonNull(checkPartDict) ? checkPartDict.getId() : null);
                    });
                    companyTeamItemGroupService.updateBatchById(originCompanyGroupList);
                }else{
                    List<CompanyTeam> companyTeamList=Lists.newArrayList();
                    originCompanyGroupList.forEach(companyTeamItemGroup -> {
                        companyTeamItemGroup.setItemGroupId(targetItemGroup.getId());
                        companyTeamItemGroup.setItemGroupName(targetItemGroup.getName());
                        companyTeamItemGroup.setDepartmentId(targetItemGroup.getDepartmentId());
                        companyTeamItemGroup.setDepartmentName(targetItemGroup.getDepartmentName());
                        companyTeamItemGroup.setCheckPartName(StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartName() : targetItemGroup.getCheckPartName());
                        companyTeamItemGroup.setCheckPartCode(StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : targetItemGroup.getCheckPartCode());

                        CheckPartDict checkPartDict = checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, StringUtils.equals(targetItemGroup.getHasCheckPart(), "1") ? originItemGroup.getCheckPartCode() : targetItemGroup.getCheckPartCode()));
                        companyTeamItemGroup.setCheckPartId(Objects.nonNull(checkPartDict) ? checkPartDict.getId() : null);
                        companyTeamItemGroup.setPrice(targetItemGroup.getPrice());
                        BigDecimal targetPriceAfterDis = targetItemGroup.getPrice().multiply(companyTeamItemGroup.getDisRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
                        BigDecimal finalAmount = targetItemGroup.getPrice().subtract(companyTeamItemGroup.getPrice().setScale(2, BigDecimal.ROUND_HALF_UP));
                        companyTeamItemGroup.setPriceAfterDis(targetPriceAfterDis);
                        companyTeamItemGroup.setPriceDisDiffAmount(targetItemGroup.getPrice().subtract(targetPriceAfterDis).setScale(2, BigDecimal.ROUND_HALF_UP));
                        CompanyTeam companyTeam = companyTeamService.getById(companyTeamItemGroup.getTeamId());
                        companyTeam.setTeamPrice(companyTeam.getTeamPrice().add(finalAmount));
                        companyTeam.setTeamDiscountPrice(companyTeam.getTeamDiscountPrice().add(finalAmount));
                        companyTeamList.add(companyTeam);
                    });
                    companyTeamItemGroupService.updateBatchById(originCompanyGroupList);
                    companyTeamService.updateBatchById(companyTeamList);

                }
                //1对多的情况：一个原项目对应多个目标项目
            }else{
                List<ItemGroup> targetGroupList = itemGroupService.listByIds(targetGroupIds);
                // 计算targetGroupList中所有ItemGroup的price总和
                BigDecimal totalPrice = targetGroupList.stream()
                        .filter(itemGroup -> itemGroup.getPrice() != null)
                        .map(ItemGroup::getPrice)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 获取原项目的所有CompanyTeamItemGroup
                List<CompanyTeamItemGroup> originCompanyGroupList = companyTeamItemGroupService.list(new LambdaQueryWrapper<CompanyTeamItemGroup>().eq(CompanyTeamItemGroup::getItemGroupId, originGroupIds.get(0)));

                // 删除原来的CompanyTeamItemGroup
                companyTeamItemGroupService.remove(new LambdaQueryWrapper<CompanyTeamItemGroup>().eq(CompanyTeamItemGroup::getItemGroupId, originGroupIds.get(0)));

                // 为每个目标项目创建新的CompanyTeamItemGroup
                List<CompanyTeamItemGroup> newCompanyGroupList = Lists.newArrayList();
                List<CompanyTeam> companyTeamList = Lists.newArrayList();

                for (CompanyTeamItemGroup originCompanyGroup : originCompanyGroupList) {
                    for (ItemGroup itemGroup : targetGroupList) {
                        CompanyTeamItemGroup newCompanyGroup = new CompanyTeamItemGroup();
                        BeanUtils.copyProperties(originCompanyGroup, newCompanyGroup);
                        newCompanyGroup.setId(null); // 清空ID，让数据库自动生成
                        newCompanyGroup.setItemGroupId(itemGroup.getId());
                        newCompanyGroup.setItemGroupName(itemGroup.getName());
                        newCompanyGroup.setDepartmentId(itemGroup.getDepartmentId());
                        newCompanyGroup.setDepartmentName(itemGroup.getDepartmentName());
                        newCompanyGroup.setCheckPartName(itemGroup.getCheckPartName());
                        newCompanyGroup.setCheckPartCode(itemGroup.getCheckPartCode());
                        newCompanyGroup.setCheckPartId(checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, itemGroup.getCheckPartCode())).getId());
                        newCompanyGroup.setPrice(itemGroup.getPrice());

                        // 重新计算折后价格
                        BigDecimal targetPriceAfterDis = itemGroup.getPrice().multiply(newCompanyGroup.getDisRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
                        newCompanyGroup.setPriceAfterDis(targetPriceAfterDis);
                        newCompanyGroup.setPriceDisDiffAmount(itemGroup.getPrice().subtract(targetPriceAfterDis).setScale(2, BigDecimal.ROUND_HALF_UP));

                        newCompanyGroupList.add(newCompanyGroup);
                    }

                    // 更新CompanyTeam的价格（价格差异 = 新总价 - 原价格）
                    BigDecimal priceDiff = totalPrice.subtract(originItemGroup.getPrice());
                    BigDecimal disPriceDiff = totalPrice.multiply(originCompanyGroup.getDisRate()).setScale(2, BigDecimal.ROUND_HALF_UP)
                            .subtract(originCompanyGroup.getPriceAfterDis());

                    CompanyTeam companyTeam = companyTeamService.getById(originCompanyGroup.getTeamId());
                    companyTeam.setTeamPrice(companyTeam.getTeamPrice().add(priceDiff));
                    companyTeam.setTeamDiscountPrice(companyTeam.getTeamDiscountPrice().add(disPriceDiff));
                    companyTeamList.add(companyTeam);
                }

                // 批量保存新的CompanyTeamItemGroup
                companyTeamItemGroupService.saveBatch(newCompanyGroupList);
                // 更新CompanyTeam
                companyTeamService.updateBatchById(companyTeamList);
            }

        }else{
            //多对1的情况：多个原项目对应一个目标项目
            if (targetGroupIds.size() == 1) {
                List<ItemGroup> originGroupList = itemGroupService.listByIds(originGroupIds);
                ItemGroup targetItemGroup = itemGroupService.getById(targetGroupIds.get(0));
                Validate.notNull(targetItemGroup, "目标项目不存在");

                // 获取所有原项目组的CompanyTeamItemGroup，按TeamId分组
                Map<String, List<CompanyTeamItemGroup>> companyGroupMap = Maps.newHashMap();
                for (String originGroupId : originGroupIds) {
                    List<CompanyTeamItemGroup> companyGroups = companyTeamItemGroupService.list(new LambdaQueryWrapper<CompanyTeamItemGroup>().eq(CompanyTeamItemGroup::getItemGroupId, originGroupId));
                    for (CompanyTeamItemGroup companyGroup : companyGroups) {
                        companyGroupMap.computeIfAbsent(companyGroup.getTeamId(), k -> Lists.newArrayList()).add(companyGroup);
                    }
                }

                // 删除所有原项目的CompanyTeamItemGroup
                for (String originGroupId : originGroupIds) {
                    companyTeamItemGroupService.remove(new LambdaQueryWrapper<CompanyTeamItemGroup>().eq(CompanyTeamItemGroup::getItemGroupId, originGroupId));
                }

                // 为每个团队创建一个新的CompanyTeamItemGroup（合并到目标项目）
                List<CompanyTeamItemGroup> newCompanyGroupList = Lists.newArrayList();
                List<CompanyTeam> companyTeamList = Lists.newArrayList();

                for (Map.Entry<String, List<CompanyTeamItemGroup>> entry : companyGroupMap.entrySet()) {
                    String teamId = entry.getKey();
                    List<CompanyTeamItemGroup> companyGroups = entry.getValue();

                    // 使用第一个CompanyTeamItemGroup作为模板
                    CompanyTeamItemGroup templateCompanyGroup = companyGroups.get(0);

                    // 创建新的CompanyTeamItemGroup
                    CompanyTeamItemGroup newCompanyGroup = new CompanyTeamItemGroup();
                    BeanUtils.copyProperties(templateCompanyGroup, newCompanyGroup);
                    newCompanyGroup.setId(null); // 清空ID，让数据库自动生成
                    newCompanyGroup.setItemGroupId(targetItemGroup.getId());
                    newCompanyGroup.setItemGroupName(targetItemGroup.getName());
                    newCompanyGroup.setDepartmentId(targetItemGroup.getDepartmentId());
                    newCompanyGroup.setDepartmentName(targetItemGroup.getDepartmentName());
                    newCompanyGroup.setCheckPartName(targetItemGroup.getCheckPartName());
                    newCompanyGroup.setCheckPartCode(targetItemGroup.getCheckPartCode());
                    newCompanyGroup.setCheckPartId(checkPartDictService.getOne(new LambdaQueryWrapper<CheckPartDict>().eq(CheckPartDict::getCode, targetItemGroup.getCheckPartCode())).getId());
                    newCompanyGroup.setPrice(targetItemGroup.getPrice());

                    // 重新计算折后价格
                    BigDecimal targetPriceAfterDis = targetItemGroup.getPrice().multiply(newCompanyGroup.getDisRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    newCompanyGroup.setPriceAfterDis(targetPriceAfterDis);
                    newCompanyGroup.setPriceDisDiffAmount(targetItemGroup.getPrice().subtract(targetPriceAfterDis).setScale(2, BigDecimal.ROUND_HALF_UP));

                    newCompanyGroupList.add(newCompanyGroup);

                    // 计算原多个项目的总价格和折后总价格
                    BigDecimal originCompanyTotalPrice = companyGroups.stream()
                            .map(CompanyTeamItemGroup::getPrice)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal originCompanyTotalPriceAfterDis = companyGroups.stream()
                            .map(CompanyTeamItemGroup::getPriceAfterDis)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 更新CompanyTeam的价格（价格差异 = 目标价格 - 原总价格）
                    BigDecimal priceDiff = targetItemGroup.getPrice().subtract(originCompanyTotalPrice).setScale(2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal disPriceDiff = targetPriceAfterDis.subtract(originCompanyTotalPriceAfterDis).setScale(2, BigDecimal.ROUND_HALF_UP);

                    CompanyTeam companyTeam = companyTeamService.getById(teamId);
                    companyTeam.setTeamPrice(companyTeam.getTeamPrice().add(priceDiff));
                    companyTeam.setTeamDiscountPrice(companyTeam.getTeamDiscountPrice().add(disPriceDiff));
                    companyTeamList.add(companyTeam);
                }

                // 批量保存新的CompanyTeamItemGroup
                companyTeamItemGroupService.saveBatch(newCompanyGroupList);
                // 更新CompanyTeam
                companyTeamService.updateBatchById(companyTeamList);

            } else {
                //多对多的情况 - 暂不处理，可以根据业务需求后续扩展
                log.warn("多对多的情况暂不支持，originGroupIds: {}, targetGroupIds: {}", originGroupIds, targetGroupIds);
                return Result.error("多对多的情况暂不支持");
            }
        }

        return Result.OK("添加成功！");
    }
}
