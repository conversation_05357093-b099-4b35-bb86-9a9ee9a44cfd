package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: zy_item_map
 * @Author: jeecg-boot
 * @Date:   2025-08-15
 * @Version: V1.0
 */
@Data
@TableName("zy_item_map")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="zy_item_map对象", description="zy_item_map")
public class ZyItemMap implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**平台项目代码*/
	@Excel(name = "平台项目代码", width = 15)
    @ApiModelProperty(value = "平台项目代码")
    private String platCode;
	/**平台项目名称*/
	@Excel(name = "平台项目名称", width = 15)
    @ApiModelProperty(value = "平台项目名称")
    private String platName;
	/**院内项目代码*/
	@Excel(name = "院内项目代码", width = 15)
    @ApiModelProperty(value = "院内项目代码")
    private String hisCode;
	/**院内项目名称*/
	@Excel(name = "院内项目名称", width = 15)
    @ApiModelProperty(value = "院内项目名称")
    private String hisName;
	/**平台项目分类*/
	@Excel(name = "平台项目分类", width = 15)
    @ApiModelProperty(value = "平台项目分类")
    private String platCategory;
}
