package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.ZyItemMap;
import org.jeecg.modules.occu.service.IZyItemMapService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: zy_item_map
 * @Author: jeecg-boot
 * @Date:   2025-08-15
 * @Version: V1.0
 */
@Api(tags="zy_item_map")
@RestController
@RequestMapping("/occu/zyItemMap")
@Slf4j
public class ZyItemMapController extends JeecgController<ZyItemMap, IZyItemMapService> {
	@Autowired
	private IZyItemMapService zyItemMapService;
	
	/**
	 * 分页列表查询
	 *
	 * @param zyItemMap
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "zy_item_map-分页列表查询")
	@ApiOperation(value="zy_item_map-分页列表查询", notes="zy_item_map-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ZyItemMap>> queryPageList(ZyItemMap zyItemMap,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ZyItemMap> queryWrapper = QueryGenerator.initQueryWrapper(zyItemMap, req.getParameterMap());
		Page<ZyItemMap> page = new Page<ZyItemMap>(pageNo, pageSize);
		IPage<ZyItemMap> pageList = zyItemMapService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param zyItemMap
	 * @return
	 */
	@AutoLog(value = "zy_item_map-添加")
	@ApiOperation(value="zy_item_map-添加", notes="zy_item_map-添加")
	@RequiresPermissions("occu:zy_item_map:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZyItemMap zyItemMap) {
		zyItemMapService.save(zyItemMap);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zyItemMap
	 * @return
	 */
	@AutoLog(value = "zy_item_map-编辑")
	@ApiOperation(value="zy_item_map-编辑", notes="zy_item_map-编辑")
	@RequiresPermissions("occu:zy_item_map:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZyItemMap zyItemMap) {
		zyItemMapService.updateById(zyItemMap);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "zy_item_map-通过id删除")
	@ApiOperation(value="zy_item_map-通过id删除", notes="zy_item_map-通过id删除")
	@RequiresPermissions("occu:zy_item_map:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zyItemMapService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "zy_item_map-批量删除")
	@ApiOperation(value="zy_item_map-批量删除", notes="zy_item_map-批量删除")
	@RequiresPermissions("occu:zy_item_map:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.zyItemMapService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "zy_item_map-通过id查询")
	@ApiOperation(value="zy_item_map-通过id查询", notes="zy_item_map-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZyItemMap> queryById(@RequestParam(name="id",required=true) String id) {
		ZyItemMap zyItemMap = zyItemMapService.getById(id);
		if(zyItemMap==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zyItemMap);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zyItemMap
    */
    @RequiresPermissions("occu:zy_item_map:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyItemMap zyItemMap) {
        return super.exportXls(request, zyItemMap, ZyItemMap.class, "zy_item_map");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_item_map:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyItemMap.class);
    }

}
