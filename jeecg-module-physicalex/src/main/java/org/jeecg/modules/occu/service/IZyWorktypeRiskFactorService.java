package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 工种危害因素关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-08
 * @Version: V1.0
 */
public interface IZyWorktypeRiskFactorService extends IService<ZyRiskFactorWorktype> {

    /**
     * 通过工种ID查询关联的危害因素列表
     * @param worktypeId 工种ID
     * @return List<ZyRiskFactorWorktype>
     */
    List<ZyRiskFactorWorktype> selectByWorktypeId(String worktypeId);
}
