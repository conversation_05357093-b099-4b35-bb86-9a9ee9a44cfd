package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 危害因素工种关联表
 * @Author: jeecg-boot
 * @Date: 2025-02-18
 * @Version: V1.0
 */
@Data
@TableName("zy_risk_factor_worktype")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="zy_risk_factor_worktype对象", description="危害因素工种关联表")
public class ZyRiskFactorWorktype implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private String id;

    /**危害因素ID*/
    @Excel(name = "危害因素ID", width = 15)
    @ApiModelProperty(value = "危害因素ID")
    private String factorId;

    /**危害因素代码*/
    @Excel(name = "危害因素代码", width = 15)
    @ApiModelProperty(value = "危害因素代码")
    private String factorCode;

    /**危害因素ID*/
    @Excel(name = "危害因素名称", width = 15)
    @ApiModelProperty(value = "危害因素名称")
    private String factorName;

    /**工种ID*/
    @Excel(name = "工种ID", width = 15)
    @ApiModelProperty(value = "工种ID")
    private String worktypeId;

    /**工种代码*/
    @Excel(name = "工种代码", width = 15)
    @ApiModelProperty(value = "工种代码")
    private String worktypeCode;

    /**工种代码*/
    @Excel(name = "工种名称", width = 15)
    @ApiModelProperty(value = "工种名称")
    private String worktypeName;

    /**排序*/
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer seq;
}
