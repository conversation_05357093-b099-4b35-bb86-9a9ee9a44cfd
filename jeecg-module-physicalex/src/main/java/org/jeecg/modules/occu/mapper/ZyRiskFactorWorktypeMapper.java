package org.jeecg.modules.occu.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 危害因素工种关联表
 * @Author: jeecg-boot
 * @Date: 2025-02-18
 * @Version: V1.0
 */
public interface ZyRiskFactorWorktypeMapper extends BaseMapper<ZyRiskFactorWorktype> {

    /**
     * 通过危害因素ID删除关联数据
     *
     * @param factorId 危害因素ID
     * @return boolean
     */
    public boolean deleteByMainId(@Param("factorId") String factorId);

    /**
     * 通过危害因素ID查询关联数据
     *
     * @param factorId 危害因素ID
     * @return List<ZyRiskFactorWorktype>
     */
    public List<ZyRiskFactorWorktype> selectByMainId(@Param("factorId") String factorId);
}
