package org.jeecg.modules.occu.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 工种危害因素关联表
 * @Author: jeecg-boot
 * @Date: 2025-01-08
 * @Version: V1.0
 */
public interface ZyWorktypeRiskFactorMapper extends BaseMapper<ZyRiskFactorWorktype> {

    /**
     * 通过工种ID删除关联数据
     *
     * @param worktypeId 工种ID
     * @return boolean
     */
    public boolean deleteByWorktypeId(@Param("worktypeId") String worktypeId);

    /**
     * 通过工种ID查询关联数据
     *
     * @param worktypeId 工种ID
     * @return List<ZyRiskFactorWorktype>
     */
    public List<ZyRiskFactorWorktype> selectByWorktypeId(@Param("worktypeId") String worktypeId);

    /**
     * 通过工种ID和危害因素ID查询关联数据
     *
     * @param worktypeId 工种ID
     * @param riskFactorId 危害因素ID
     * @return List<ZyRiskFactorWorktype>
     */
    public List<ZyRiskFactorWorktype> selectByWorktypeIdAndRiskFactorId(@Param("worktypeId") String worktypeId, @Param("riskFactorId") String riskFactorId);

    /**
     * 批量删除工种和危害因素的关联
     *
     * @param worktypeId 工种ID
     * @param riskFactorIds 危害因素ID列表
     * @return boolean
     */
    public boolean deleteByWorktypeIdAndRiskFactorIds(@Param("worktypeId") String worktypeId, @Param("riskFactorIds") List<String> riskFactorIds);

    /**
     * 获取所有可用的危害因素
     *
     * @return List<?>
     */
    public List<ZyRiskFactor> selectAvailableRiskFactors();

    /**
     * 根据危害因素ID查询危害因素详细信息
     *
     * @param riskFactorId 危害因素ID
     * @return ZyRiskFactor
     */
    public ZyRiskFactor selectRiskFactorById(@Param("riskFactorId") String riskFactorId);
}
