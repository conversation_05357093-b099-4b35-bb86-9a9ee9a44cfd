<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.occu.mapper.ZyWorktypeRiskFactorMapper">

    <!-- 通过工种ID删除关联数据 -->
    <delete id="deleteByWorktypeId">
        DELETE FROM zy_risk_factor_worktype WHERE worktype_id = #{worktypeId}
    </delete>

    <!-- 通过工种ID查询关联数据 -->
    <select id="selectByWorktypeId" resultType="org.jeecg.modules.occu.entity.ZyRiskFactorWorktype">
        SELECT * FROM zy_risk_factor_worktype WHERE worktype_id = #{worktypeId}
    </select>

    <!-- 通过工种ID和危害因素ID查询关联数据 -->
    <select id="selectByWorktypeIdAndRiskFactorId" resultType="org.jeecg.modules.occu.entity.ZyRiskFactorWorktype">
        SELECT * FROM zy_risk_factor_worktype
        WHERE worktype_id = #{worktypeId} AND factor_id = #{riskFactorId}
    </select>

    <!-- 批量删除工种和危害因素的关联 -->
    <delete id="deleteByWorktypeIdAndRiskFactorIds">
        DELETE FROM zy_risk_factor_worktype
        WHERE worktype_id = #{worktypeId}
        AND factor_id IN
        <foreach collection="riskFactorIds" item="riskFactorId" open="(" separator="," close=")">
            #{riskFactorId}
        </foreach>
    </delete>

    <!-- 获取所有可用的危害因素 -->
    <select id="selectAvailableRiskFactors" resultType="org.jeecg.modules.occu.entity.ZyRiskFactor">
        SELECT id, name, code FROM zy_risk_factor
        WHERE valid = 1
        ORDER BY sort, name
    </select>
    <select id="selectRiskFactorById" resultType="org.jeecg.modules.occu.entity.ZyRiskFactor">
        SELECT id, name, code FROM zy_risk_factor
        WHERE id = #{riskFactorId}
    </select>

</mapper>
