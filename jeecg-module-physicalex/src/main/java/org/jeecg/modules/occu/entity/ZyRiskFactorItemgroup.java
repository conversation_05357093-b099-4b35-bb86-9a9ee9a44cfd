package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import java.util.Date;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.UnsupportedEncodingException;

/**
 * @Description: 危害因素必检项目
 * @Author: jeecg-boot
 * @Date:   2025-02-18
 * @Version: V1.0
 */
@ApiModel(value="zy_risk_factor_itemgroup对象", description="危害因素必检项目")
@Data
@TableName("zy_risk_factor_itemgroup")
public class ZyRiskFactorItemgroup implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**危害因素ID*/
    @ApiModelProperty(value = "危害因素ID")
    private java.lang.String factorId;
	/**岗位*/
	@Excel(name = "岗位", width = 15, dicCode = "job_status")
	@Dict(dicCode = "job_status")
    @ApiModelProperty(value = "岗位")
    private java.lang.String post;
	/**项目组合*/
	@Excel(name = "项目组合", width = 15, dictTable = "item_group where enable_flag=1 and del_flag=0", dicText = "name", dicCode = "id")
	@Dict(dictTable = "item_group where enable_flag=1 and del_flag=0", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "项目组合")
    private java.lang.String itemgroupId;
	/**排序号*/
	@Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private java.lang.Integer seq;
    /**检查部位*/
    @Excel(name = "检查部位", width = 15)
    @ApiModelProperty(value = "检查部位")
    private String checkPartCode;
}
