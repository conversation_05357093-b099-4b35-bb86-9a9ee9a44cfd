package org.jeecg.modules.electronicbill.service;

/**
 * 电子票据自动冲红服务接口
 * 
 * @author: auto-generated
 * @date: 2025-01-24
 */
public interface IElectronicBillAutoWriteoffService {

    /**
     * 退款后自动冲红电子票据
     * 
     * @param billId 支付单ID
     * @param refundReason 退款原因
     * @param operator 操作人
     * @throws Exception 冲红异常
     */
    void autoWriteoffAfterRefund(String billId, String refundReason, String operator) throws Exception;

    /**
     * 批量退款后自动冲红电子票据
     * 
     * @param billIds 支付单ID列表
     * @param refundReason 退款原因
     * @param operator 操作人
     * @throws Exception 冲红异常
     */
    void batchAutoWriteoffAfterRefund(java.util.List<String> billIds, String refundReason, String operator) throws Exception;

    /**
     * 检查是否需要自动冲红
     * 
     * @param billId 支付单ID
     * @return 是否需要冲红
     */
    boolean needAutoWriteoff(String billId);
    
}