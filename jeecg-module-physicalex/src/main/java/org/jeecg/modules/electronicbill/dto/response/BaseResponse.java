package org.jeecg.modules.electronicbill.dto.response;

import lombok.Getter;
import lombok.Setter;

/**
 * 电子票据API响应基类
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class BaseResponse {

    /**
     * 接口调用是否成功
     */
    private Boolean success;

    /**
     * 响应结果代码
     * "0000" - 成功
     * 其他 - 错误代码
     */
    private String result;

    /**
     * 错误信息描述
     */
    private String errorMessage;

    /**
     * 响应时间戳
     */
    private Long timestamp;

    /**
     * 请求跟踪ID
     */
    private String traceId;

    public BaseResponse() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 创建成功响应
     * 
     * @return 成功响应对象
     */
    public static BaseResponse success() {
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        response.setResult("0000");
        response.setErrorMessage("操作成功");
        return response;
    }

    /**
     * 创建失败响应
     * 
     * @param result 错误代码
     * @param errorMessage 错误信息
     * @return 失败响应对象
     */
    public static BaseResponse failure(String result, String errorMessage) {
        BaseResponse response = new BaseResponse();
        response.setSuccess(false);
        response.setResult(result);
        response.setErrorMessage(errorMessage);
        return response;
    }

    /**
     * 判断响应是否成功
     * 
     * @return true-成功, false-失败
     */
    public boolean isSuccess() {
        return Boolean.TRUE.equals(success) && "0000".equals(result);
    }
}