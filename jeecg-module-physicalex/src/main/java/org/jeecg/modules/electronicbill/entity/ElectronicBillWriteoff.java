package org.jeecg.modules.electronicbill.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 电子票据冲红记录表
 * 
 * <AUTHOR>
 */
@Data
@TableName("electronic_bill_writeoff")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "electronic_bill_writeoff对象", description = "电子票据冲红记录表")
public class ElectronicBillWriteoff implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "原电子票据ID", width = 15)
    @ApiModelProperty(value = "原电子票据ID")
    private String originalBillId;

    @Excel(name = "原电子票据代码", width = 20)
    @ApiModelProperty(value = "原电子票据代码")
    private String originalBillBatchCode;

    @Excel(name = "原电子票据号码", width = 20)
    @ApiModelProperty(value = "原电子票据号码")
    private String originalBillNo;

    @Excel(name = "电子红票代码", width = 20)
    @ApiModelProperty(value = "电子红票代码")
    private String scarletBillBatchCode;

    @Excel(name = "电子红票号码", width = 20)
    @ApiModelProperty(value = "电子红票号码")
    private String scarletBillNo;

    @Excel(name = "电子红票校验码", width = 20)
    @ApiModelProperty(value = "电子红票校验码")
    private String scarletRandom;

    @Excel(name = "冲红原因", width = 30)
    @ApiModelProperty(value = "冲红原因")
    private String reason;

    @Excel(name = "经办人", width = 15)
    @ApiModelProperty(value = "经办人")
    private String operator;

    @Excel(name = "业务发生时间", width = 20)
    @ApiModelProperty(value = "业务发生时间")
    private String busDateTime; // 格式：yyyyMMddHHmmssSSS

    @Excel(name = "开票点编码", width = 15)
    @ApiModelProperty(value = "开票点编码")
    private String placeCode;

    @Excel(name = "电子红票生成时间", width = 20)
    @ApiModelProperty(value = "电子红票生成时间")
    private String createTime; // 格式：yyyyMMddHHmmssSSS

    @ApiModelProperty(value = "电子红票二维码图片数据")
    private String billQrCode; // BASE64编码

    @Excel(name = "电子红票H5页面URL", width = 30)
    @ApiModelProperty(value = "电子红票H5页面URL")
    private String pictureUrl;

    @Excel(name = "电子红票外网H5页面URL", width = 30)
    @ApiModelProperty(value = "电子红票外网H5页面URL")
    private String pictureNetUrl;

    @Excel(name = "冲红状态", width = 15)
    @ApiModelProperty(value = "冲红状态：1-成功，0-失败")
    private String status;

    @Excel(name = "错误消息", width = 30)
    @ApiModelProperty(value = "错误消息")
    private String errorMessage;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
}