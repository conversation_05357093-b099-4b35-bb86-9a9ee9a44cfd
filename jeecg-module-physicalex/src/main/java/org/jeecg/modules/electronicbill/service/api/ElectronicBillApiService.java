package org.jeecg.modules.electronicbill.service.api;

import org.jeecg.modules.electronicbill.dto.request.*;
import org.jeecg.modules.electronicbill.dto.response.*;

/**
 * 电子票据API服务接口
 * 基于博思电子票据管理平台接口规范
 * 
 * <AUTHOR>
 */
public interface ElectronicBillApiService {

    /**
     * 门诊电子票据开具接口
     * 服务标识：invoiceEBillOutpatient
     * 
     * @param request 门诊开票请求
     * @return 开票结果
     */
    ElectronicBillResponse invoiceOutpatient(OutpatientBillRequest request);

    /**
     * 住院电子票据开具接口
     * 服务标识：invEBillHospitalized
     * 
     * @param request 住院开票请求
     * @return 开票结果
     */
    ElectronicBillResponse invoiceHospitalized(HospitalizedBillRequest request);

    /**
     * 电子票据冲红接口
     * 服务标识：writeOffEBill
     * 
     * @param request 冲红请求
     * @return 冲红结果
     */
    WriteOffResponse writeOffBill(WriteOffRequest request);

    /**
     * 根据电子票信息获取电子票据状态接口
     * 服务标识：getEBillStatesByBillInfo
     * 
     * @param request 状态查询请求
     * @return 票据状态
     */
    BillStatusResponse getBillStatus(BillStatusRequest request);

    /**
     * 根据电子票信息获取电子票据状态接口（简化版）
     * 
     * @param billBatchCode 电子票据代码
     * @param billNo 电子票据号码
     * @return 票据状态
     */
    default BillStatusResponse getBillStatus(String billBatchCode, String billNo) {
        BillStatusRequest request = new BillStatusRequest();
        request.setBillBatchCode(billBatchCode);
        request.setBillNo(billNo);
        return getBillStatus(request);
    }

    /**
     * 根据开票日期获取开票信息接口
     * 服务标识：getBillByIvcDate
     * 
     * @param request 按日期查询请求
     * @return 开票信息列表
     */
    GetBillByDateResponse getBillByIvcDate(GetBillByDateRequest request);

    /**
     * 总笔数核对接口
     * 服务标识：checkTotalData
     * 
     * @param request 总笔数核对请求
     * @return 核对结果
     */
    CheckTotalDataResponse checkTotalData(CheckTotalDataRequest request);

    /**
     * 冲红数据核对接口
     * 服务标识：checkWriteOffData
     * 
     * @param request 冲红数据核对请求
     * @return 核对结果
     */
    CheckWriteOffDataResponse checkWriteOffData(CheckWriteOffDataRequest request);

    /**
     * 根据业务日期获取开票信息接口
     * 服务标识：getBillByBusDate
     * 
     * @param request 按业务日期查询请求
     * @return 开票信息列表
     */
    GetBillByBusDateResponse getBillByBusDate(GetBillByBusDateRequest request);

    /**
     * 按开票日期总笔数核对接口
     * 服务标识：checkTotalDataByIvcDate
     * 
     * @param request 按开票日期总笔数核对请求
     * @return 核对结果
     */
    CheckTotalDataResponse checkTotalDataByIvcDate(CheckTotalDataByIvcDateRequest request);

    /**
     * 按开票日期冲红数据核对接口
     * 服务标识：checkWriteOffDataByIvcDate
     * 
     * @param request 按开票日期冲红数据核对请求
     * @return 核对结果
     */
    CheckWriteOffDataResponse checkWriteOffDataByIvcDate(CheckWriteOffDataByIvcDateRequest request);
}