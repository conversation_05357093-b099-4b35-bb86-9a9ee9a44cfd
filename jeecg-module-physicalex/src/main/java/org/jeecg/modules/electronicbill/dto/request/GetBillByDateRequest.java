package org.jeecg.modules.electronicbill.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 根据开票日期获取开票信息请求
 * 服务标识：getBillByIvcDate
 * 
 * <AUTHOR>
 */
@Data
public class GetBillByDateRequest {

    /**
     * 起始日期
     * 格式：yyyyMMddHHmmssSSS
     */
    @JsonProperty("busBgnDate")
    @NotBlank(message = "起始日期不能为空")
    private String busBgnDate;

    /**
     * 截止日期
     * 格式：yyyyMMddHHmmssSSS
     */
    @JsonProperty("busEndDate")
    @NotBlank(message = "截止日期不能为空")
    private String busEndDate;

    /**
     * 开票点编码
     * 不填写，则查询单位下所有开票据点
     */
    @JsonProperty("placeCode")
    private String placeCode;

    /**
     * 数据类型
     * 1 正常电子、2 电子红票、3 换开纸质、4 换开纸质红票、5空白纸质
     */
    @JsonProperty("dataType")
    private String dataType;

    /**
     * 业务标识
     * 01住院、02门诊、03急诊、05门特、04体检中心
     */
    @JsonProperty("busType")
    private String busType;

    /**
     * 页码
     * 传入值为1代表第一页,传入值为2代表第二页,依此类推
     */
    @JsonProperty("pageNo")
    @NotNull(message = "页码不能为空")
    private Integer pageNo;

    /**
     * 每页条数
     * 每页返回最多返回200条
     */
    @JsonProperty("pageSize")
    @NotNull(message = "每页条数不能为空")
    private Integer pageSize;
}