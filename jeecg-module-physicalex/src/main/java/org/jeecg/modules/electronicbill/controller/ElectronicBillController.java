package org.jeecg.modules.electronicbill.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.electronicbill.dto.response.*;
import org.jeecg.modules.electronicbill.entity.ElectronicBill;
import org.jeecg.modules.electronicbill.mapper.ElectronicBillMapper;
import org.jeecg.modules.electronicbill.service.IElectronicBillService;
import org.jeecg.modules.fee.entity.CustomerRegBill;
import org.jeecg.modules.fee.mapper.CustomerRegBillMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 电子票据管理控制器
 *
 * <AUTHOR>
 */
@Api(tags = "电子票据管理")
@RestController
@RequestMapping("/electronicbill/bill")
@Slf4j
public class ElectronicBillController extends JeecgController<ElectronicBill, IElectronicBillService> {

    @Resource
    private IElectronicBillService electronicBillService;

    @Resource
    private ElectronicBillMapper electronicBillMapper;

    @Resource
    private CustomerRegBillMapper customerRegBillMapper;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "电子票据-分页列表查询")
    @ApiOperation(value = "电子票据-分页列表查询", notes = "电子票据-分页列表查询")
    @GetMapping(value = "/list")
    public Result<Page<ElectronicBill>> queryPageList(ElectronicBill electronicBill,
                                                      @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                      @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                      @RequestParam(value = "startDate", required = false) String startDate,
                                                      @RequestParam(value = "endDate", required = false) String endDate,
                                                      HttpServletRequest req) {
        Page<ElectronicBill> page = new Page<>(pageNo, pageSize);
        Page<ElectronicBill> pageList = electronicBillMapper.selectBillPage(page,
                electronicBill.getBusNo(),
                electronicBill.getElectronicBillNo(),
                electronicBill.getBillBatchCode(),
                electronicBill.getPayer(),
                electronicBill.getBillStatus(),
                electronicBill.getTeamFlag(),
                startDate,
                endDate);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "电子票据-添加")
    @ApiOperation(value = "电子票据-添加", notes = "电子票据-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ElectronicBill electronicBill) {
        electronicBillService.save(electronicBill);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "电子票据-编辑")
    @ApiOperation(value = "电子票据-编辑", notes = "电子票据-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody ElectronicBill electronicBill) {
        electronicBillService.updateById(electronicBill);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "电子票据-通过id删除")
    @ApiOperation(value = "电子票据-通过id删除", notes = "电子票据-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        electronicBillService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "电子票据-批量删除")
    @ApiOperation(value = "电子票据-批量删除", notes = "电子票据-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.electronicBillService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "电子票据-通过id查询")
    @ApiOperation(value = "电子票据-通过id查询", notes = "电子票据-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ElectronicBill> queryById(@RequestParam(name="id",required=true) String id) {
        ElectronicBill electronicBill = electronicBillService.getById(id);
        if(electronicBill==null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(electronicBill);
    }

    /**
     * 为支付单申请电子票据
     */
    @AutoLog(value = "电子票据-申请开票")
    @ApiOperation(value = "为支付单申请电子票据", notes = "为支付单申请电子票据")
    @PostMapping(value = "/apply")
    public Result<ElectronicBillResponse> applyElectronicBill(@ApiParam("支付单ID") @RequestParam String billId) {
        try {
            CustomerRegBill customerRegBill = customerRegBillMapper.selectById(billId);
            if (customerRegBill == null) {
                return Result.error("支付单不存在");
            }

            ElectronicBillResponse response = electronicBillService.applyElectronicBill(customerRegBill);
            
            if (response.isSuccess()) {
                return Result.OK("电子票据申请成功",response);
            } else {
                return Result.error(response.getErrorMessage(), response);
            }
        } catch (Exception e) {
            log.error("申请电子票据异常", e);
            return Result.error("申请失败：" + e.getMessage());
        }
    }

    /**
     * 批量申请电子票据
     */
    @AutoLog(value = "电子票据-批量申请开票")
    @ApiOperation(value = "批量申请电子票据", notes = "批量申请电子票据")
    @PostMapping(value = "/batchApply")
    public Result<List<ElectronicBillResponse>> batchApplyElectronicBill(@ApiParam("支付单ID列表") @RequestBody List<String> billIds) {
        try {
            List<ElectronicBillResponse> responses = electronicBillService.batchApplyElectronicBill(billIds);
            
            long successCount = responses.stream().filter(ElectronicBillResponse::isSuccess).count();
            
            return Result.OK( String.format("批量申请完成，成功：%d，失败：%d", successCount, responses.size() - successCount),responses);
        } catch (Exception e) {
            log.error("批量申请电子票据异常", e);
            return Result.error("批量申请失败：" + e.getMessage());
        }
    }

    /**
     * 电子票据冲红
     */
    @AutoLog(value = "电子票据-冲红")
    @ApiOperation(value = "电子票据冲红", notes = "电子票据冲红")
    @PostMapping(value = "/writeOff")
    public Result<WriteOffResponse> writeOffElectronicBill(@ApiParam("支付单ID") @RequestParam String billId,
                                                           @ApiParam("冲红原因") @RequestParam String reason,
                                                           @ApiParam("操作人") @RequestParam String operator) {
        try {
            WriteOffResponse response = electronicBillService.writeOffElectronicBill(billId, reason, operator);
            
            if (response.isSuccess()) {
                return Result.OK("电子票据冲红成功",response);
            } else {
                return Result.error(response.getErrorMessage(), response);
            }
        } catch (Exception e) {
            log.error("电子票据冲红异常", e);
            return Result.error("冲红失败：" + e.getMessage());
        }
    }

    /**
     * 查询票据状态
     */
    @AutoLog(value = "电子票据-状态查询")
    @ApiOperation(value = "查询票据状态", notes = "查询票据状态")
    @GetMapping(value = "/queryStatus")
    public Result<BillStatusResponse> queryBillStatus(@ApiParam("支付单ID") @RequestParam String billId) {
        try {
            BillStatusResponse response = electronicBillService.queryBillStatus(billId);
            
            if (response.isSuccess()) {
                return Result.OK("查询成功",response);
            } else {
                return Result.error(response.getErrorMessage(), response);
            }
        } catch (Exception e) {
            log.error("查询票据状态异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据票据代码和号码查询状态
     */
    @AutoLog(value = "电子票据-状态查询")
    @ApiOperation(value = "根据票据代码和号码查询状态", notes = "根据票据代码和号码查询状态")
    @GetMapping(value = "/queryStatusByBillNo")
    public Result<BillStatusResponse> queryBillStatusByBillNo(@ApiParam("票据代码") @RequestParam String billBatchCode,
                                                              @ApiParam("票据号码") @RequestParam String billNo) {
        try {
            BillStatusResponse response = electronicBillService.queryBillStatus(billBatchCode, billNo);
            
            if (response.isSuccess()) {
                return Result.OK( "查询成功",response);
            } else {
                return Result.error(response.getErrorMessage(), response);
            }
        } catch (Exception e) {
            log.error("查询票据状态异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 重新申请电子票据
     */
    @AutoLog(value = "电子票据-重新申请")
    @ApiOperation(value = "重新申请电子票据", notes = "重新申请电子票据")
    @PostMapping(value = "/retry")
    public Result<ElectronicBillResponse> retryApplyElectronicBill(@ApiParam("支付单ID") @RequestParam String billId) {
        try {
            ElectronicBillResponse response = electronicBillService.retryApplyElectronicBill(billId);
            
            if (response.isSuccess()) {
                return Result.OK("重新申请成功",response);
            } else {
                return Result.error(response.getErrorMessage(), response);
            }
        } catch (Exception e) {
            log.error("重新申请电子票据异常", e);
            return Result.error("重新申请失败：" + e.getMessage());
        }
    }

    /**
     * 同步票据状态
     */
    @AutoLog(value = "电子票据-同步状态")
    @ApiOperation(value = "同步票据状态", notes = "同步票据状态")
    @PostMapping(value = "/syncStatus")
    public Result<Integer> syncBillStatus() {
        try {
            int syncCount = electronicBillService.syncBillStatus();
            return Result.OK(String.format("同步完成，成功同步%d条记录", syncCount),syncCount);
        } catch (Exception e) {
            log.error("同步票据状态异常", e);
            return Result.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 获取票据PDF下载地址
     */
    @AutoLog(value = "电子票据-获取PDF地址")
    @ApiOperation(value = "获取票据PDF下载地址", notes = "获取票据PDF下载地址")
    @GetMapping(value = "/getPdfUrl")
    public Result<String> getBillPdfUrl(@ApiParam("支付单ID") @RequestParam String billId) {
        try {
            String pdfUrl = electronicBillService.getBillPdfUrl(billId);
            if (pdfUrl != null) {
                return Result.OK(pdfUrl, "获取成功");
            } else {
                return Result.error("未找到PDF下载地址");
            }
        } catch (Exception e) {
            log.error("获取PDF地址异常", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取票据二维码
     */
    @AutoLog(value = "电子票据-获取二维码")
    @ApiOperation(value = "获取票据二维码", notes = "获取票据二维码")
    @GetMapping(value = "/getQrCode")
    public Result<String> getBillQrCode(@ApiParam("支付单ID") @RequestParam String billId) {
        try {
            String qrCode = electronicBillService.getBillQrCode(billId);
            if (qrCode != null) {
                return Result.OK(qrCode, "获取成功");
            } else {
                return Result.error("未找到票据二维码");
            }
        } catch (Exception e) {
            log.error("获取二维码异常", e);
            return Result.error("获取失败：" + e.getMessage());
        }
    }

    /**
     * 统计信息
     */
    @AutoLog(value = "电子票据-统计信息")
    @ApiOperation(value = "获取统计信息", notes = "获取统计信息")
    @GetMapping(value = "/statistics")
    public Result<java.util.Map<String, Object>> getStatistics(@RequestParam(value = "startDate", required = false) String startDate,
                                                                @RequestParam(value = "endDate", required = false) String endDate) {
        try {
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            
            // 总数统计
            Long totalCount = electronicBillMapper.countByStatus(null, startDate, endDate);
            Long normalCount = electronicBillMapper.countByStatus("1", startDate, endDate);
            Long voidCount = electronicBillMapper.countByStatus("2", startDate, endDate);
            
            // 金额统计
            java.math.BigDecimal totalAmount = electronicBillMapper.sumAmountByStatus(null, startDate, endDate);
            java.math.BigDecimal normalAmount = electronicBillMapper.sumAmountByStatus("1", startDate, endDate);
            java.math.BigDecimal voidAmount = electronicBillMapper.sumAmountByStatus("2", startDate, endDate);
            
            statistics.put("totalCount", totalCount);
            statistics.put("normalCount", normalCount);
            statistics.put("voidCount", voidCount);
            statistics.put("totalAmount", totalAmount);
            statistics.put("normalAmount", normalAmount);
            statistics.put("voidAmount", voidAmount);
            
            // 团检统计
            List<java.util.Map<String, Object>> teamSummary = electronicBillMapper.selectTeamBillSummary(startDate, endDate);
            statistics.put("teamSummary", teamSummary);
            
            return Result.OK(statistics);
        } catch (Exception e) {
            log.error("获取统计信息异常", e);
            return Result.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据开票日期获取开票信息接口
     */
    @AutoLog(value = "电子票据-根据开票日期获取开票信息")
    @ApiOperation(value = "根据开票日期获取开票信息", notes = "查询指定日期范围内的开票信息，支持分页")
    @GetMapping("/getBillsByDate")
    public Result<?> getBillsByDate(
            @ApiParam(value = "开始日期", required = true, example = "20250101000000000") 
            @RequestParam String startDate,
            @ApiParam(value = "结束日期", required = true, example = "20250131235959999") 
            @RequestParam String endDate,
            @ApiParam(value = "开票点编码", example = "PLACE_001") 
            @RequestParam(required = false) String placeCode,
            @ApiParam(value = "业务类型", example = "04") 
            @RequestParam(required = false) String busType,
            @ApiParam(value = "数据类型", example = "1") 
            @RequestParam(required = false) String dataType,
            @ApiParam(value = "页码", example = "1") 
            @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数", example = "20") 
            @RequestParam(defaultValue = "20") Integer pageSize) {
        
        try {
            log.info("根据开票日期获取开票信息：startDate={}, endDate={}, placeCode={}, busType={}, dataType={}, pageNo={}, pageSize={}", 
                     startDate, endDate, placeCode, busType, dataType, pageNo, pageSize);

            // 参数校验
            if (pageSize > 200) {
                return Result.error("每页条数不能超过200");
            }

            GetBillByDateResponse response = electronicBillService.getBillsByDate(
                startDate, endDate, placeCode, busType, dataType, pageNo, pageSize);

            if ("S0000".equals(response.getResult())) {
                return Result.OK("查询成功", response.getMessage());
            } else {
                return Result.error("查询失败：" + response.getResult());
            }

        } catch (Exception e) {
            log.error("根据开票日期获取开票信息异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 总笔数核对接口
     */
    @AutoLog(value = "电子票据-总笔数核对")
    @ApiOperation(value = "总笔数核对", notes = "核对指定日期范围内的开票总笔数和金额")
    @PostMapping("/checkTotalData")
    public Result<?> checkTotalData(
            @ApiParam(value = "起始日期", required = true, example = "20250101") 
            @RequestParam String startDate,
            @ApiParam(value = "截止日期", required = true, example = "20250131") 
            @RequestParam String endDate,
            @ApiParam(value = "业务类型", example = "04") 
            @RequestParam(required = false) String busType,
            @ApiParam(value = "开票点编码", example = "PLACE_001") 
            @RequestParam(required = false) String placeCode) {
        
        try {
            log.info("总笔数核对：startDate={}, endDate={}, busType={}, placeCode={}", 
                     startDate, endDate, busType, placeCode);

            CheckTotalDataResponse response = electronicBillService.checkTotalData(
                startDate, endDate, busType, placeCode);

            if (response.isSuccess()) {
                return Result.OK("核对成功", response.getMessage());
            } else {
                return Result.error("核对失败：" + response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("总笔数核对异常", e);
            return Result.error("核对失败：" + e.getMessage());
        }
    }

    /**
     * 冲红数据核对接口
     */
    @AutoLog(value = "电子票据-冲红数据核对")
    @ApiOperation(value = "冲红数据核对", notes = "核对指定日期范围内的冲红数据")
    @PostMapping("/checkWriteOffData")
    public Result<?> checkWriteOffData(
            @ApiParam(value = "起始日期", required = true, example = "20250101") 
            @RequestParam String startDate,
            @ApiParam(value = "截止日期", required = true, example = "20250131") 
            @RequestParam String endDate,
            @ApiParam(value = "业务类型", example = "04") 
            @RequestParam(required = false) String busType,
            @ApiParam(value = "开票点编码", example = "PLACE_001") 
            @RequestParam(required = false) String placeCode) {
        
        try {
            log.info("冲红数据核对：startDate={}, endDate={}, busType={}, placeCode={}", 
                     startDate, endDate, busType, placeCode);

            CheckWriteOffDataResponse response = electronicBillService.checkWriteOffData(
                startDate, endDate, busType, placeCode);

            if (response.isSuccess()) {
                return Result.OK("核对成功", response.getMessage());
            } else {
                return Result.error("核对失败：" + response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("冲红数据核对异常", e);
            return Result.error("核对失败：" + e.getMessage());
        }
    }

    /**
     * 根据业务日期获取开票信息接口
     */
    @AutoLog(value = "电子票据-根据业务日期获取开票信息")
    @ApiOperation(value = "根据业务日期获取开票信息", notes = "根据业务日期查询开票信息，支持分页")
    @GetMapping("/getBillsByBusDate")
    public Result<?> getBillsByBusDate(
            @ApiParam(value = "起始日期", required = true, example = "20250101") 
            @RequestParam String startDate,
            @ApiParam(value = "截止日期", required = true, example = "20250131") 
            @RequestParam String endDate,
            @ApiParam(value = "开票点编码", example = "PLACE_001") 
            @RequestParam(required = false) String placeCode,
            @ApiParam(value = "业务类型", example = "04") 
            @RequestParam(required = false) String busType,
            @ApiParam(value = "数据类型", example = "1") 
            @RequestParam(required = false) String dataType,
            @ApiParam(value = "页码", example = "1") 
            @RequestParam(defaultValue = "1") Integer pageNo,
            @ApiParam(value = "每页条数", example = "20") 
            @RequestParam(defaultValue = "20") Integer pageSize) {
        
        try {
            log.info("根据业务日期获取开票信息：startDate={}, endDate={}, placeCode={}, busType={}, dataType={}, pageNo={}, pageSize={}", 
                     startDate, endDate, placeCode, busType, dataType, pageNo, pageSize);

            // 参数校验
            if (pageSize > 200) {
                return Result.error("每页条数不能超过200");
            }

            GetBillByBusDateResponse response = electronicBillService.getBillsByBusDate(
                startDate, endDate, placeCode, busType, dataType, pageNo, pageSize);

            if (response.isSuccess()) {
                return Result.OK("查询成功", response.getMessage());
            } else {
                return Result.error("查询失败：" + response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("根据业务日期获取开票信息异常", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 按开票日期总笔数核对接口
     */
    @AutoLog(value = "电子票据-按开票日期总笔数核对")
    @ApiOperation(value = "按开票日期总笔数核对", notes = "按开票日期核对总笔数和金额")
    @PostMapping("/checkTotalDataByIvcDate")
    public Result<?> checkTotalDataByIvcDate(
            @ApiParam(value = "起始日期", required = true, example = "20250101") 
            @RequestParam String startDate,
            @ApiParam(value = "截止日期", required = true, example = "20250131") 
            @RequestParam String endDate,
            @ApiParam(value = "业务类型", example = "04") 
            @RequestParam(required = false) String busType,
            @ApiParam(value = "开票点编码", example = "PLACE_001") 
            @RequestParam(required = false) String placeCode) {
        
        try {
            log.info("按开票日期总笔数核对：startDate={}, endDate={}, busType={}, placeCode={}", 
                     startDate, endDate, busType, placeCode);

            CheckTotalDataResponse response = electronicBillService.checkTotalDataByIvcDate(
                startDate, endDate, busType, placeCode);

            if (response.isSuccess()) {
                return Result.OK("核对成功", response.getMessage());
            } else {
                return Result.error("核对失败：" + response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("按开票日期总笔数核对异常", e);
            return Result.error("核对失败：" + e.getMessage());
        }
    }

    /**
     * 按开票日期冲红数据核对接口
     */
    @AutoLog(value = "电子票据-按开票日期冲红数据核对")
    @ApiOperation(value = "按开票日期冲红数据核对", notes = "按开票日期核对冲红数据")
    @PostMapping("/checkWriteOffDataByIvcDate")
    public Result<?> checkWriteOffDataByIvcDate(
            @ApiParam(value = "起始日期", required = true, example = "20250101") 
            @RequestParam String startDate,
            @ApiParam(value = "截止日期", required = true, example = "20250131") 
            @RequestParam String endDate,
            @ApiParam(value = "业务类型", example = "04") 
            @RequestParam(required = false) String busType,
            @ApiParam(value = "开票点编码", example = "PLACE_001") 
            @RequestParam(required = false) String placeCode) {
        
        try {
            log.info("按开票日期冲红数据核对：startDate={}, endDate={}, busType={}, placeCode={}", 
                     startDate, endDate, busType, placeCode);

            CheckWriteOffDataResponse response = electronicBillService.checkWriteOffDataByIvcDate(
                startDate, endDate, busType, placeCode);

            if (response.isSuccess()) {
                return Result.OK("核对成功", response.getMessage());
            } else {
                return Result.error("核对失败：" + response.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("按开票日期冲红数据核对异常", e);
            return Result.error("核对失败：" + e.getMessage());
        }
    }
}