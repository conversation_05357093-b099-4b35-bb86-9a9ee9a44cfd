package org.jeecg.modules.electronicbill.dto.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 冲红数据核对响应
 * 
 * <AUTHOR>
 */
@Data
public class CheckWriteOffDataResponse extends BaseResponse {

    /**
     * 业务数据
     */
    private WriteOffDataContent message;

    @Data
    public static class WriteOffDataContent {
        /**
         * 冲红笔数
         */
        private Integer writeOffBillCount;

        /**
         * 冲红金额
         */
        private BigDecimal writeOffBillAmount;
    }

    public static CheckWriteOffDataResponse failure(String result, String errorMessage) {
        CheckWriteOffDataResponse response = new CheckWriteOffDataResponse();
        response.setResult(result);
        response.setErrorMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }
}