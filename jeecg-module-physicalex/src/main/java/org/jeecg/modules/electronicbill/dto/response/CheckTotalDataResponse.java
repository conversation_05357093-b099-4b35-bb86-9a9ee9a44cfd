package org.jeecg.modules.electronicbill.dto.response;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 总笔数核对响应
 * 
 * <AUTHOR>
 */
@Getter
@Setter
public class CheckTotalDataResponse extends BaseResponse {

    /**
     * 业务数据
     */
    private CheckDataContent message;

    @Getter
    @Setter
    public static class CheckDataContent {
        /**
         * 正票笔数
         */
        private Integer normalBillCount;

        /**
         * 正票金额
         */
        private BigDecimal normalBillAmount;

        /**
         * 红票笔数
         */
        private Integer redBillCount;

        /**
         * 红票金额
         */
        private BigDecimal redBillAmount;

        /**
         * 合计笔数
         */
        private Integer totalBillCount;

        /**
         * 合计金额
         */
        private BigDecimal totalBillAmount;
    }

    public static CheckTotalDataResponse failure(String result, String errorMessage) {
        CheckTotalDataResponse response = new CheckTotalDataResponse();
        response.setResult(result);
        response.setErrorMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }
}