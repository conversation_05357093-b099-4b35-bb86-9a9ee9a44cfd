package org.jeecg.modules.electronicbill.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子票据冲红响应
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据冲红响应")
public class WriteOffResponse {

    @ApiModelProperty(value = "返回结果标识")
    @JsonProperty("result")
    private String result; // S0000表示成功

    @ApiModelProperty(value = "返回结果内容")
    @JsonProperty("message")
    private String message; // BASE64编码的JSON格式业务参数

    // 解析后的业务参数
    @ApiModelProperty(value = "电子红票票据代码")
    private String eScarletBillBatchCode;

    @ApiModelProperty(value = "电子红票票据号码")
    private String eScarletBillNo;

    @ApiModelProperty(value = "电子红票校验码")
    private String eScarletRandom;

    @ApiModelProperty(value = "电子红票生成时间")
    private String createTime; // 格式：yyyyMMddHHmmssSSS

    @ApiModelProperty(value = "电子票据二维码图片数据")
    private String billQRCode; // BASE64编码

    @ApiModelProperty(value = "电子票据H5页面URL")
    private String pictureUrl;

    @ApiModelProperty(value = "电子票据外网H5页面URL")
    private String pictureNetUrl;

    // 扩展字段
    @ApiModelProperty(value = "错误代码")
    private String errorCode;

    @ApiModelProperty(value = "错误描述")
    private String errorMessage;

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    /**
     * 创建成功响应
     */
    public static WriteOffResponse success() {
        WriteOffResponse response = new WriteOffResponse();
        response.setResult("S0000");
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static WriteOffResponse failure(String errorCode, String errorMessage) {
        WriteOffResponse response = new WriteOffResponse();
        response.setResult(errorCode);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "S0000".equals(result) || (success != null && success);
    }
}