package org.jeecg.modules.electronicbill.service.api.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.electronicbill.config.ElectronicBillConfig;
import org.jeecg.modules.electronicbill.dto.request.*;
import org.jeecg.modules.electronicbill.dto.response.*;
import org.jeecg.modules.electronicbill.service.api.ElectronicBillApiService;
import org.jeecg.modules.electronicbill.util.ElectronicBillUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 电子票据API服务实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ElectronicBillApiServiceImpl implements ElectronicBillApiService {

    @Autowired
    private ElectronicBillConfig billConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ElectronicBillUtil billUtil;

    @Override
    public ElectronicBillResponse invoiceOutpatient(OutpatientBillRequest request) {
        log.info("门诊电子票据开具请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("invoiceEBillOutpatient", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            ElectronicBillResponse result = parseElectronicBillResponse(response);
            
            log.info("门诊电子票据开具响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("门诊电子票据开具失败", e);
            return ElectronicBillResponse.failure("E9999", "系统异常：" + e.getMessage());
        }
    }

    @Override
    public ElectronicBillResponse invoiceHospitalized(HospitalizedBillRequest request) {
        log.info("住院电子票据开具请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("invEBillHospitalized", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            ElectronicBillResponse result = parseElectronicBillResponse(response);
            
            log.info("住院电子票据开具响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("住院电子票据开具失败", e);
            return ElectronicBillResponse.failure("E9999", "系统异常：" + e.getMessage());
        }
    }

    @Override
    public WriteOffResponse writeOffBill(WriteOffRequest request) {
        log.info("电子票据冲红请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("writeOffEBill", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            WriteOffResponse result = parseWriteOffResponse(response);
            
            log.info("电子票据冲红响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("电子票据冲红失败", e);
            return WriteOffResponse.failure("E9999", "系统异常：" + e.getMessage());
        }
    }

    @Override
    public BillStatusResponse getBillStatus(BillStatusRequest request) {
        log.info("电子票据状态查询请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("getEBillStatesByBillInfo", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            BillStatusResponse result = parseBillStatusResponse(response);
            
            log.info("电子票据状态查询响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("电子票据状态查询失败", e);
            return BillStatusResponse.failure("E9999", "系统异常：" + e.getMessage());
        }
    }

    /**
     * 构建请求参数
     */
    private Map<String, Object> buildRequestParams(String serviceId, Object businessData) {
        Map<String, Object> params = new HashMap<>();
        
        // 基础参数
        params.put("serialNumber", billUtil.generateSerialNumber());
        params.put("timestamp", billUtil.getCurrentTimestamp());
        params.put("version", "1.0");
        params.put("serviceId", serviceId);
        
        // 业务数据转BASE64
        String businessDataJson = JSON.toJSONString(businessData);
        String businessDataBase64 = Base64.getEncoder().encodeToString(businessDataJson.getBytes());
        params.put("businessData", businessDataBase64);
        
        // 生成签名
        String signature = billUtil.generateSignature(params, billConfig.getPrivateKey());
        params.put("signature", signature);
        
        return params;
    }

    /**
     * 调用票据API
     */
    private String callBillApi(Map<String, Object> requestParams) {
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        // 创建请求实体
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, headers);
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.postForEntity(
            billConfig.getApiUrl(), 
            requestEntity, 
            String.class
        );
        
        if (response.getStatusCode() == HttpStatus.OK) {
            return response.getBody();
        } else {
            throw new RuntimeException("API调用失败，状态码：" + response.getStatusCode());
        }
    }

    /**
     * 解析电子票据开具响应
     */
    private ElectronicBillResponse parseElectronicBillResponse(String responseJson) {
        try {
            Map<String, Object> responseMap = JSON.parseObject(responseJson, Map.class);
            
            ElectronicBillResponse result = new ElectronicBillResponse();
            result.setResult((String) responseMap.get("result"));
            result.setMessage((String) responseMap.get("message"));
            
            // 如果成功，解析业务数据
            if ("S0000".equals(result.getResult()) && result.getMessage() != null) {
                String businessDataJson = new String(Base64.getDecoder().decode(result.getMessage()));
                Map<String, Object> businessData = JSON.parseObject(businessDataJson, Map.class);
                
                result.setBillName((String) businessData.get("billName"));
                result.setBillBatchCode((String) businessData.get("billBatchCode"));
                result.setBillNo((String) businessData.get("billNo"));
                result.setRandom((String) businessData.get("random"));
                result.setIvcDateTime((String) businessData.get("ivcDateTime"));
                result.setBillQRCode((String) businessData.get("billQRCode"));
                result.setPictureUrl((String) businessData.get("pictureUrl"));
                result.setPictureNetUrl((String) businessData.get("pictureNetUrl"));
                result.setNoticeQRCode((String) businessData.get("noticeQRCode"));
                result.setNoticePictureUrl((String) businessData.get("noticePictureUrl"));
                result.setNoticePictureNetUrl((String) businessData.get("noticePictureNetUrl"));
            }
            
            result.setSuccess(result.isSuccess());
            return result;
            
        } catch (Exception e) {
            log.error("解析电子票据响应失败", e);
            return ElectronicBillResponse.failure("E9998", "响应解析失败");
        }
    }

    /**
     * 解析冲红响应
     */
    private WriteOffResponse parseWriteOffResponse(String responseJson) {
        try {
            Map<String, Object> responseMap = JSON.parseObject(responseJson, Map.class);
            
            WriteOffResponse result = new WriteOffResponse();
            result.setResult((String) responseMap.get("result"));
            result.setMessage((String) responseMap.get("message"));
            
            // 如果成功，解析业务数据
            if ("S0000".equals(result.getResult()) && result.getMessage() != null) {
                String businessDataJson = new String(Base64.getDecoder().decode(result.getMessage()));
                Map<String, Object> businessData = JSON.parseObject(businessDataJson, Map.class);
                
                result.setEScarletBillBatchCode((String) businessData.get("eScarletBillBatchCode"));
                result.setEScarletBillNo((String) businessData.get("eScarletBillNo"));
                result.setEScarletRandom((String) businessData.get("eScarletRandom"));
                result.setCreateTime((String) businessData.get("createTime"));
                result.setBillQRCode((String) businessData.get("billQRCode"));
                result.setPictureUrl((String) businessData.get("pictureUrl"));
                result.setPictureNetUrl((String) businessData.get("pictureNetUrl"));
            }
            
            result.setSuccess(result.isSuccess());
            return result;
            
        } catch (Exception e) {
            log.error("解析冲红响应失败", e);
            return WriteOffResponse.failure("E9998", "响应解析失败");
        }
    }

    /**
     * 解析状态查询响应
     */
    private BillStatusResponse parseBillStatusResponse(String responseJson) {
        try {
            Map<String, Object> responseMap = JSON.parseObject(responseJson, Map.class);
            
            BillStatusResponse result = new BillStatusResponse();
            result.setResult((String) responseMap.get("result"));
            result.setMessage((String) responseMap.get("message"));
            
            // 如果成功，解析业务数据
            if ("S0000".equals(result.getResult()) && result.getMessage() != null) {
                String businessDataJson = new String(Base64.getDecoder().decode(result.getMessage()));
                Map<String, Object> businessData = JSON.parseObject(businessDataJson, Map.class);
                
                result.setBillName((String) businessData.get("billName"));
                result.setBillBatchCode((String) businessData.get("billBatchCode"));
                result.setBillNo((String) businessData.get("billNo"));
                result.setRandom((String) businessData.get("random"));
                result.setIvcDateTime((String) businessData.get("ivcDateTime"));
                result.setState((String) businessData.get("state"));
                result.setIsPrtPaper((String) businessData.get("isPrtPaper"));
                result.setPBillBatchCode((String) businessData.get("pBillBatchCode"));
                result.setPBillNo((String) businessData.get("pBillNo"));
                result.setPCreateTime((String) businessData.get("pCreateTime"));
                result.setPBillBusDate((String) businessData.get("pBillBusDate"));
                result.setIsScarlet((String) businessData.get("isScarlet"));
                result.setScarletBillBatchCode((String) businessData.get("scarletBillBatchCode"));
                result.setScarletBillNo((String) businessData.get("scarletBillNo"));
                result.setScarletRandom((String) businessData.get("scarletRandom"));
                result.setScarletBillQRCode((String) businessData.get("scarletBillQRCode"));
                result.setScarletCreateTime((String) businessData.get("scarletCreateTime"));
                result.setScarletBillBusDate((String) businessData.get("scarletBillBusDate"));
            }
            
            result.setSuccess(result.isSuccess());
            return result;
            
        } catch (Exception e) {
            log.error("解析状态查询响应失败", e);
            return BillStatusResponse.failure("E9998", "响应解析失败");
        }
    }

    @Override
    public GetBillByDateResponse getBillByIvcDate(GetBillByDateRequest request) {
        log.info("根据开票日期获取开票信息请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("getBillByIvcDate", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            GetBillByDateResponse result = parseGetBillByDateResponse(response);
            
            log.info("根据开票日期获取开票信息响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("根据开票日期获取开票信息失败", e);
            return GetBillByDateResponse.failure("E9999", "接口调用失败：" + e.getMessage());
        }
    }

    /**
     * 解析根据开票日期获取开票信息响应
     */
    private GetBillByDateResponse parseGetBillByDateResponse(String response) {
        try {
            Map<String, Object> responseMap = JSON.parseObject(response, Map.class);
            String result = (String) responseMap.get("result");
            String message = (String) responseMap.get("message");

            GetBillByDateResponse billResponse = new GetBillByDateResponse();
            billResponse.setResult(result);

            if (!"S0000".equals(result)) {
                // 失败响应
                log.warn("获取开票信息失败：result={}, message={}", result, message);
                return GetBillByDateResponse.failure(result, message);
            }

            // 成功响应，解析BASE64编码的message
            String decodedMessage = new String(Base64.getDecoder().decode(message), "UTF-8");
            Map<String, Object> messageData = JSON.parseObject(decodedMessage, Map.class);

            GetBillByDateResponse.MessageContent messageContent = new GetBillByDateResponse.MessageContent();
            messageContent.setTotal((Integer) messageData.get("total"));
            messageContent.setPageNo((Integer) messageData.get("pageNo"));

            // 解析开票明细列表
            Object billListObj = messageData.get("billList");
            if (billListObj != null) {
                java.util.List<Map<String, Object>> billListMap = (java.util.List<Map<String, Object>>) billListObj;
                java.util.List<GetBillByDateResponse.BillInfo> billList = new java.util.ArrayList<>();

                for (Map<String, Object> billData : billListMap) {
                    GetBillByDateResponse.BillInfo billInfo = new GetBillByDateResponse.BillInfo();
                    billInfo.setBusDate((String) billData.get("busDate"));
                    billInfo.setBusNo((String) billData.get("busNo"));
                    billInfo.setBusType((String) billData.get("busType"));
                    billInfo.setPlaceCode((String) billData.get("placeCode"));
                    billInfo.setBillName((String) billData.get("billName"));
                    billInfo.setBillBatchCode((String) billData.get("billBatchCode"));
                    billInfo.setBillNo((String) billData.get("billNo"));
                    billInfo.setRandom((String) billData.get("random"));
                    
                    // 处理金额，可能是Number类型
                    Object totalAmtObj = billData.get("totalAmt");
                    if (totalAmtObj != null) {
                        billInfo.setTotalAmt(new java.math.BigDecimal(totalAmtObj.toString()));
                    }
                    
                    billInfo.setIvcDateTime((String) billData.get("ivcDateTime"));
                    billInfo.setDataType((String) billData.get("dataType"));
                    billInfo.setState((String) billData.get("state"));
                    billInfo.setRelateBillBatchCode((String) billData.get("relateBillBatchCode"));
                    billInfo.setRelateBillNo((String) billData.get("relateBillNo"));

                    billList.add(billInfo);
                }
                messageContent.setBillList(billList);
            }

            billResponse.setMessage(messageContent);
            return billResponse;
            
        } catch (Exception e) {
            log.error("解析根据开票日期获取开票信息响应失败", e);
            return GetBillByDateResponse.failure("E9998", "响应解析失败");
        }
    }

    @Override
    public CheckTotalDataResponse checkTotalData(CheckTotalDataRequest request) {
        log.info("总笔数核对请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("checkTotalData", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            CheckTotalDataResponse result = parseCheckTotalDataResponse(response);
            
            log.info("总笔数核对响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("总笔数核对失败", e);
            return CheckTotalDataResponse.failure("E9999", "系统异常：" + e.getMessage());
        }
    }

    @Override
    public CheckWriteOffDataResponse checkWriteOffData(CheckWriteOffDataRequest request) {
        log.info("冲红数据核对请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("checkWriteOffData", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            CheckWriteOffDataResponse result = parseCheckWriteOffDataResponse(response);
            
            log.info("冲红数据核对响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("冲红数据核对失败", e);
            return CheckWriteOffDataResponse.failure("E9999", "系统异常：" + e.getMessage());
        }
    }

    @Override
    public GetBillByBusDateResponse getBillByBusDate(GetBillByBusDateRequest request) {
        log.info("根据业务日期获取开票信息请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("getBillByBusDate", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            GetBillByBusDateResponse result = parseGetBillByBusDateResponse(response);
            
            log.info("根据业务日期获取开票信息响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("根据业务日期获取开票信息失败", e);
            return GetBillByBusDateResponse.failure("E9999", "接口调用失败：" + e.getMessage());
        }
    }

    @Override
    public CheckTotalDataResponse checkTotalDataByIvcDate(CheckTotalDataByIvcDateRequest request) {
        log.info("按开票日期总笔数核对请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("checkTotalDataByIvcDate", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            CheckTotalDataResponse result = parseCheckTotalDataResponse(response);
            
            log.info("按开票日期总笔数核对响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("按开票日期总笔数核对失败", e);
            return CheckTotalDataResponse.failure("E9999", "系统异常：" + e.getMessage());
        }
    }

    @Override
    public CheckWriteOffDataResponse checkWriteOffDataByIvcDate(CheckWriteOffDataByIvcDateRequest request) {
        log.info("按开票日期冲红数据核对请求：{}", JSON.toJSONString(request));
        
        try {
            // 构建请求参数
            Map<String, Object> requestParams = buildRequestParams("checkWriteOffDataByIvcDate", request);
            
            // 调用接口
            String response = callBillApi(requestParams);
            
            // 解析响应
            CheckWriteOffDataResponse result = parseCheckWriteOffDataResponse(response);
            
            log.info("按开票日期冲红数据核对响应：{}", JSON.toJSONString(result));
            return result;
            
        } catch (Exception e) {
            log.error("按开票日期冲红数据核对失败", e);
            return CheckWriteOffDataResponse.failure("E9999", "系统异常：" + e.getMessage());
        }
    }

    /**
     * 解析总笔数核对响应
     */
    private CheckTotalDataResponse parseCheckTotalDataResponse(String response) {
        try {
            Map<String, Object> responseMap = JSON.parseObject(response, Map.class);
            String result = (String) responseMap.get("result");
            String message = (String) responseMap.get("message");

            CheckTotalDataResponse checkResponse = new CheckTotalDataResponse();
            checkResponse.setResult(result);

            if (!"S0000".equals(result)) {
                return CheckTotalDataResponse.failure(result, message);
            }

            // 成功响应，解析BASE64编码的message
            String decodedMessage = new String(Base64.getDecoder().decode(message), "UTF-8");
            Map<String, Object> messageData = JSON.parseObject(decodedMessage, Map.class);

            CheckTotalDataResponse.CheckDataContent content = new CheckTotalDataResponse.CheckDataContent();
            content.setNormalBillCount((Integer) messageData.get("normalBillCount"));
            content.setNormalBillAmount(new java.math.BigDecimal(messageData.get("normalBillAmount").toString()));
            content.setRedBillCount((Integer) messageData.get("redBillCount"));
            content.setRedBillAmount(new java.math.BigDecimal(messageData.get("redBillAmount").toString()));
            content.setTotalBillCount((Integer) messageData.get("totalBillCount"));
            content.setTotalBillAmount(new java.math.BigDecimal(messageData.get("totalBillAmount").toString()));

            checkResponse.setMessage(content);
            checkResponse.setSuccess(true);
            return checkResponse;
            
        } catch (Exception e) {
            log.error("解析总笔数核对响应失败", e);
            return CheckTotalDataResponse.failure("E9998", "响应解析失败");
        }
    }

    /**
     * 解析冲红数据核对响应
     */
    private CheckWriteOffDataResponse parseCheckWriteOffDataResponse(String response) {
        try {
            Map<String, Object> responseMap = JSON.parseObject(response, Map.class);
            String result = (String) responseMap.get("result");
            String message = (String) responseMap.get("message");

            CheckWriteOffDataResponse checkResponse = new CheckWriteOffDataResponse();
            checkResponse.setResult(result);

            if (!"S0000".equals(result)) {
                return CheckWriteOffDataResponse.failure(result, message);
            }

            // 成功响应，解析BASE64编码的message
            String decodedMessage = new String(Base64.getDecoder().decode(message), "UTF-8");
            Map<String, Object> messageData = JSON.parseObject(decodedMessage, Map.class);

            CheckWriteOffDataResponse.WriteOffDataContent content = new CheckWriteOffDataResponse.WriteOffDataContent();
            content.setWriteOffBillCount((Integer) messageData.get("writeOffBillCount"));
            content.setWriteOffBillAmount(new java.math.BigDecimal(messageData.get("writeOffBillAmount").toString()));

            checkResponse.setMessage(content);
            checkResponse.setSuccess(true);
            return checkResponse;
            
        } catch (Exception e) {
            log.error("解析冲红数据核对响应失败", e);
            return CheckWriteOffDataResponse.failure("E9998", "响应解析失败");
        }
    }

    /**
     * 解析根据业务日期获取开票信息响应
     */
    private GetBillByBusDateResponse parseGetBillByBusDateResponse(String response) {
        try {
            Map<String, Object> responseMap = JSON.parseObject(response, Map.class);
            String result = (String) responseMap.get("result");
            String message = (String) responseMap.get("message");

            GetBillByBusDateResponse billResponse = new GetBillByBusDateResponse();
            billResponse.setResult(result);

            if (!"S0000".equals(result)) {
                return GetBillByBusDateResponse.failure(result, message);
            }

            // 成功响应，解析BASE64编码的message  
            String decodedMessage = new String(Base64.getDecoder().decode(message), "UTF-8");
            Map<String, Object> messageData = JSON.parseObject(decodedMessage, Map.class);

            GetBillByBusDateResponse.MessageContent messageContent = new GetBillByBusDateResponse.MessageContent();
            messageContent.setTotal((Integer) messageData.get("total"));
            messageContent.setPageNo((Integer) messageData.get("pageNo"));

            // 解析开票明细列表
            Object billListObj = messageData.get("billList");
            if (billListObj != null) {
                java.util.List<Map<String, Object>> billListMap = (java.util.List<Map<String, Object>>) billListObj;
                java.util.List<GetBillByBusDateResponse.BillInfo> billList = new java.util.ArrayList<>();

                for (Map<String, Object> billData : billListMap) {
                    GetBillByBusDateResponse.BillInfo billInfo = new GetBillByBusDateResponse.BillInfo();
                    billInfo.setBusDate((String) billData.get("busDate"));
                    billInfo.setBusNo((String) billData.get("busNo"));
                    billInfo.setBusType((String) billData.get("busType"));
                    billInfo.setPlaceCode((String) billData.get("placeCode"));
                    billInfo.setBillName((String) billData.get("billName"));
                    billInfo.setBillBatchCode((String) billData.get("billBatchCode"));
                    billInfo.setBillNo((String) billData.get("billNo"));
                    billInfo.setRandom((String) billData.get("random"));
                    
                    Object totalAmtObj = billData.get("totalAmt");
                    if (totalAmtObj != null) {
                        billInfo.setTotalAmt(new java.math.BigDecimal(totalAmtObj.toString()));
                    }
                    
                    billInfo.setIvcDateTime((String) billData.get("ivcDateTime"));
                    billInfo.setDataType((String) billData.get("dataType"));
                    billInfo.setState((String) billData.get("state"));
                    billInfo.setRelateBillBatchCode((String) billData.get("relateBillBatchCode"));
                    billInfo.setRelateBillNo((String) billData.get("relateBillNo"));

                    billList.add(billInfo);
                }
                messageContent.setBillList(billList);
            }

            billResponse.setMessage(messageContent);
            billResponse.setSuccess(true);
            return billResponse;
            
        } catch (Exception e) {
            log.error("解析根据业务日期获取开票信息响应失败", e);
            return GetBillByBusDateResponse.failure("E9998", "响应解析失败");
        }
    }
}