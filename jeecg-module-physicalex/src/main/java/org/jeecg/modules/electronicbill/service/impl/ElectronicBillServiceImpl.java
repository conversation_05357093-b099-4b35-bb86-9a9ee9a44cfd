package org.jeecg.modules.electronicbill.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.electronicbill.config.ElectronicBillConfig;
import org.jeecg.modules.electronicbill.dto.request.*;
import org.jeecg.modules.electronicbill.dto.response.*;
import org.jeecg.modules.electronicbill.entity.ElectronicBill;
import org.jeecg.modules.electronicbill.entity.ElectronicBillDetail;
import org.jeecg.modules.electronicbill.entity.ElectronicBillWriteoff;
import org.jeecg.modules.electronicbill.mapper.ElectronicBillMapper;
import org.jeecg.modules.electronicbill.mapper.ElectronicBillDetailMapper;
import org.jeecg.modules.electronicbill.mapper.ElectronicBillWriteoffMapper;
import org.jeecg.modules.electronicbill.service.IElectronicBillService;
import org.jeecg.modules.electronicbill.service.api.ElectronicBillApiService;
import org.jeecg.modules.electronicbill.util.ElectronicBillUtil;
import org.jeecg.modules.fee.entity.CustomerRegBill;
import org.jeecg.modules.fee.mapper.CustomerRegBillMapper;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 电子票据业务服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ElectronicBillServiceImpl extends ServiceImpl<ElectronicBillMapper, ElectronicBill> implements IElectronicBillService {

    @Resource
    private ElectronicBillMapper electronicBillMapper;

    @Resource
    private ElectronicBillDetailMapper electronicBillDetailMapper;

    @Resource
    private ElectronicBillWriteoffMapper electronicBillWriteoffMapper;

    @Resource
    private CustomerRegBillMapper customerRegBillMapper;


    @Resource
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;

    @Resource
    private ElectronicBillApiService billApiService;

    @Resource
    private ElectronicBillConfig billConfig;

    @Resource
    private ElectronicBillUtil billUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ElectronicBillResponse applyElectronicBill(CustomerRegBill customerRegBill) {
        log.info("开始为支付单申请电子票据，支付单ID：{}", customerRegBill.getId());

        try {
            // 检查是否已经开过票
            ElectronicBill existingBill = electronicBillMapper.selectByBillId(customerRegBill.getId());
            if (existingBill != null && existingBill.getBillBatchCode() != null) {
                log.warn("支付单{}已经开过电子票据", customerRegBill.getId());
                return ElectronicBillResponse.failure("E0001", "该支付单已经开过电子票据");
            }

            // 判断团检预开票场景
            if ("1".equals(customerRegBill.getAfterPayFlag())) {
                return createTeamPreIssueBill(customerRegBill);
            }

            // 根据业务类型选择开票接口（默认门诊）
            return createOutpatientBill(customerRegBill);

        } catch (Exception e) {
            log.error("申请电子票据失败", e);
            return ElectronicBillResponse.failure("E9999", "申请电子票据失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ElectronicBillResponse createOutpatientBill(CustomerRegBill customerRegBill) {
        log.info("开始门诊电子票据开具，支付单ID：{}", customerRegBill.getId());

        try {
            // 构建门诊开票请求
            OutpatientBillRequest request = buildOutpatientRequest(customerRegBill);

            // 保存电子票据记录
            ElectronicBill electronicBill = saveElectronicBillRecord(customerRegBill, request);

            // 调用开票接口
            ElectronicBillResponse response = billApiService.invoiceOutpatient(request);

            // 处理开票结果
            handleBillResponse(electronicBill, response);

            // 更新支付单状态
            updateCustomerRegBillStatus(customerRegBill, response);

            return response;

        } catch (Exception e) {
            log.error("门诊电子票据开具失败", e);
            return ElectronicBillResponse.failure("E9999", "门诊开票失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ElectronicBillResponse createHospitalizedBill(CustomerRegBill customerRegBill) {
        log.info("开始住院电子票据开具，支付单ID：{}", customerRegBill.getId());

        try {
            // 构建住院开票请求
            HospitalizedBillRequest request = buildHospitalizedRequest(customerRegBill);

            // 保存电子票据记录
            ElectronicBill electronicBill = saveElectronicBillRecord(customerRegBill, request);

            // 调用开票接口
            ElectronicBillResponse response = billApiService.invoiceHospitalized(request);

            // 处理开票结果
            handleBillResponse(electronicBill, response);

            // 更新支付单状态
            updateCustomerRegBillStatus(customerRegBill, response);

            return response;

        } catch (Exception e) {
            log.error("住院电子票据开具失败", e);
            return ElectronicBillResponse.failure("E9999", "住院开票失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ElectronicBillResponse createTeamPreIssueBill(CustomerRegBill customerRegBill) {
        log.info("开始团检预开票据，支付单ID：{}", customerRegBill.getId());

        try {
            // 构建门诊开票请求（团检使用门诊接口）
            OutpatientBillRequest request = buildOutpatientRequest(customerRegBill);
            
            // 设置团检特殊标识
            request.setRemark("团检预开票据，待后续付费确认");

            // 保存电子票据记录
            ElectronicBill electronicBill = saveElectronicBillRecord(customerRegBill, request);
            electronicBill.setTeamFlag(1);
            electronicBill.setPreIssueFlag(1);
            electronicBillMapper.updateById(electronicBill);

            // 调用开票接口
            ElectronicBillResponse response = billApiService.invoiceOutpatient(request);

            // 处理开票结果
            handleBillResponse(electronicBill, response);

            // 更新支付单状态
            updateCustomerRegBillStatus(customerRegBill, response);

            log.info("团检预开票据完成，支付单ID：{}", customerRegBill.getId());
            return response;

        } catch (Exception e) {
            log.error("团检预开票据失败", e);
            return ElectronicBillResponse.failure("E9999", "团检预开票失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WriteOffResponse writeOffElectronicBill(String billId, String reason, String operator) {
        log.info("开始电子票据冲红，支付单ID：{}，原因：{}", billId, reason);

        try {
            // 查询原票据信息
            ElectronicBill originalBill = electronicBillMapper.selectByBillId(billId);
            if (originalBill == null) {
                return WriteOffResponse.failure("E0002", "未找到原电子票据记录");
            }

            if (originalBill.getBillBatchCode() == null || originalBill.getElectronicBillNo() == null) {
                return WriteOffResponse.failure("E0003", "原电子票据信息不完整，无法冲红");
            }

            // 检查是否已经冲红
            if ("1".equals(originalBill.getIsScarlet())) {
                return WriteOffResponse.failure("E0004", "该票据已经冲红");
            }

            // 构建冲红请求
            WriteOffRequest request = new WriteOffRequest();
            request.setBillBatchCode(originalBill.getBillBatchCode());
            request.setBillNo(originalBill.getElectronicBillNo());
            request.setReason(reason);
            request.setOperator(operator);
            request.setBusDateTime(billUtil.getCurrentTimestamp());
            request.setPlaceCode(originalBill.getPlaceCode());

            // 调用冲红接口
            WriteOffResponse response = billApiService.writeOffBill(request);

            // 保存冲红记录
            saveWriteoffRecord(originalBill, request, response);

            // 更新原票据状态
            if (response.isSuccess()) {
                originalBill.setIsScarlet("1");
                originalBill.setUpdateTime(new Date());
                electronicBillMapper.updateById(originalBill);
            }

            return response;

        } catch (Exception e) {
            log.error("电子票据冲红失败", e);
            return WriteOffResponse.failure("E9999", "冲红失败：" + e.getMessage());
        }
    }

    @Override
    public BillStatusResponse queryBillStatus(String billId) {
        try {
            ElectronicBill bill = electronicBillMapper.selectByBillId(billId);
            if (bill == null) {
                return BillStatusResponse.failure("E0002", "未找到电子票据记录");
            }

            return queryBillStatus(bill.getBillBatchCode(), bill.getElectronicBillNo());

        } catch (Exception e) {
            log.error("查询票据状态失败", e);
            return BillStatusResponse.failure("E9999", "查询失败：" + e.getMessage());
        }
    }

    @Override
    public BillStatusResponse queryBillStatus(String billBatchCode, String billNo) {
        try {
            if (billBatchCode == null || billNo == null) {
                return BillStatusResponse.failure("E0005", "票据代码或号码为空");
            }

            BillStatusRequest request = new BillStatusRequest();
            request.setBillBatchCode(billBatchCode);
            request.setBillNo(billNo);

            return billApiService.getBillStatus(request);

        } catch (Exception e) {
            log.error("查询票据状态失败", e);
            return BillStatusResponse.failure("E9999", "查询失败：" + e.getMessage());
        }
    }

    @Override
    public int syncBillStatus() {
        log.info("开始同步票据状态");

        try {
            List<ElectronicBill> needSyncBills = electronicBillMapper.selectNeedSyncBills(100);
            int syncCount = 0;

            for (ElectronicBill bill : needSyncBills) {
                try {
                    BillStatusResponse statusResponse = queryBillStatus(bill.getBillBatchCode(), bill.getElectronicBillNo());
                    
                    if (statusResponse.isSuccess()) {
                        // 更新本地状态
                        bill.setBillStatus(statusResponse.getState());
                        bill.setIsScarlet(statusResponse.getIsScarlet());
                        bill.setSyncStatus(1);
                        bill.setLastSyncTime(new Date());
                        electronicBillMapper.updateById(bill);
                        syncCount++;
                    } else {
                        // 同步失败
                        bill.setSyncStatus(2);
                        bill.setErrorMessage(statusResponse.getErrorMessage());
                        bill.setLastSyncTime(new Date());
                        electronicBillMapper.updateById(bill);
                    }

                } catch (Exception e) {
                    log.warn("同步票据状态失败，票据ID：{}", bill.getId(), e);
                }
            }

            log.info("票据状态同步完成，同步成功：{}条", syncCount);
            return syncCount;

        } catch (Exception e) {
            log.error("同步票据状态异常", e);
            return 0;
        }
    }

    @Override
    public List<ElectronicBillResponse> batchApplyElectronicBill(List<String> billIds) {
        log.info("开始批量申请电子票据，数量：{}", billIds.size());

        List<ElectronicBillResponse> results = new ArrayList<>();

        for (String billId : billIds) {
            try {
                CustomerRegBill customerRegBill = customerRegBillMapper.selectById(billId);
                if (customerRegBill != null) {
                    ElectronicBillResponse response = applyElectronicBill(customerRegBill);
                    results.add(response);
                } else {
                    results.add(ElectronicBillResponse.failure("E0006", "支付单不存在：" + billId));
                }
            } catch (Exception e) {
                log.warn("批量开票失败，支付单ID：{}", billId, e);
                results.add(ElectronicBillResponse.failure("E9999", "开票失败：" + e.getMessage()));
            }
        }

        return results;
    }

    @Override
    public ElectronicBillResponse retryApplyElectronicBill(String billId) {
        log.info("重新申请电子票据，支付单ID：{}", billId);

        try {
            CustomerRegBill customerRegBill = customerRegBillMapper.selectById(billId);
            if (customerRegBill == null) {
                return ElectronicBillResponse.failure("E0006", "支付单不存在");
            }

            // 删除之前失败的记录
            ElectronicBill existingBill = electronicBillMapper.selectByBillId(billId);
            if (existingBill != null && existingBill.getBillBatchCode() == null) {
                electronicBillMapper.deleteById(existingBill.getId());
                electronicBillDetailMapper.deleteByBillId(existingBill.getId());
            }

            return applyElectronicBill(customerRegBill);

        } catch (Exception e) {
            log.error("重新申请电子票据失败", e);
            return ElectronicBillResponse.failure("E9999", "重新申请失败：" + e.getMessage());
        }
    }

    @Override
    public String getBillPdfUrl(String billId) {
        try {
            ElectronicBill bill = electronicBillMapper.selectByBillId(billId);
            return bill != null ? bill.getPictureUrl() : null;
        } catch (Exception e) {
            log.error("获取PDF下载地址失败", e);
            return null;
        }
    }

    @Override
    public String getBillQrCode(String billId) {
        try {
            ElectronicBill bill = electronicBillMapper.selectByBillId(billId);
            return bill != null ? bill.getBillQrCode() : null;
        } catch (Exception e) {
            log.error("获取票据二维码失败", e);
            return null;
        }
    }

    /**
     * 构建门诊开票请求
     */
    private OutpatientBillRequest buildOutpatientRequest(CustomerRegBill customerRegBill) {
        OutpatientBillRequest request = new OutpatientBillRequest();

        // 基础信息
        request.setBusNo(billUtil.generateBusNo("OUT"));
        request.setBusType(billConfig.getBusType().getOutpatient());
        request.setPayer(getPatientName(customerRegBill));
        request.setBusDateTime(billUtil.formatBusDateTime(customerRegBill.getCreateTime()));
        request.setPlaceCode(billUtil.generatePlaceCode(null, billConfig.getDefaultPlaceCode()));
        request.setPayee(customerRegBill.getCreator());
        request.setAuthor(customerRegBill.getCreator());
        request.setTotalAmt(customerRegBill.getAmount());

        // 患者信息
        setPatientInfo(request, customerRegBill);

        // 费用明细
        List<OutpatientBillRequest.BillDetailItem> detailList = buildBillDetails(customerRegBill);
        request.setListDetail(detailList);

        return request;
    }

    /**
     * 构建住院开票请求
     */
    private HospitalizedBillRequest buildHospitalizedRequest(CustomerRegBill customerRegBill) {
        HospitalizedBillRequest request = new HospitalizedBillRequest();

        // 基础信息
        request.setBusNo(billUtil.generateBusNo("IN"));
        request.setBusType(billConfig.getBusType().getHospitalized());
        request.setPayer(getPatientName(customerRegBill));
        request.setBusDateTime(billUtil.formatBusDateTime(customerRegBill.getCreateTime()));
        request.setPlaceCode(billUtil.generatePlaceCode(null, billConfig.getDefaultPlaceCode()));
        request.setPayee(customerRegBill.getCreator());
        request.setAuthor(customerRegBill.getCreator());
        request.setChecker(customerRegBill.getCreator()); // 住院需要复核人
        request.setTotalAmt(customerRegBill.getAmount());

        // 患者信息
        setPatientInfo(request, customerRegBill);

        // 费用明细
        List<OutpatientBillRequest.BillDetailItem> detailList = buildBillDetails(customerRegBill);
        request.setListDetail(detailList);

        return request;
    }

    /**
     * 设置患者信息
     */
    private void setPatientInfo(Object request, CustomerRegBill customerRegBill) {
        // 这里可以根据customerRegBill关联的客户信息设置患者详细信息
        // 暂时使用基础信息
    }

    /**
     * 构建费用明细
     */
    private List<OutpatientBillRequest.BillDetailItem> buildBillDetails(CustomerRegBill customerRegBill) {
        List<OutpatientBillRequest.BillDetailItem> detailList = new ArrayList<>();

        try {
            // 方式1：通过billId直接查询项目组合（推荐）
            LambdaQueryWrapper<CustomerRegItemGroup> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CustomerRegItemGroup::getBillId, customerRegBill.getId());
            List<CustomerRegItemGroup> itemGroups = customerRegItemGroupMapper.selectList(queryWrapper);

            // 方式2：如果方式1没有数据，通过customerRegItemGroupIds查询
            if (itemGroups.isEmpty() && customerRegBill.getCustomerRegItemGroupIds() != null && !customerRegBill.getCustomerRegItemGroupIds().isEmpty()) {
                queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(CustomerRegItemGroup::getId, customerRegBill.getCustomerRegItemGroupIds());
                itemGroups = customerRegItemGroupMapper.selectList(queryWrapper);
            }

            for (CustomerRegItemGroup itemGroup : itemGroups) {
                OutpatientBillRequest.BillDetailItem detail = new OutpatientBillRequest.BillDetailItem();
                
                detail.setCode(itemGroup.getItemGroupId());
                detail.setName(itemGroup.getItemGroupName());
                detail.setStandard(itemGroup.getCheckPartName()); // 规格使用检查部位
                detail.setUnit("次");
                detail.setPrice(itemGroup.getPrice());
                detail.setAmt(itemGroup.getPriceAfterDis());
                detail.setMedCareInstitution("自费"); // 默认自费，实际应该根据医保类型设置
                detail.setBalancedNumber(billUtil.getCurrentTimestamp().substring(0, 6)); // 费款所属期使用年月
                
                // 构建备注字段：医保标识|费款所属期|执行科室|规格
                String remark = billUtil.buildRemarkField(
                    detail.getMedCareInstitution(),
                    detail.getBalancedNumber(),
                    itemGroup.getDepartmentName(),
                    detail.getStandard()
                );
                detail.setRemark(remark);

                detailList.add(detail);
            }

            log.debug("构建电子票据明细完成，支付单ID：{}，明细数量：{}", customerRegBill.getId(), detailList.size());
            
        } catch (Exception e) {
            log.error("构建费用明细失败，支付单ID：{}", customerRegBill.getId(), e);
        }

        return detailList;
    }

    /**
     * 获取患者姓名
     */
    private String getPatientName(CustomerRegBill customerRegBill) {
        // 这里应该根据customerRegBill关联查询患者信息
        // 暂时返回默认值
        return "患者姓名"; // 实际应该从Customer表查询
    }

    /**
     * 保存电子票据记录
     */
    private ElectronicBill saveElectronicBillRecord(CustomerRegBill customerRegBill, Object request) {
        ElectronicBill electronicBill = new ElectronicBill();
        
        electronicBill.setBillId(customerRegBill.getId());
        electronicBill.setBillNo(customerRegBill.getBillNo());
        
        if (request instanceof OutpatientBillRequest) {
            OutpatientBillRequest outRequest = (OutpatientBillRequest) request;
            electronicBill.setBusNo(outRequest.getBusNo());
            electronicBill.setBusType(outRequest.getBusType());
            electronicBill.setPayer(outRequest.getPayer());
            electronicBill.setBusDateTime(outRequest.getBusDateTime());
            electronicBill.setPlaceCode(outRequest.getPlaceCode());
            electronicBill.setPayee(outRequest.getPayee());
            electronicBill.setAuthor(outRequest.getAuthor());
            electronicBill.setChecker(outRequest.getChecker());
            electronicBill.setTotalAmt(outRequest.getTotalAmt());
            electronicBill.setTel(outRequest.getTel());
            electronicBill.setEmail(outRequest.getEmail());
            electronicBill.setCardNo(outRequest.getCardNo());
        }

        electronicBill.setBillStatus("1"); // 初始状态：正常
        electronicBill.setIsScarlet("0"); // 未冲红
        electronicBill.setSyncStatus(0); // 未同步
        electronicBill.setCreateTime(new Date());
        electronicBill.setCreateBy(getCurrentUserId());

        electronicBillMapper.insert(electronicBill);

        return electronicBill;
    }

    /**
     * 处理开票响应
     */
    private void handleBillResponse(ElectronicBill electronicBill, ElectronicBillResponse response) {
        if (response.isSuccess()) {
            // 更新票据信息
            electronicBill.setBillBatchCode(response.getBillBatchCode());
            electronicBill.setElectronicBillNo(response.getBillNo());
            electronicBill.setRandomCode(response.getRandom());
            electronicBill.setIvcDateTime(response.getIvcDateTime());
            electronicBill.setBillQrCode(response.getBillQRCode());
            electronicBill.setPictureUrl(response.getPictureUrl());
            electronicBill.setPictureNetUrl(response.getPictureNetUrl());
            electronicBill.setSyncStatus(1); // 已同步
            electronicBill.setErrorMessage(null);
        } else {
            // 记录错误信息
            electronicBill.setSyncStatus(2); // 同步失败
            electronicBill.setErrorMessage(response.getErrorMessage());
        }

        electronicBill.setUpdateTime(new Date());
        electronicBill.setUpdateBy(getCurrentUserId());
        electronicBillMapper.updateById(electronicBill);
    }

    /**
     * 更新支付单状态
     */
    private void updateCustomerRegBillStatus(CustomerRegBill customerRegBill, ElectronicBillResponse response) {
        if (response.isSuccess()) {
            customerRegBill.setElectronicBillBatchCode(response.getBillBatchCode());
            customerRegBill.setElectronicBillNo(response.getBillNo());
            customerRegBill.setElectronicBillRandom(response.getRandom());
            customerRegBill.setElectronicBillStatus("2"); // 已开具
            customerRegBill.setElectronicBillQrCode(response.getBillQRCode());
            customerRegBill.setElectronicBillPictureUrl(response.getPictureUrl());
            customerRegBill.setElectronicBillIvcTime(billUtil.parseBusDateTime(response.getIvcDateTime()));
            customerRegBill.setElectronicBillError(null);
        } else {
            customerRegBill.setElectronicBillStatus("3"); // 申请失败
            customerRegBill.setElectronicBillError(response.getErrorMessage());
        }

        customerRegBill.setUpdateTime(new Date());
        customerRegBillMapper.updateById(customerRegBill);
    }

    /**
     * 保存冲红记录
     */
    private void saveWriteoffRecord(ElectronicBill originalBill, WriteOffRequest request, WriteOffResponse response) {
        ElectronicBillWriteoff writeoff = new ElectronicBillWriteoff();
        
        writeoff.setOriginalBillId(originalBill.getId());
        writeoff.setOriginalBillBatchCode(request.getBillBatchCode());
        writeoff.setOriginalBillNo(request.getBillNo());
        writeoff.setReason(request.getReason());
        writeoff.setOperator(request.getOperator());
        writeoff.setBusDateTime(request.getBusDateTime());
        writeoff.setPlaceCode(request.getPlaceCode());

        if (response.isSuccess()) {
            writeoff.setScarletBillBatchCode(response.getEScarletBillBatchCode());
            writeoff.setScarletBillNo(response.getEScarletBillNo());
            writeoff.setScarletRandom(response.getEScarletRandom());
            writeoff.setCreateTime(response.getCreateTime());
            writeoff.setBillQrCode(response.getBillQRCode());
            writeoff.setPictureUrl(response.getPictureUrl());
            writeoff.setPictureNetUrl(response.getPictureNetUrl());
            writeoff.setStatus("1"); // 成功
        } else {
            writeoff.setStatus("0"); // 失败
            writeoff.setErrorMessage(response.getErrorMessage());
        }

        writeoff.setCreateBy(getCurrentUserId());
        writeoff.setCreateDate(new Date());

        electronicBillWriteoffMapper.insert(writeoff);
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            return loginUser != null ? loginUser.getId() : "system";
        } catch (Exception e) {
            return "system";
        }
    }

    @Override
    public GetBillByDateResponse getBillsByDate(String startDate, String endDate, String placeCode, 
                                               String busType, String dataType, Integer pageNo, Integer pageSize) {
        log.info("根据开票日期获取开票信息，开始日期：{}，结束日期：{}，开票点：{}，业务类型：{}，数据类型：{}，页码：{}，每页数量：{}", 
                 startDate, endDate, placeCode, busType, dataType, pageNo, pageSize);

        try {
            // 构建请求
            GetBillByDateRequest request = new GetBillByDateRequest();
            request.setBusBgnDate(startDate);
            request.setBusEndDate(endDate);
            request.setPlaceCode(placeCode);
            request.setBusType(busType);
            request.setDataType(dataType);
            request.setPageNo(pageNo);
            request.setPageSize(pageSize);

            // 调用博思平台接口
            GetBillByDateResponse response = billApiService.getBillByIvcDate(request);

            log.info("根据开票日期获取开票信息完成，返回记录数：{}", 
                     response.getMessage() != null ? response.getMessage().getBillList() != null ? 
                     response.getMessage().getBillList().size() : 0 : 0);

            return response;

        } catch (Exception e) {
            log.error("根据开票日期获取开票信息失败", e);
            return GetBillByDateResponse.failure("E9999", "查询失败：" + e.getMessage());
        }
    }

    @Override
    public CheckTotalDataResponse checkTotalData(String startDate, String endDate, String busType, String placeCode) {
        log.info("总笔数核对，开始日期：{}，结束日期：{}，业务类型：{}，开票点：{}", startDate, endDate, busType, placeCode);

        try {
            // 构建请求
            CheckTotalDataRequest request = new CheckTotalDataRequest();
            request.setStartDate(startDate);
            request.setEndDate(endDate);
            request.setBusType(busType);
            request.setPlaceCode(placeCode);

            // 调用博思平台接口
            CheckTotalDataResponse response = billApiService.checkTotalData(request);

            log.info("总笔数核对完成：{}", JSON.toJSONString(response));
            return response;

        } catch (Exception e) {
            log.error("总笔数核对失败", e);
            return CheckTotalDataResponse.failure("E9999", "核对失败：" + e.getMessage());
        }
    }

    @Override
    public CheckWriteOffDataResponse checkWriteOffData(String startDate, String endDate, String busType, String placeCode) {
        log.info("冲红数据核对，开始日期：{}，结束日期：{}，业务类型：{}，开票点：{}", startDate, endDate, busType, placeCode);

        try {
            // 构建请求
            CheckWriteOffDataRequest request = new CheckWriteOffDataRequest();
            request.setStartDate(startDate);
            request.setEndDate(endDate);
            request.setBusType(busType);
            request.setPlaceCode(placeCode);

            // 调用博思平台接口
            CheckWriteOffDataResponse response = billApiService.checkWriteOffData(request);

            log.info("冲红数据核对完成：{}", JSON.toJSONString(response));
            return response;

        } catch (Exception e) {
            log.error("冲红数据核对失败", e);
            return CheckWriteOffDataResponse.failure("E9999", "核对失败：" + e.getMessage());
        }
    }

    @Override
    public GetBillByBusDateResponse getBillsByBusDate(String startDate, String endDate, String placeCode, 
                                                     String busType, String dataType, Integer pageNo, Integer pageSize) {
        log.info("根据业务日期获取开票信息，开始日期：{}，结束日期：{}，开票点：{}，业务类型：{}，数据类型：{}，页码：{}，每页数量：{}", 
                 startDate, endDate, placeCode, busType, dataType, pageNo, pageSize);

        try {
            // 构建请求
            GetBillByBusDateRequest request = new GetBillByBusDateRequest();
            request.setBusBgnDate(startDate);
            request.setBusEndDate(endDate);
            request.setPlaceCode(placeCode);
            request.setBusType(busType);
            request.setDataType(dataType);
            request.setPageNo(pageNo);
            request.setPageSize(pageSize);

            // 调用博思平台接口
            GetBillByBusDateResponse response = billApiService.getBillByBusDate(request);

            log.info("根据业务日期获取开票信息完成，返回记录数：{}", 
                     response.getMessage() != null ? response.getMessage().getBillList() != null ? 
                     response.getMessage().getBillList().size() : 0 : 0);

            return response;

        } catch (Exception e) {
            log.error("根据业务日期获取开票信息失败", e);
            return GetBillByBusDateResponse.failure("E9999", "查询失败：" + e.getMessage());
        }
    }

    @Override
    public CheckTotalDataResponse checkTotalDataByIvcDate(String startDate, String endDate, String busType, String placeCode) {
        log.info("按开票日期总笔数核对，开始日期：{}，结束日期：{}，业务类型：{}，开票点：{}", startDate, endDate, busType, placeCode);

        try {
            // 构建请求
            CheckTotalDataByIvcDateRequest request = new CheckTotalDataByIvcDateRequest();
            request.setStartDate(startDate);
            request.setEndDate(endDate);
            request.setBusType(busType);
            request.setPlaceCode(placeCode);

            // 调用博思平台接口
            CheckTotalDataResponse response = billApiService.checkTotalDataByIvcDate(request);

            log.info("按开票日期总笔数核对完成：{}", JSON.toJSONString(response));
            return response;

        } catch (Exception e) {
            log.error("按开票日期总笔数核对失败", e);
            return CheckTotalDataResponse.failure("E9999", "核对失败：" + e.getMessage());
        }
    }

    @Override
    public CheckWriteOffDataResponse checkWriteOffDataByIvcDate(String startDate, String endDate, String busType, String placeCode) {
        log.info("按开票日期冲红数据核对，开始日期：{}，结束日期：{}，业务类型：{}，开票点：{}", startDate, endDate, busType, placeCode);

        try {
            // 构建请求
            CheckWriteOffDataByIvcDateRequest request = new CheckWriteOffDataByIvcDateRequest();
            request.setStartDate(startDate);
            request.setEndDate(endDate);
            request.setBusType(busType);
            request.setPlaceCode(placeCode);

            // 调用博思平台接口
            CheckWriteOffDataResponse response = billApiService.checkWriteOffDataByIvcDate(request);

            log.info("按开票日期冲红数据核对完成：{}", JSON.toJSONString(response));
            return response;

        } catch (Exception e) {
            log.error("按开票日期冲红数据核对失败", e);
            return CheckWriteOffDataResponse.failure("E9999", "核对失败：" + e.getMessage());
        }
    }
}