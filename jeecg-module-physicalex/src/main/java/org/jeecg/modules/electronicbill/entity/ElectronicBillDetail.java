package org.jeecg.modules.electronicbill.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;  
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子票据明细表
 * 
 * <AUTHOR>
 */
@Data
@TableName("electronic_bill_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "electronic_bill_detail对象", description = "电子票据明细表")
public class ElectronicBillDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "电子票据ID", width = 15)
    @ApiModelProperty(value = "电子票据ID")
    private String billId;

    @Excel(name = "支付记录ID", width = 15)
    @ApiModelProperty(value = "支付记录ID")
    private String payRecordId;

    @Excel(name = "项目组合ID", width = 15)
    @ApiModelProperty(value = "项目组合ID")
    private String itemGroupId;

    @Excel(name = "费用类别编码", width = 15)
    @ApiModelProperty(value = "费用类别编码")
    private String code;

    @Excel(name = "费用类别名称", width = 20)
    @ApiModelProperty(value = "费用类别名称")
    private String name;

    @Excel(name = "规格", width = 15)
    @ApiModelProperty(value = "规格")
    private String standard;

    @Excel(name = "单位", width = 10)
    @ApiModelProperty(value = "单位")
    private String unit;

    @Excel(name = "单价", width = 15)
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @Excel(name = "金额", width = 15)
    @ApiModelProperty(value = "金额")
    private BigDecimal amt;

    @Excel(name = "医保报销类型", width = 15)
    @ApiModelProperty(value = "医保报销类型")
    private String medCareInstitution;

    @Excel(name = "费款所属期", width = 15)
    @ApiModelProperty(value = "费款所属期")
    private String balancedNumber;

    @Excel(name = "执行科室", width = 20)
    @ApiModelProperty(value = "执行科室")
    private String execDeptName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}