package org.jeecg.modules.electronicbill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.electronicbill.entity.ElectronicBillWriteoff;

import java.util.List;

/**
 * 电子票据冲红记录表 Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface ElectronicBillWriteoffMapper extends BaseMapper<ElectronicBillWriteoff> {

    /**
     * 分页查询冲红记录
     */
    Page<ElectronicBillWriteoff> selectWriteoffPage(Page<ElectronicBillWriteoff> page,
                                                    @Param("originalBillNo") String originalBillNo,
                                                    @Param("scarletBillNo") String scarletBillNo,
                                                    @Param("operator") String operator,
                                                    @Param("status") String status,
                                                    @Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);

    /**
     * 根据原票据ID查询冲红记录
     */
    List<ElectronicBillWriteoff> selectByOriginalBillId(@Param("originalBillId") String originalBillId);

    /**
     * 根据原票据号查询冲红记录
     */
    List<ElectronicBillWriteoff> selectByOriginalBillNo(@Param("originalBillNo") String originalBillNo);

    /**
     * 统计冲红次数
     */
    Long countByOriginalBillId(@Param("originalBillId") String originalBillId);

    /**
     * 查询冲红失败的记录
     */
    List<ElectronicBillWriteoff> selectFailedWriteoffs(@Param("limit") Integer limit);
}