package org.jeecg.modules.electronicbill.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子票据开具响应
 * 门诊和住院开票接口通用响应
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据开具响应")
public class ElectronicBillResponse {

    @ApiModelProperty(value = "返回结果标识")
    @JsonProperty("result")
    private String result; // S0000表示成功

    @ApiModelProperty(value = "返回结果内容")
    @JsonProperty("message")
    private String message; // BASE64编码的JSON格式业务参数

    // 解析后的业务参数
    @ApiModelProperty(value = "电子票据种类名称")
    private String billName;

    @ApiModelProperty(value = "电子票据代码")
    private String billBatchCode;

    @ApiModelProperty(value = "电子票据号码")
    private String billNo;

    @ApiModelProperty(value = "电子校验码")
    private String random;

    @ApiModelProperty(value = "开票时间")
    private String ivcDateTime; // 格式：yyyyMMddHHmmssSSS

    @ApiModelProperty(value = "电子票据二维码图片数据")
    private String billQRCode; // BASE64编码

    @ApiModelProperty(value = "电子票据H5页面URL")
    private String pictureUrl;

    @ApiModelProperty(value = "电子票据外网H5页面URL")
    private String pictureNetUrl;

    @ApiModelProperty(value = "告知单二维码")
    private String noticeQRCode; // BASE64编码

    @ApiModelProperty(value = "告知单H5页面URL")
    private String noticePictureUrl;

    @ApiModelProperty(value = "告知单外网H5页面URL")
    private String noticePictureNetUrl;

    // 扩展字段
    @ApiModelProperty(value = "错误代码")
    private String errorCode;

    @ApiModelProperty(value = "错误描述")
    private String errorMessage;

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    /**
     * 创建成功响应
     */
    public static ElectronicBillResponse success() {
        ElectronicBillResponse response = new ElectronicBillResponse();
        response.setResult("S0000");
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static ElectronicBillResponse failure(String errorCode, String errorMessage) {
        ElectronicBillResponse response = new ElectronicBillResponse();
        response.setResult(errorCode);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "S0000".equals(result) || (success != null && success);
    }
}