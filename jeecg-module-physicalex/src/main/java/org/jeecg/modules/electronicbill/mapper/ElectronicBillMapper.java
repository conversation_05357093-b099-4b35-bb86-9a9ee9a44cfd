package org.jeecg.modules.electronicbill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.electronicbill.entity.ElectronicBill;

import java.util.Date;
import java.util.List;

/**
 * 电子票据主表 Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface ElectronicBillMapper extends BaseMapper<ElectronicBill> {

    /**
     * 分页查询电子票据
     */
    Page<ElectronicBill> selectBillPage(Page<ElectronicBill> page,
                                        @Param("busNo") String busNo,
                                        @Param("electronicBillNo") String electronicBillNo,
                                        @Param("billBatchCode") String billBatchCode,
                                        @Param("payer") String payer,
                                        @Param("billStatus") String billStatus,
                                        @Param("teamFlag") Integer teamFlag,
                                        @Param("startDate") String startDate,
                                        @Param("endDate") String endDate);

    /**
     * 根据支付单ID查询电子票据
     */
    ElectronicBill selectByBillId(@Param("billId") String billId);

    /**
     * 根据票据号查询电子票据
     */
    ElectronicBill selectByElectronicBillNo(@Param("electronicBillNo") String electronicBillNo);

    /**
     * 根据票据代码和号码查询
     */
    ElectronicBill selectByBillBatchCodeAndNo(@Param("billBatchCode") String billBatchCode, 
                                              @Param("electronicBillNo") String electronicBillNo);

    /**
     * 查询需要同步状态的票据
     */
    List<ElectronicBill> selectNeedSyncBills(@Param("limit") Integer limit);

    /**
     * 查询团检预开票据
     */
    List<ElectronicBill> selectTeamPreIssueBills(@Param("companyName") String companyName,
                                                 @Param("startDate") String startDate,
                                                 @Param("endDate") String endDate);

    /**
     * 统计票据数量
     */
    Long countByStatus(@Param("billStatus") String billStatus,
                       @Param("startDate") String startDate,
                       @Param("endDate") String endDate);

    /**
     * 统计票据金额
     */
    java.math.BigDecimal sumAmountByStatus(@Param("billStatus") String billStatus,
                                           @Param("startDate") String startDate,
                                           @Param("endDate") String endDate);

    /**
     * 批量更新票据状态
     */
    int batchUpdateStatus(@Param("billIds") List<String> billIds,
                          @Param("billStatus") String billStatus,
                          @Param("updateBy") String updateBy);

    /**
     * 更新票据信息
     */
    int updateBillInfo(@Param("billId") String billId,
                       @Param("billBatchCode") String billBatchCode,
                       @Param("electronicBillNo") String electronicBillNo,
                       @Param("randomCode") String randomCode,
                       @Param("ivcDateTime") String ivcDateTime,
                       @Param("billQrCode") String billQrCode,
                       @Param("pictureUrl") String pictureUrl,
                       @Param("pictureNetUrl") String pictureNetUrl,
                       @Param("billStatus") String billStatus,
                       @Param("updateBy") String updateBy);

    /**
     * 查询开票失败的记录
     */
    List<ElectronicBill> selectFailedBills(@Param("limit") Integer limit);

    /**
     * 统计团检票据汇总信息
     */
    List<java.util.Map<String, Object>> selectTeamBillSummary(@Param("startDate") String startDate,
                                                               @Param("endDate") String endDate);
}