package org.jeecg.modules.electronicbill.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 根据开票日期获取开票信息响应
 * 
 * <AUTHOR>
 */
@Data
public class GetBillByDateResponse {

    /**
     * 返回结果标识
     */
    @JsonProperty("result")
    private String result;

    /**
     * 返回结果内容
     */
    @JsonProperty("message")
    private MessageContent message;

    @Data
    public static class MessageContent {
        /**
         * 总条数（用于分页）
         */
        @JsonProperty("total")
        private Integer total;

        /**
         * 当前页码
         */
        @JsonProperty("pageNo")
        private Integer pageNo;

        /**
         * 开票明细列表
         */
        @JsonProperty("billList")
        private List<BillInfo> billList;
    }

    @Data
    public static class BillInfo {
        /**
         * 业务日期
         * 格式:yyyyMMddHHmmssSSS
         */
        @JsonProperty("busDate")
        private String busDate;

        /**
         * 业务流水号
         */
        @JsonProperty("busNo")
        private String busNo;

        /**
         * 业务标识
         */
        @JsonProperty("busType")
        private String busType;

        /**
         * 开票点编码
         */
        @JsonProperty("placeCode")
        private String placeCode;

        /**
         * 票据种类名称
         */
        @JsonProperty("billName")
        private String billName;

        /**
         * 票据代码
         */
        @JsonProperty("billBatchCode")
        private String billBatchCode;

        /**
         * 票据号码
         */
        @JsonProperty("billNo")
        private String billNo;

        /**
         * 校验码
         */
        @JsonProperty("random")
        private String random;

        /**
         * 总金额
         */
        @JsonProperty("totalAmt")
        private BigDecimal totalAmt;

        /**
         * 开票时间
         * yyyyMMddHHmmssSSS
         */
        @JsonProperty("ivcDateTime")
        private String ivcDateTime;

        /**
         * 数据类型
         * 1 正常电子、2 电子红票、3 换开纸质、4 换开纸质红票、5空白纸质
         */
        @JsonProperty("dataType")
        private String dataType;

        /**
         * 状态
         * 1正常 、2作废 、3冲红
         */
        @JsonProperty("state")
        private String state;

        /**
         * 关联电子票据代码
         */
        @JsonProperty("relateBillBatchCode")
        private String relateBillBatchCode;

        /**
         * 关联电子票据号码
         */
        @JsonProperty("relateBillNo")
        private String relateBillNo;
    }

    /**
     * 成功响应构造方法
     */
    public static GetBillByDateResponse success(MessageContent message) {
        GetBillByDateResponse response = new GetBillByDateResponse();
        response.setResult("S0000");
        response.setMessage(message);
        return response;
    }

    /**
     * 失败响应构造方法
     */
    public static GetBillByDateResponse failure(String errorCode, String errorMessage) {
        GetBillByDateResponse response = new GetBillByDateResponse();
        response.setResult(errorCode);
        MessageContent message = new MessageContent();
        // 简化处理，错误信息直接放在message中的toString
        response.setMessage(message);
        return response;
    }
}