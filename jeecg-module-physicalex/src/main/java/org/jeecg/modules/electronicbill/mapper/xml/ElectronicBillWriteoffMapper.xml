<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.electronicbill.mapper.ElectronicBillWriteoffMapper">

    <!-- 通用查询结果映射 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.electronicbill.entity.ElectronicBillWriteoff">
        <id column="id" property="id" />
        <result column="original_bill_id" property="originalBillId" />
        <result column="original_bill_batch_code" property="originalBillBatchCode" />
        <result column="original_bill_no" property="originalBillNo" />
        <result column="scarlet_bill_batch_code" property="scarletBillBatchCode" />
        <result column="scarlet_bill_no" property="scarletBillNo" />
        <result column="scarlet_random" property="scarletRandom" />
        <result column="reason" property="reason" />
        <result column="operator" property="operator" />
        <result column="bus_date_time" property="busDateTime" />
        <result column="place_code" property="placeCode" />
        <result column="create_time" property="createTime" />
        <result column="bill_qr_code" property="billQrCode" />
        <result column="picture_url" property="pictureUrl" />
        <result column="picture_net_url" property="pictureNetUrl" />
        <result column="status" property="status" />
        <result column="error_message" property="errorMessage" />
        <result column="create_by" property="createBy" />
        <result column="create_date" property="createDate" />
    </resultMap>

    <!-- 通用查询列 -->
    <sql id="Base_Column_List">
        id, original_bill_id, original_bill_batch_code, original_bill_no, scarlet_bill_batch_code, 
        scarlet_bill_no, scarlet_random, reason, operator, bus_date_time, place_code, create_time,
        bill_qr_code, picture_url, picture_net_url, status, error_message, create_by, create_date
    </sql>

    <!-- 根据原票据ID查询冲红记录 -->
    <select id="selectByOriginalBillId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_writeoff
        WHERE original_bill_id = #{originalBillId}
        ORDER BY create_date DESC
    </select>

    <!-- 根据原票据ID列表查询冲红记录 -->
    <select id="selectByOriginalBillIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_writeoff
        WHERE original_bill_id IN
        <foreach collection="originalBillIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        ORDER BY create_date DESC
    </select>

    <!-- 根据原票据号查询冲红记录 -->
    <select id="selectByOriginalBillNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_writeoff
        WHERE original_bill_no = #{originalBillNo}
        ORDER BY create_date DESC
    </select>

    <!-- 根据红票号查询冲红记录 -->
    <select id="selectByScarletBillNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_writeoff
        WHERE scarlet_bill_no = #{scarletBillNo}
        ORDER BY create_date DESC
    </select>

    <!-- 根据操作人查询冲红记录 -->
    <select id="selectByOperator" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_writeoff
        WHERE operator = #{operator}
        ORDER BY create_date DESC
    </select>

    <!-- 根据状态查询冲红记录 -->
    <select id="selectByStatus" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_writeoff
        WHERE status = #{status}
        ORDER BY create_date DESC
    </select>

    <!-- 检查票据是否已冲红 -->
    <select id="checkWriteoffExists" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM electronic_bill_writeoff
        WHERE original_bill_id = #{originalBillId} AND status = '1'
    </select>

    <!-- 根据时间范围查询冲红记录 -->
    <select id="selectByTimeRange" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_writeoff
        WHERE create_date BETWEEN #{startTime} AND #{endTime}
        ORDER BY create_date DESC
    </select>

    <!-- 条件查询冲红记录 -->
    <select id="selectByCondition" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_writeoff
        <where>
            <if test="originalBillId != null and originalBillId != ''">
                AND original_bill_id = #{originalBillId}
            </if>
            <if test="originalBillNo != null and originalBillNo != ''">
                AND original_bill_no = #{originalBillNo}
            </if>
            <if test="scarletBillNo != null and scarletBillNo != ''">
                AND scarlet_bill_no = #{scarletBillNo}
            </if>
            <if test="operator != null and operator != ''">
                AND operator LIKE CONCAT('%', #{operator}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="placeCode != null and placeCode != ''">
                AND place_code = #{placeCode}
            </if>
            <if test="startDate != null">
                AND create_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND create_date &lt;= #{endDate}
            </if>
            <if test="reason != null and reason != ''">
                AND reason LIKE CONCAT('%', #{reason}, '%')
            </if>
        </where>
        ORDER BY create_date DESC
    </select>

    <!-- 统计冲红记录数量 -->
    <select id="countByCondition" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM electronic_bill_writeoff
        <where>
            <if test="originalBillId != null and originalBillId != ''">
                AND original_bill_id = #{originalBillId}
            </if>
            <if test="operator != null and operator != ''">
                AND operator LIKE CONCAT('%', #{operator}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startDate != null">
                AND create_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND create_date &lt;= #{endDate}
            </if>
        </where>
    </select>

    <!-- 根据操作人统计冲红数量 -->
    <select id="countByOperator" parameterType="map" resultType="map">
        SELECT 
            operator,
            COUNT(*) as writeoffCount,
            COUNT(CASE WHEN status = '1' THEN 1 END) as successCount,
            COUNT(CASE WHEN status = '0' THEN 1 END) as failureCount
        FROM electronic_bill_writeoff
        <where>
            <if test="startDate != null">
                AND create_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND create_date &lt;= #{endDate}
            </if>
        </where>
        GROUP BY operator
        ORDER BY writeoffCount DESC
    </select>

    <!-- 根据日期统计冲红数量 -->
    <select id="countByDate" parameterType="map" resultType="map">
        SELECT 
            DATE(create_date) as writeoffDate,
            COUNT(*) as writeoffCount,
            COUNT(CASE WHEN status = '1' THEN 1 END) as successCount,
            COUNT(CASE WHEN status = '0' THEN 1 END) as failureCount
        FROM electronic_bill_writeoff
        <where>
            <if test="startDate != null">
                AND create_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND create_date &lt;= #{endDate}
            </if>
        </where>
        GROUP BY DATE(create_date)
        ORDER BY writeoffDate DESC
    </select>

    <!-- 批量插入冲红记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO electronic_bill_writeoff (
            id, original_bill_id, original_bill_batch_code, original_bill_no, scarlet_bill_batch_code,
            scarlet_bill_no, scarlet_random, reason, operator, bus_date_time, place_code, create_time,
            bill_qr_code, picture_url, picture_net_url, status, error_message, create_by, create_date
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.originalBillId}, #{item.originalBillBatchCode}, #{item.originalBillNo},
                #{item.scarletBillBatchCode}, #{item.scarletBillNo}, #{item.scarletRandom}, #{item.reason},
                #{item.operator}, #{item.busDateTime}, #{item.placeCode}, #{item.createTime},
                #{item.billQrCode}, #{item.pictureUrl}, #{item.pictureNetUrl}, #{item.status},
                #{item.errorMessage}, #{item.createBy}, #{item.createDate}
            )
        </foreach>
    </insert>

    <!-- 更新冲红状态 -->
    <update id="updateStatus" parameterType="map">
        UPDATE electronic_bill_writeoff 
        SET status = #{status}
        <if test="errorMessage != null">
            , error_message = #{errorMessage}
        </if>
        <if test="scarletBillBatchCode != null">
            , scarlet_bill_batch_code = #{scarletBillBatchCode}
        </if>
        <if test="scarletBillNo != null">
            , scarlet_bill_no = #{scarletBillNo}
        </if>
        <if test="scarletRandom != null">
            , scarlet_random = #{scarletRandom}
        </if>
        <if test="billQrCode != null">
            , bill_qr_code = #{billQrCode}
        </if>
        <if test="pictureUrl != null">
            , picture_url = #{pictureUrl}
        </if>
        <if test="pictureNetUrl != null">
            , picture_net_url = #{pictureNetUrl}
        </if>
        WHERE id = #{id}
    </update>

    <!-- 根据原票据ID删除冲红记录 -->
    <delete id="deleteByOriginalBillId" parameterType="java.lang.String">
        DELETE FROM electronic_bill_writeoff WHERE original_bill_id = #{originalBillId}
    </delete>

    <!-- 根据时间范围删除冲红记录 -->
    <delete id="deleteByTimeRange" parameterType="map">
        DELETE FROM electronic_bill_writeoff 
        WHERE create_date BETWEEN #{startTime} AND #{endTime}
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </delete>

</mapper>