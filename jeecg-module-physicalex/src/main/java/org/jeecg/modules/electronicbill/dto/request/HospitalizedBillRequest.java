package org.jeecg.modules.electronicbill.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 住院电子票据开具请求
 * 基于接口标识：invEBillHospitalized
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("住院电子票据开具请求")
public class HospitalizedBillRequest {

    // ========== 票据信息 ==========
    
    @ApiModelProperty(value = "业务流水号", required = true)
    @NotBlank(message = "业务流水号不能为空")
    @JsonProperty("busNo")
    private String busNo;

    @ApiModelProperty(value = "业务标识", required = true)
    @NotBlank(message = "业务标识不能为空")
    @JsonProperty("busType")
    private String busType = "01"; // 住院固定值

    @ApiModelProperty(value = "患者姓名", required = true)
    @NotBlank(message = "患者姓名不能为空")
    @JsonProperty("payer")
    private String payer;

    @ApiModelProperty(value = "业务发生时间", required = true)
    @NotBlank(message = "业务发生时间不能为空")
    @JsonProperty("busDateTime")
    private String busDateTime; // 格式：yyyyMMddHHmmssSSS

    @ApiModelProperty(value = "开票点编码", required = true)
    @NotBlank(message = "开票点编码不能为空")
    @JsonProperty("placeCode")
    private String placeCode;

    @ApiModelProperty(value = "收费员", required = true)
    @NotBlank(message = "收费员不能为空")
    @JsonProperty("payee")
    private String payee;

    @ApiModelProperty(value = "票据编制人", required = true)
    @NotBlank(message = "票据编制人不能为空")
    @JsonProperty("author")
    private String author;

    @ApiModelProperty(value = "票据复核人", required = true)
    @NotBlank(message = "票据复核人不能为空")
    @JsonProperty("checker")
    private String checker;

    @ApiModelProperty(value = "开票总金额", required = true)
    @NotNull(message = "开票总金额不能为空")
    @JsonProperty("totalAmt")
    private BigDecimal totalAmt;

    @ApiModelProperty(value = "备注")
    @JsonProperty("remark")
    private String remark;

    // ========== 缴费信息 ==========

    @ApiModelProperty(value = "患者支付宝账户")
    @JsonProperty("alipayCode")
    private String alipayCode;

    @ApiModelProperty(value = "微信支付订单号")
    @JsonProperty("weChatOrderNo")
    private String weChatOrderNo;

    @ApiModelProperty(value = "微信医保支付订单号")
    @JsonProperty("weChatMedTransNo")
    private String weChatMedTransNo;

    @ApiModelProperty(value = "微信公众号或小程序用户ID")
    @JsonProperty("openID")
    private String openID;

    @ApiModelProperty(value = "银联支付信息")
    @JsonProperty("unionPayOrderNo")
    private String unionPayOrderNo;

    @ApiModelProperty(value = "支付信息列表")
    @JsonProperty("payOrderInfo")
    private List<OutpatientBillRequest.PayOrderInfo> payOrderInfo;

    // ========== 通知信息 ==========

    @ApiModelProperty(value = "患者手机号码")
    @JsonProperty("tel")
    private String tel;

    @ApiModelProperty(value = "邮箱")
    @JsonProperty("email")
    private String email;

    // ========== 就诊信息 ==========

    @ApiModelProperty(value = "患者身份证号")
    @JsonProperty("cardNo")
    private String cardNo;

    @ApiModelProperty(value = "卡类型")
    @JsonProperty("cardType")
    private String cardType;

    @ApiModelProperty(value = "住院号")
    @JsonProperty("inHospitalNo")
    private String inHospitalNo;

    @ApiModelProperty(value = "入院日期")
    @JsonProperty("inHospitalDate")
    private String inHospitalDate;

    @ApiModelProperty(value = "出院日期")
    @JsonProperty("outHospitalDate")
    private String outHospitalDate;

    @ApiModelProperty(value = "住院天数")
    @JsonProperty("inHospitalDays")
    private Integer inHospitalDays;

    @ApiModelProperty(value = "科室代码")
    @JsonProperty("deptCode")
    private String deptCode;

    @ApiModelProperty(value = "科室名称")
    @JsonProperty("deptName")
    private String deptName;

    @ApiModelProperty(value = "病区")
    @JsonProperty("wardCode")
    private String wardCode;

    @ApiModelProperty(value = "床位号")
    @JsonProperty("bedNo")
    private String bedNo;

    @ApiModelProperty(value = "医生代码")
    @JsonProperty("doctorCode")
    private String doctorCode;

    @ApiModelProperty(value = "医生姓名")
    @JsonProperty("doctorName")
    private String doctorName;

    // ========== 内蒙特殊字段 ==========

    @ApiModelProperty(value = "是否可流通")
    @JsonProperty("isArrears")
    private String isArrears;

    @ApiModelProperty(value = "不可流通原因")
    @JsonProperty("arrearsReason")
    private String arrearsReason;

    @ApiModelProperty(value = "交费日期")
    @JsonProperty("chargeDate")
    private String chargeDate;

    // ========== 费用类别 ==========

    @ApiModelProperty(value = "费用类别列表", required = true)
    @NotNull(message = "费用类别列表不能为空")
    @JsonProperty("listDetail")
    private List<OutpatientBillRequest.BillDetailItem> listDetail;
}