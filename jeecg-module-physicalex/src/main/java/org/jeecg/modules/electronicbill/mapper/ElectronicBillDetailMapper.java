package org.jeecg.modules.electronicbill.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.electronicbill.entity.ElectronicBillDetail;

import java.util.List;

/**
 * 电子票据明细表 Mapper接口
 * <AUTHOR>
 */
@Mapper
public interface ElectronicBillDetailMapper extends BaseMapper<ElectronicBillDetail> {

    /**
     * 根据票据ID查询明细
     */
    List<ElectronicBillDetail> selectByBillId(@Param("billId") String billId);


    /**
     * 批量插入明细
     */
    int batchInsert(@Param("detailList") List<ElectronicBillDetail> detailList);

    /**
     * 统计明细总金额
     */
    java.math.BigDecimal sumAmountByBillId(@Param("billId") String billId);

    /**
     * 根据项目组合ID查询明细
     */
    List<ElectronicBillDetail> selectByItemGroupId(@Param("itemGroupId") String itemGroupId);

    /**
     * 删除票据相关明细
     */
    int deleteByBillId(@Param("billId") String billId);
}