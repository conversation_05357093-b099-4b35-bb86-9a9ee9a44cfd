<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.electronicbill.mapper.ElectronicBillMapper">

    <resultMap id="BaseResultMap" type="org.jeecg.modules.electronicbill.entity.ElectronicBill">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="bill_id" property="billId" jdbcType="VARCHAR"/>
        <result column="bill_no" property="billNo" jdbcType="VARCHAR"/>
        <result column="bus_no" property="busNo" jdbcType="VARCHAR"/>
        <result column="bill_batch_code" property="billBatchCode" jdbcType="VARCHAR"/>
        <result column="electronic_bill_no" property="electronicBillNo" jdbcType="VARCHAR"/>
        <result column="random_code" property="randomCode" jdbcType="VARCHAR"/>
        <result column="bus_type" property="busType" jdbcType="VARCHAR"/>
        <result column="payer" property="payer" jdbcType="VARCHAR"/>
        <result column="bus_date_time" property="busDateTime" jdbcType="VARCHAR"/>
        <result column="place_code" property="placeCode" jdbcType="VARCHAR"/>
        <result column="payee" property="payee" jdbcType="VARCHAR"/>
        <result column="author" property="author" jdbcType="VARCHAR"/>
        <result column="checker" property="checker" jdbcType="VARCHAR"/>
        <result column="total_amt" property="totalAmt" jdbcType="DECIMAL"/>
        <result column="bill_status" property="billStatus" jdbcType="VARCHAR"/>
        <result column="is_scarlet" property="isScarlet" jdbcType="VARCHAR"/>
        <result column="ivc_date_time" property="ivcDateTime" jdbcType="VARCHAR"/>
        <result column="bill_qr_code" property="billQrCode" jdbcType="LONGVARCHAR"/>
        <result column="picture_url" property="pictureUrl" jdbcType="VARCHAR"/>
        <result column="picture_net_url" property="pictureNetUrl" jdbcType="VARCHAR"/>
        <result column="team_flag" property="teamFlag" jdbcType="INTEGER"/>
        <result column="pre_issue_flag" property="preIssueFlag" jdbcType="INTEGER"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="card_no" property="cardNo" jdbcType="VARCHAR"/>
        <result column="tel" property="tel" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="dept_code" property="deptCode" jdbcType="VARCHAR"/>
        <result column="dept_name" property="deptName" jdbcType="VARCHAR"/>
        <result column="doctor_code" property="doctorCode" jdbcType="VARCHAR"/>
        <result column="doctor_name" property="doctorName" jdbcType="VARCHAR"/>
        <result column="is_arrears" property="isArrears" jdbcType="VARCHAR"/>
        <result column="arrears_reason" property="arrearsReason" jdbcType="VARCHAR"/>
        <result column="charge_date" property="chargeDate" jdbcType="VARCHAR"/>
        <result column="in_hospital_no" property="inHospitalNo" jdbcType="VARCHAR"/>
        <result column="in_hospital_date" property="inHospitalDate" jdbcType="VARCHAR"/>
        <result column="out_hospital_date" property="outHospitalDate" jdbcType="VARCHAR"/>
        <result column="in_hospital_days" property="inHospitalDays" jdbcType="INTEGER"/>
        <result column="ward_code" property="wardCode" jdbcType="VARCHAR"/>
        <result column="bed_no" property="bedNo" jdbcType="VARCHAR"/>
        <result column="sync_status" property="syncStatus" jdbcType="INTEGER"/>
        <result column="last_sync_time" property="lastSyncTime" jdbcType="TIMESTAMP"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 分页查询电子票据 -->
    <select id="selectBillPage" resultMap="BaseResultMap">
        SELECT * FROM electronic_bill
        <where>
            <if test="busNo != null and busNo != ''">
                AND bus_no LIKE CONCAT('%', #{busNo}, '%')
            </if>
            <if test="electronicBillNo != null and electronicBillNo != ''">
                AND electronic_bill_no LIKE CONCAT('%', #{electronicBillNo}, '%')
            </if>
            <if test="billBatchCode != null and billBatchCode != ''">
                AND bill_batch_code = #{billBatchCode}
            </if>
            <if test="payer != null and payer != ''">
                AND payer LIKE CONCAT('%', #{payer}, '%')
            </if>
            <if test="billStatus != null and billStatus != ''">
                AND bill_status = #{billStatus}
            </if>
            <if test="teamFlag != null">
                AND team_flag = #{teamFlag}
            </if>
            <if test="startDate != null and startDate != ''">
                AND bus_date_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND bus_date_time &lt;= #{endDate}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据支付单ID查询 -->
    <select id="selectByBillId" resultMap="BaseResultMap">
        SELECT * FROM electronic_bill WHERE bill_id = #{billId}
    </select>

    <!-- 根据票据号查询 -->
    <select id="selectByElectronicBillNo" resultMap="BaseResultMap">
        SELECT * FROM electronic_bill WHERE electronic_bill_no = #{electronicBillNo}
    </select>

    <!-- 根据票据代码和号码查询 -->
    <select id="selectByBillBatchCodeAndNo" resultMap="BaseResultMap">
        SELECT * FROM electronic_bill 
        WHERE bill_batch_code = #{billBatchCode} AND electronic_bill_no = #{electronicBillNo}
    </select>

    <!-- 查询需要同步状态的票据 -->
    <select id="selectNeedSyncBills" resultMap="BaseResultMap">
        SELECT * FROM electronic_bill 
        WHERE sync_status = 0 OR (sync_status = 2 AND last_sync_time &lt; DATE_SUB(NOW(), INTERVAL 1 HOUR))
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 查询团检预开票据 -->
    <select id="selectTeamPreIssueBills" resultMap="BaseResultMap">
        SELECT * FROM electronic_bill 
        WHERE team_flag = 1 AND pre_issue_flag = 1
        <if test="companyName != null and companyName != ''">
            AND company_name LIKE CONCAT('%', #{companyName}, '%')
        </if>
        <if test="startDate != null and startDate != ''">
            AND bus_date_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND bus_date_time &lt;= #{endDate}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 统计票据数量 -->
    <select id="countByStatus" resultType="java.lang.Long">
        SELECT COUNT(*) FROM electronic_bill
        <where>
            <if test="billStatus != null and billStatus != ''">
                AND bill_status = #{billStatus}
            </if>
            <if test="startDate != null and startDate != ''">
                AND bus_date_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND bus_date_time &lt;= #{endDate}
            </if>
        </where>
    </select>

    <!-- 统计票据金额 -->
    <select id="sumAmountByStatus" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(total_amt), 0) FROM electronic_bill
        <where>
            <if test="billStatus != null and billStatus != ''">
                AND bill_status = #{billStatus}
            </if>
            <if test="startDate != null and startDate != ''">
                AND bus_date_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != ''">
                AND bus_date_time &lt;= #{endDate}
            </if>
        </where>
    </select>

    <!-- 批量更新票据状态 -->
    <update id="batchUpdateStatus">
        UPDATE electronic_bill 
        SET bill_status = #{billStatus}, update_by = #{updateBy}, update_time = NOW()
        WHERE id IN
        <foreach collection="billIds" item="billId" open="(" close=")" separator=",">
            #{billId}
        </foreach>
    </update>

    <!-- 更新票据信息 -->
    <update id="updateBillInfo">
        UPDATE electronic_bill 
        SET 
            bill_batch_code = #{billBatchCode},
            electronic_bill_no = #{electronicBillNo},
            random_code = #{randomCode},
            ivc_date_time = #{ivcDateTime},
            bill_qr_code = #{billQrCode},
            picture_url = #{pictureUrl},
            picture_net_url = #{pictureNetUrl},
            bill_status = #{billStatus},
            update_by = #{updateBy},
            update_time = NOW()
        WHERE bill_id = #{billId}
    </update>

    <!-- 查询开票失败的记录 -->
    <select id="selectFailedBills" resultMap="BaseResultMap">
        SELECT * FROM electronic_bill 
        WHERE sync_status = 2 OR (bill_batch_code IS NULL AND create_time &lt; DATE_SUB(NOW(), INTERVAL 10 MINUTE))
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 统计团检票据汇总信息 -->
    <select id="selectTeamBillSummary" resultType="java.util.Map">
        SELECT 
            company_name,
            COUNT(*) as bill_count,
            SUM(total_amt) as total_amount,
            SUM(CASE WHEN bill_status = '1' THEN 1 ELSE 0 END) as normal_count,
            SUM(CASE WHEN bill_status = '2' THEN 1 ELSE 0 END) as void_count,
            SUM(CASE WHEN is_scarlet = '1' THEN 1 ELSE 0 END) as scarlet_count
        FROM electronic_bill 
        WHERE team_flag = 1
        <if test="startDate != null and startDate != ''">
            AND bus_date_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            AND bus_date_time &lt;= #{endDate}
        </if>
        GROUP BY company_name
        ORDER BY total_amount DESC
    </select>

</mapper>