package org.jeecg.modules.electronicbill.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 电子票据状态查询请求
 * 基于接口标识：getEBillStatesByBillInfo
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据状态查询请求")
public class BillStatusRequest {

    @ApiModelProperty(value = "电子票据代码", required = true)
    @NotBlank(message = "电子票据代码不能为空")
    @JsonProperty("billBatchCode")
    private String billBatchCode;

    @ApiModelProperty(value = "电子票据号码", required = true)
    @NotBlank(message = "电子票据号码不能为空")
    @JsonProperty("billNo")
    private String billNo;
}