package org.jeecg.modules.electronicbill.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 电子票据工具类
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class ElectronicBillUtil {

    private static final String CHARSET = "UTF-8";
    private static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmssSSS";

    /**
     * 生成序列号
     */
    public String generateSerialNumber() {
        return "SN" + System.currentTimeMillis() + String.format("%03d", new Random().nextInt(1000));
    }

    /**
     * 获取当前时间戳
     */
    public String getCurrentTimestamp() {
        return new SimpleDateFormat(TIMESTAMP_FORMAT).format(new Date());
    }

    /**
     * 生成签名
     */
    public String generateSignature(Map<String, Object> params, String privateKey) {
        try {
            // 排序参数
            TreeMap<String, Object> sortedParams = new TreeMap<>(params);
            
            // 构建签名字符串
            StringBuilder signBuilder = new StringBuilder();
            for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
                if (!"signature".equals(entry.getKey()) && entry.getValue() != null) {
                    signBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
                }
            }
            
            // 添加私钥
            signBuilder.append("key=").append(privateKey);
            
            String signString = signBuilder.toString();
            log.debug("签名字符串：{}", signString);
            
            // MD5加密
            return md5(signString).toUpperCase();
            
        } catch (Exception e) {
            log.error("生成签名失败", e);
            return "";
        }
    }

    /**
     * 验证签名
     */
    public boolean verifySignature(Map<String, Object> params, String signature, String privateKey) {
        String expectedSignature = generateSignature(params, privateKey);
        return expectedSignature.equals(signature);
    }

    /**
     * MD5加密
     */
    private String md5(String input) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] digest = md.digest(input.getBytes(CHARSET));
        
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 格式化业务时间
     */
    public String formatBusDateTime(Date date) {
        if (date == null) {
            date = new Date();
        }
        return new SimpleDateFormat(TIMESTAMP_FORMAT).format(date);
    }

    /**
     * 解析业务时间
     */
    public Date parseBusDateTime(String busDateTime) {
        try {
            return new SimpleDateFormat(TIMESTAMP_FORMAT).parse(busDateTime);
        } catch (Exception e) {
            log.warn("解析业务时间失败：{}", busDateTime);
            return null;
        }
    }

    /**
     * 构建业务流水号
     */
    public String generateBusNo(String prefix) {
        String timestamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        String random = String.format("%04d", new Random().nextInt(10000));
        return (prefix != null ? prefix : "BUS") + timestamp + random;
    }

    /**
     * 检查必填字段
     */
    public void validateRequired(Object obj, String... fieldNames) {
        try {
            Map<String, Object> map = JSON.parseObject(JSON.toJSONString(obj), Map.class);
            
            List<String> missingFields = new ArrayList<>();
            for (String fieldName : fieldNames) {
                Object value = map.get(fieldName);
                if (value == null || (value instanceof String && ((String) value).trim().isEmpty())) {
                    missingFields.add(fieldName);
                }
            }
            
            if (!missingFields.isEmpty()) {
                throw new IllegalArgumentException("必填字段不能为空：" + String.join(", ", missingFields));
            }
            
        } catch (Exception e) {
            throw new IllegalArgumentException("字段验证失败：" + e.getMessage());
        }
    }

    /**
     * 格式化金额（保留2位小数）
     */
    public String formatAmount(java.math.BigDecimal amount) {
        if (amount == null) {
            return "0.00";
        }
        return amount.setScale(2, java.math.BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 生成开票点编码
     */
    public String generatePlaceCode(String deptCode, String defaultPlaceCode) {
        if (deptCode != null && !deptCode.trim().isEmpty()) {
            return "PLACE_" + deptCode;
        }
        return defaultPlaceCode;
    }

    /**
     * 构建医保标识|费款所属期|执行科室|规格 的备注字段
     */
    public String buildRemarkField(String medCareInstitution, String balancedNumber, 
                                   String execDeptName, String standard) {
        StringBuilder remark = new StringBuilder();
        
        if (medCareInstitution != null) {
            remark.append(medCareInstitution);
        }
        remark.append("|");
        
        if (balancedNumber != null) {
            remark.append(balancedNumber);
        }
        remark.append("|");
        
        if (execDeptName != null) {
            remark.append(execDeptName);
        }
        remark.append("|");
        
        if (standard != null) {
            remark.append(standard);
        }
        
        return remark.toString();
    }
}