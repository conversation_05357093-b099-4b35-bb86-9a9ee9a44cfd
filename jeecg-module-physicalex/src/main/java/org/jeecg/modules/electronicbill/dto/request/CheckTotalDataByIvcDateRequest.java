package org.jeecg.modules.electronicbill.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 按开票日期总笔数核对请求
 * 服务标识：checkTotalDataByIvcDate
 * 
 * <AUTHOR>
 */
@Data
public class CheckTotalDataByIvcDateRequest {

    /**
     * 起始日期
     * 格式：yyyyMMdd
     */
    @JsonProperty("startDate")
    @NotBlank(message = "起始日期不能为空")
    private String startDate;

    /**
     * 截止日期
     * 格式：yyyyMMdd
     */
    @JsonProperty("endDate")
    @NotBlank(message = "截止日期不能为空")
    private String endDate;

    /**
     * 业务标识
     * 01住院、02门诊、03急诊、05门特、04体检中心
     */
    @JsonProperty("busType")
    private String busType;

    /**
     * 开票点编码
     */
    @JsonProperty("placeCode")
    private String placeCode;
}