package org.jeecg.modules.electronicbill.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 电子票据主表
 * 基于博思电子票据接口规范设计
 * 
 * <AUTHOR>
 */
@Data
@TableName("electronic_bill")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "electronic_bill对象", description = "电子票据主表")
public class ElectronicBill implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    @Excel(name = "关联支付单ID", width = 15)
    @ApiModelProperty(value = "关联支付单ID")
    private String billId;

    @Excel(name = "支付单号", width = 15)
    @ApiModelProperty(value = "支付单号")
    private String billNo;

    @Excel(name = "业务流水号", width = 20)
    @ApiModelProperty(value = "业务流水号")
    private String busNo;

    @Excel(name = "电子票据代码", width = 20)
    @ApiModelProperty(value = "电子票据代码")
    private String billBatchCode;

    @Excel(name = "电子票据号码", width = 20)
    @ApiModelProperty(value = "电子票据号码")
    private String electronicBillNo;

    @Excel(name = "电子校验码", width = 20)
    @ApiModelProperty(value = "电子校验码")
    private String randomCode;

    @Excel(name = "业务标识", width = 15)
    @ApiModelProperty(value = "业务标识：01-住院,02-门诊")
    private String busType;

    @Excel(name = "患者姓名", width = 15)
    @ApiModelProperty(value = "患者姓名")
    private String payer;

    @Excel(name = "业务发生时间", width = 20)
    @ApiModelProperty(value = "业务发生时间")
    private String busDateTime; // 格式：yyyyMMddHHmmssSSS

    @Excel(name = "开票点编码", width = 15)
    @ApiModelProperty(value = "开票点编码")
    private String placeCode;

    @Excel(name = "收费员", width = 15)
    @ApiModelProperty(value = "收费员")
    private String payee;

    @Excel(name = "票据编制人", width = 15)
    @ApiModelProperty(value = "票据编制人")
    private String author;

    @Excel(name = "票据复核人", width = 15)
    @ApiModelProperty(value = "票据复核人")
    private String checker;

    @Excel(name = "开票总金额", width = 15)
    @ApiModelProperty(value = "开票总金额")
    private BigDecimal totalAmt;

    @Excel(name = "票据状态", width = 15)
    @ApiModelProperty(value = "票据状态：1-正常，2-作废")
    private String billStatus;

    @Excel(name = "是否已开红票", width = 15)
    @ApiModelProperty(value = "是否已开红票：0-未开红票，1-已开红票")
    private String isScarlet;

    @Excel(name = "开票时间", width = 20)
    @ApiModelProperty(value = "开票时间")
    private String ivcDateTime; // 格式：yyyyMMddHHmmssSSS

    @ApiModelProperty(value = "电子票据二维码图片数据")
    private String billQrCode; // BASE64编码

    @Excel(name = "电子票据H5页面URL", width = 30)
    @ApiModelProperty(value = "电子票据H5页面URL")
    private String pictureUrl;

    @Excel(name = "电子票据外网H5页面URL", width = 30)
    @ApiModelProperty(value = "电子票据外网H5页面URL")
    private String pictureNetUrl;

    @Excel(name = "团检标志", width = 15)
    @ApiModelProperty(value = "团检标志：1-团检，0-个检")
    private Integer teamFlag;

    @Excel(name = "预开票标志", width = 15)
    @ApiModelProperty(value = "预开票标志：1-预开票，0-正常开票")
    private Integer preIssueFlag;

    @Excel(name = "团检单位名称", width = 20)
    @ApiModelProperty(value = "团检单位名称")
    private String companyName;

    @Excel(name = "患者身份证号", width = 20)
    @ApiModelProperty(value = "患者身份证号")
    private String cardNo;

    @Excel(name = "患者手机号", width = 15)
    @ApiModelProperty(value = "患者手机号")
    private String tel;

    @Excel(name = "邮箱", width = 20)
    @ApiModelProperty(value = "邮箱")
    private String email;

    @Excel(name = "科室代码", width = 15)
    @ApiModelProperty(value = "科室代码")
    private String deptCode;

    @Excel(name = "科室名称", width = 20)
    @ApiModelProperty(value = "科室名称")
    private String deptName;

    @Excel(name = "医生代码", width = 15)
    @ApiModelProperty(value = "医生代码")
    private String doctorCode;

    @Excel(name = "医生姓名", width = 15)
    @ApiModelProperty(value = "医生姓名")
    private String doctorName;

    // 内蒙特殊字段
    @Excel(name = "是否可流通", width = 15)
    @ApiModelProperty(value = "是否可流通")
    private String isArrears;

    @Excel(name = "不可流通原因", width = 30)
    @ApiModelProperty(value = "不可流通原因")
    private String arrearsReason;

    @Excel(name = "交费日期", width = 20)
    @ApiModelProperty(value = "交费日期")
    private String chargeDate;

    // 住院特殊字段
    @Excel(name = "住院号", width = 20)
    @ApiModelProperty(value = "住院号")
    private String inHospitalNo;

    @Excel(name = "入院日期", width = 20)
    @ApiModelProperty(value = "入院日期")
    private String inHospitalDate;

    @Excel(name = "出院日期", width = 20)
    @ApiModelProperty(value = "出院日期")
    private String outHospitalDate;

    @Excel(name = "住院天数", width = 15)
    @ApiModelProperty(value = "住院天数")
    private Integer inHospitalDays;

    @Excel(name = "病区", width = 15)
    @ApiModelProperty(value = "病区")
    private String wardCode;

    @Excel(name = "床位号", width = 15)
    @ApiModelProperty(value = "床位号")
    private String bedNo;

    @Excel(name = "同步状态", width = 15)
    @ApiModelProperty(value = "同步状态：0-未同步，1-已同步，2-同步失败")
    private Integer syncStatus;

    @Excel(name = "最后同步时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后同步时间")
    private Date lastSyncTime;

    @Excel(name = "错误消息", width = 30)
    @ApiModelProperty(value = "错误消息")
    private String errorMessage;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}