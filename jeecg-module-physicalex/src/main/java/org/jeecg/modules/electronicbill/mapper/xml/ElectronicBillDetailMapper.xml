<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.electronicbill.mapper.ElectronicBillDetailMapper">

    <!-- 通用查询结果映射 -->
    <resultMap id="BaseResultMap" type="org.jeecg.modules.electronicbill.entity.ElectronicBillDetail">
        <id column="id" property="id" />
        <result column="bill_id" property="billId" />
        <result column="pay_record_id" property="payRecordId" />
        <result column="item_group_id" property="itemGroupId" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="standard" property="standard" />
        <result column="unit" property="unit" />
        <result column="price" property="price" />
        <result column="amt" property="amt" />
        <result column="med_care_institution" property="medCareInstitution" />
        <result column="balanced_number" property="balancedNumber" />
        <result column="exec_dept_name" property="execDeptName" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询列 -->
    <sql id="Base_Column_List">
        id, bill_id, pay_record_id, item_group_id, code, name, standard, unit, price, amt, 
        med_care_institution, balanced_number, exec_dept_name, remark, create_time
    </sql>

    <!-- 根据票据ID查询明细 -->
    <select id="selectByBillId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_detail
        WHERE bill_id = #{billId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据票据ID列表查询明细 -->
    <select id="selectByBillIds" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_detail
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
        ORDER BY bill_id, create_time ASC
    </select>


    <!-- 根据项目组合ID查询明细 -->
    <select id="selectByItemGroupId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_detail
        WHERE item_group_id = #{itemGroupId}
        ORDER BY create_time ASC
    </select>

    <!-- 根据费用类别编码查询明细 -->
    <select id="selectByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_detail
        WHERE code = #{code}
        ORDER BY create_time ASC
    </select>

    <!-- 根据票据ID统计金额 -->
    <select id="sumAmtByBillId" parameterType="java.lang.String" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(amt), 0)
        FROM electronic_bill_detail
        WHERE bill_id = #{billId}
    </select>

    <!-- 根据票据ID统计明细数量 -->
    <select id="countByBillId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM electronic_bill_detail
        WHERE bill_id = #{billId}
    </select>

    <!-- 批量插入明细 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO electronic_bill_detail (
            id, bill_id, pay_record_id, item_group_id, code, name, standard, unit, price, amt,
            med_care_institution, balanced_number, exec_dept_name, remark, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.billId}, #{item.payRecordId}, #{item.itemGroupId}, #{item.code}, 
                #{item.name}, #{item.standard}, #{item.unit}, #{item.price}, #{item.amt},
                #{item.medCareInstitution}, #{item.balancedNumber}, #{item.execDeptName}, 
                #{item.remark}, #{item.createTime}
            )
        </foreach>
    </insert>

    <!-- 根据票据ID删除明细 -->
    <delete id="deleteByBillId" parameterType="java.lang.String">
        DELETE FROM electronic_bill_detail WHERE bill_id = #{billId}
    </delete>

    <!-- 根据票据ID列表删除明细 -->
    <delete id="deleteByBillIds" parameterType="java.util.List">
        DELETE FROM electronic_bill_detail 
        WHERE bill_id IN
        <foreach collection="billIds" item="billId" open="(" separator="," close=")">
            #{billId}
        </foreach>
    </delete>

    <!-- 根据支付记录ID删除明细 -->
    <delete id="deleteByPayRecordId" parameterType="java.lang.String">
        DELETE FROM electronic_bill_detail WHERE pay_record_id = #{payRecordId}
    </delete>

    <!-- 条件查询明细 -->
    <select id="selectByCondition" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM electronic_bill_detail
        <where>
            <if test="billId != null and billId != ''">
                AND bill_id = #{billId}
            </if>
            <if test="payRecordId != null and payRecordId != ''">
                AND pay_record_id = #{payRecordId}
            </if>
            <if test="itemGroupId != null and itemGroupId != ''">
                AND item_group_id = #{itemGroupId}
            </if>
            <if test="code != null and code != ''">
                AND code = #{code}
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="minAmt != null">
                AND amt >= #{minAmt}
            </if>
            <if test="maxAmt != null">
                AND amt &lt;= #{maxAmt}
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 统计各费用类别金额 -->
    <select id="sumAmtByCodeGroup" parameterType="map" resultType="map">
        SELECT 
            code,
            name,
            SUM(amt) as totalAmt,
            COUNT(*) as detailCount
        FROM electronic_bill_detail
        <where>
            <if test="billIds != null and billIds.size() > 0">
                AND bill_id IN
                <foreach collection="billIds" item="billId" open="(" separator="," close=")">
                    #{billId}
                </foreach>
            </if>
            <if test="startTime != null">
                AND create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
        GROUP BY code, name
        ORDER BY totalAmt DESC
    </select>

</mapper>