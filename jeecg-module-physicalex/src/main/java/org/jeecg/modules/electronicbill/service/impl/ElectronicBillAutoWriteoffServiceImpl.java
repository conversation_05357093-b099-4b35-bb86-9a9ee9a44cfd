package org.jeecg.modules.electronicbill.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.electronicbill.entity.ElectronicBill;
import org.jeecg.modules.electronicbill.mapper.ElectronicBillMapper;
import org.jeecg.modules.electronicbill.service.IElectronicBillAutoWriteoffService;
import org.jeecg.modules.electronicbill.service.IElectronicBillService;
import org.jeecg.modules.fee.entity.CustomerRegBill;
import org.jeecg.modules.fee.mapper.CustomerRegBillMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 电子票据自动冲红服务实现
 * 
 * @author: auto-generated
 * @date: 2025-01-24
 */
@Slf4j
@Service
public class ElectronicBillAutoWriteoffServiceImpl implements IElectronicBillAutoWriteoffService {

    @Resource
    private ElectronicBillMapper electronicBillMapper;
    
    @Resource
    private CustomerRegBillMapper customerRegBillMapper;
    
    @Resource
    private IElectronicBillService electronicBillService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoWriteoffAfterRefund(String billId, String refundReason, String operator) throws Exception {
        if (StringUtils.isBlank(billId)) {
            log.warn("自动冲红：支付单ID为空，跳过冲红处理");
            return;
        }

        try {
            // 检查是否需要自动冲红
            if (!needAutoWriteoff(billId)) {
                log.info("自动冲红：支付单[{}]不需要冲红，跳过处理", billId);
                return;
            }

            // 查询电子票据信息
            LambdaQueryWrapper<ElectronicBill> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ElectronicBill::getBillId, billId)
                       .eq(ElectronicBill::getBillStatus, "1") // 正常状态
                       .eq(ElectronicBill::getIsScarlet, "0"); // 未冲红
            
            ElectronicBill electronicBill = electronicBillMapper.selectOne(queryWrapper);
            if (Objects.isNull(electronicBill)) {
                log.info("自动冲红：支付单[{}]没有找到可冲红的电子票据", billId);
                return;
            }

            // 构建冲红原因
            String writeoffReason = String.format("退款自动冲红：%s", 
                                                  StringUtils.isNotBlank(refundReason) ? refundReason : "系统退款");
            
            log.info("自动冲红：开始处理支付单[{}]的电子票据冲红，票据号：{}", billId, electronicBill.getElectronicBillNo());
            
            // 调用冲红服务
            electronicBillService.writeOffElectronicBill(billId, writeoffReason, operator);
            
            log.info("自动冲红：支付单[{}]电子票据冲红成功", billId);
            
        } catch (Exception e) {
            log.error("自动冲红：支付单[{}]电子票据冲红失败：{}", billId, e.getMessage(), e);
            // 这里不抛出异常，避免影响退款主流程
            // 可以考虑记录到失败队列，后续重试
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class) 
    public void batchAutoWriteoffAfterRefund(List<String> billIds, String refundReason, String operator) throws Exception {
        if (billIds == null || billIds.isEmpty()) {
            log.warn("自动冲红：支付单ID列表为空，跳过批量冲红处理");
            return;
        }

        log.info("自动冲红：开始批量处理{}个支付单的电子票据冲红", billIds.size());
        
        int successCount = 0;
        int failCount = 0;
        
        for (String billId : billIds) {
            try {
                autoWriteoffAfterRefund(billId, refundReason, operator);
                successCount++;
            } catch (Exception e) {
                failCount++;
                log.error("自动冲红：支付单[{}]处理失败：{}", billId, e.getMessage(), e);
            }
        }
        
        log.info("自动冲红：批量处理完成，成功：{}，失败：{}", successCount, failCount);
    }

    @Override
    public boolean needAutoWriteoff(String billId) {
        if (StringUtils.isBlank(billId)) {
            return false;
        }

        try {
            // 1. 检查支付单是否存在且需要电子票据
            CustomerRegBill customerRegBill = customerRegBillMapper.selectById(billId);
            if (Objects.isNull(customerRegBill)) {
                log.debug("支付单[{}]不存在", billId);
                return false;
            }
            
            // 检查是否需要电子票据
            if (Objects.isNull(customerRegBill.getNeedElectronicBill()) || 
                customerRegBill.getNeedElectronicBill() != 1) {
                log.debug("支付单[{}]不需要电子票据", billId);
                return false;
            }

            // 2. 检查是否有已开具的电子票据
            LambdaQueryWrapper<ElectronicBill> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ElectronicBill::getBillId, billId)
                       .eq(ElectronicBill::getBillStatus, "1") // 正常状态
                       .eq(ElectronicBill::getIsScarlet, "0"); // 未冲红
            
            long count = electronicBillMapper.selectCount(queryWrapper);
            
            boolean needWriteoff = count > 0;
            log.debug("支付单[{}]{}需要自动冲红，已开具未冲红票据数量：{}", billId, needWriteoff ? "" : "不", count);
            
            return needWriteoff;
            
        } catch (Exception e) {
            log.error("检查支付单[{}]是否需要自动冲红时发生异常：{}", billId, e.getMessage(), e);
            return false;
        }
    }
}