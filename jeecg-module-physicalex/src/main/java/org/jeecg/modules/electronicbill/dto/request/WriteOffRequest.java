package org.jeecg.modules.electronicbill.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 电子票据冲红请求
 * 基于接口标识：writeOffEBill
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据冲红请求")
public class WriteOffRequest {

    @ApiModelProperty(value = "电子票据代码", required = true)
    @NotBlank(message = "电子票据代码不能为空")
    @JsonProperty("billBatchCode")
    private String billBatchCode;

    @ApiModelProperty(value = "电子票据号码", required = true)
    @NotBlank(message = "电子票据号码不能为空")
    @JsonProperty("billNo")
    private String billNo;

    @ApiModelProperty(value = "冲红原因", required = true)
    @NotBlank(message = "冲红原因不能为空")
    @JsonProperty("reason")
    private String reason;

    @ApiModelProperty(value = "经办人", required = true)
    @NotBlank(message = "经办人不能为空")
    @JsonProperty("operator")
    private String operator;

    @ApiModelProperty(value = "业务发生时间", required = true)
    @NotBlank(message = "业务发生时间不能为空")
    @JsonProperty("busDateTime")
    private String busDateTime; // 格式：yyyyMMddHHmmssSSS

    @ApiModelProperty(value = "开票点编码", required = true)
    @NotBlank(message = "开票点编码不能为空")
    @JsonProperty("placeCode")
    private String placeCode;
}