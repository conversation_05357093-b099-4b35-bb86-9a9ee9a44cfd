package org.jeecg.modules.electronicbill.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.electronicbill.entity.ElectronicBill;
import org.jeecg.modules.electronicbill.dto.response.*;
import org.jeecg.modules.fee.entity.CustomerRegBill;

/**
 * 电子票据业务服务接口
 * 
 * <AUTHOR>
 */
public interface IElectronicBillService extends IService<ElectronicBill> {

    /**
     * 为支付单申请电子票据
     * 根据支付单类型自动选择门诊或住院开票接口
     * 
     * @param customerRegBill 支付单
     * @return 开票结果
     */
    ElectronicBillResponse applyElectronicBill(CustomerRegBill customerRegBill);

    /**
     * 门诊电子票据开具
     * 
     * @param customerRegBill 支付单
     * @return 开票结果
     */
    ElectronicBillResponse createOutpatientBill(CustomerRegBill customerRegBill);

    /**
     * 住院电子票据开具
     * 
     * @param customerRegBill 支付单
     * @return 开票结果
     */
    ElectronicBillResponse createHospitalizedBill(CustomerRegBill customerRegBill);

    /**
     * 团检预开票据处理
     * 支持afterPayFlag=1的预开票场景
     * 
     * @param customerRegBill 支付单
     * @return 开票结果
     */
    ElectronicBillResponse createTeamPreIssueBill(CustomerRegBill customerRegBill);

    /**
     * 电子票据冲红
     * 
     * @param billId 支付单ID
     * @param reason 冲红原因
     * @param operator 操作人
     * @return 冲红结果
     */
    WriteOffResponse writeOffElectronicBill(String billId, String reason, String operator);

    /**
     * 查询电子票据状态
     * 
     * @param billId 支付单ID
     * @return 票据状态
     */
    BillStatusResponse queryBillStatus(String billId);

    /**
     * 根据票据代码和号码查询状态
     * 
     * @param billBatchCode 票据代码
     * @param billNo 票据号码
     * @return 票据状态
     */
    BillStatusResponse queryBillStatus(String billBatchCode, String billNo);

    /**
     * 同步票据状态
     * 定期同步电子票据平台的状态变更
     * 
     * @return 同步结果
     */
    int syncBillStatus();

    /**
     * 批量申请电子票据
     * 适用于团检批量开票场景
     * 
     * @param billIds 支付单ID列表
     * @return 批量处理结果
     */
    java.util.List<ElectronicBillResponse> batchApplyElectronicBill(java.util.List<String> billIds);

    /**
     * 重新申请电子票据
     * 用于处理失败的开票请求
     * 
     * @param billId 支付单ID
     * @return 开票结果
     */
    ElectronicBillResponse retryApplyElectronicBill(String billId);

    /**
     * 获取电子票据PDF下载地址
     * 
     * @param billId 支付单ID
     * @return PDF下载地址
     */
    String getBillPdfUrl(String billId);

    /**
     * 获取电子票据二维码
     * 
     * @param billId 支付单ID
     * @return 二维码BASE64数据
     */
    String getBillQrCode(String billId);

    /**
     * 根据开票日期获取开票信息
     * 
     * @param startDate 开始日期 (格式: yyyyMMddHHmmssSSS)
     * @param endDate 结束日期 (格式: yyyyMMddHHmmssSSS)  
     * @param placeCode 开票点编码（可选）
     * @param busType 业务类型（可选）
     * @param dataType 数据类型（可选）
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 开票信息分页列表
     */
    GetBillByDateResponse getBillsByDate(String startDate, String endDate, String placeCode, 
                                        String busType, String dataType, Integer pageNo, Integer pageSize);

    /**
     * 总笔数核对
     * 
     * @param startDate 起始日期
     * @param endDate 截止日期
     * @param busType 业务类型（可选）
     * @param placeCode 开票点编码（可选）
     * @return 核对结果
     */
    CheckTotalDataResponse checkTotalData(String startDate, String endDate, String busType, String placeCode);

    /**
     * 冲红数据核对
     * 
     * @param startDate 起始日期
     * @param endDate 截止日期
     * @param busType 业务类型（可选）
     * @param placeCode 开票点编码（可选）
     * @return 核对结果
     */
    CheckWriteOffDataResponse checkWriteOffData(String startDate, String endDate, String busType, String placeCode);

    /**
     * 根据业务日期获取开票信息
     * 
     * @param startDate 起始日期
     * @param endDate 截止日期
     * @param placeCode 开票点编码（可选）
     * @param busType 业务类型（可选）
     * @param dataType 数据类型（可选）
     * @param pageNo 页码
     * @param pageSize 每页条数
     * @return 开票信息分页列表
     */
    GetBillByBusDateResponse getBillsByBusDate(String startDate, String endDate, String placeCode, 
                                              String busType, String dataType, Integer pageNo, Integer pageSize);

    /**
     * 按开票日期总笔数核对
     * 
     * @param startDate 起始日期
     * @param endDate 截止日期
     * @param busType 业务类型（可选）
     * @param placeCode 开票点编码（可选）
     * @return 核对结果
     */
    CheckTotalDataResponse checkTotalDataByIvcDate(String startDate, String endDate, String busType, String placeCode);

    /**
     * 按开票日期冲红数据核对
     * 
     * @param startDate 起始日期
     * @param endDate 截止日期
     * @param busType 业务类型（可选）
     * @param placeCode 开票点编码（可选）
     * @return 核对结果
     */
    CheckWriteOffDataResponse checkWriteOffDataByIvcDate(String startDate, String endDate, String busType, String placeCode);
}