package org.jeecg.modules.electronicbill.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 根据业务日期获取开票信息请求
 * 服务标识：getBillByBusDate
 * 
 * <AUTHOR>
 */
@Data
public class GetBillByBusDateRequest {

    /**
     * 起始日期
     * 格式：yyyyMMdd
     */
    @JsonProperty("busBgnDate")
    @NotBlank(message = "起始日期不能为空")
    private String busBgnDate;

    /**
     * 截止日期
     * 格式：yyyyMMdd
     */
    @JsonProperty("busEndDate")
    @NotBlank(message = "截止日期不能为空")
    private String busEndDate;

    /**
     * 开票点编码
     */
    @JsonProperty("placeCode")
    private String placeCode;

    /**
     * 业务类型
     * 01住院、02门诊、03急诊、05门特、04体检中心
     */
    @JsonProperty("busType")
    private String busType;

    /**
     * 数据类型
     * 1正票、2红票、3正红票（可空，默认查询所有）
     */
    @JsonProperty("dataType")
    private String dataType;

    /**
     * 页码
     */
    @JsonProperty("pageNo")
    @NotNull(message = "页码不能为空")
    private Integer pageNo;

    /**
     * 每页数量（最大200）
     */
    @JsonProperty("pageSize")
    private Integer pageSize;
}