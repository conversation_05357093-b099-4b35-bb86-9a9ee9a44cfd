package org.jeecg.modules.electronicbill.dto.response;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 根据业务日期获取开票信息响应
 * 
 * <AUTHOR>
 */
@Data
public class GetBillByBusDateResponse extends BaseResponse {

    /**
     * 业务数据
     */
    private MessageContent message;

    @Data
    public static class MessageContent {
        /**
         * 总记录数
         */
        private Integer total;

        /**
         * 当前页码
         */
        private Integer pageNo;

        /**
         * 开票明细列表
         */
        private List<BillInfo> billList;
    }

    @Data
    public static class BillInfo {
        /**
         * 业务日期
         */
        private String busDate;

        /**
         * 业务流水号
         */
        private String busNo;

        /**
         * 业务类型
         */
        private String busType;

        /**
         * 开票点编码
         */
        private String placeCode;

        /**
         * 票据名称
         */
        private String billName;

        /**
         * 票据代码
         */
        private String billBatchCode;

        /**
         * 票据号码
         */
        private String billNo;

        /**
         * 校验码
         */
        private String random;

        /**
         * 票据金额
         */
        private BigDecimal totalAmt;

        /**
         * 开票日期时间
         */
        private String ivcDateTime;

        /**
         * 数据类型
         */
        private String dataType;

        /**
         * 票据状态
         */
        private String state;

        /**
         * 关联票据代码(红票关联原票)
         */
        private String relateBillBatchCode;

        /**
         * 关联票据号码(红票关联原票)
         */
        private String relateBillNo;
    }

    public static GetBillByBusDateResponse failure(String result, String errorMessage) {
        GetBillByBusDateResponse response = new GetBillByBusDateResponse();
        response.setResult(result);
        response.setErrorMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }
}