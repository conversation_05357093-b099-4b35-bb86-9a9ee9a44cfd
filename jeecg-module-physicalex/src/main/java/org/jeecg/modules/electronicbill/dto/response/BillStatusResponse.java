package org.jeecg.modules.electronicbill.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 电子票据状态查询响应
 * 
 * <AUTHOR>
 */
@Data
@ApiModel("电子票据状态查询响应")
public class BillStatusResponse {

    @ApiModelProperty(value = "返回结果标识")
    @JsonProperty("result")
    private String result; // S0000表示成功

    @ApiModelProperty(value = "返回结果内容")
    @JsonProperty("message")
    private String message; // BASE64编码的JSON格式业务参数

    // 解析后的业务参数
    @ApiModelProperty(value = "电子票据种类名称")
    private String billName;

    @ApiModelProperty(value = "电子票据代码")
    private String billBatchCode;

    @ApiModelProperty(value = "电子票据号码")
    private String billNo;

    @ApiModelProperty(value = "电子校验码")
    private String random;

    @ApiModelProperty(value = "开票时间")
    private String ivcDateTime; // 格式：yyyyMMddHHmmssSSS

    @ApiModelProperty(value = "状态")
    private String state; // 状态：1正常，2作废

    @ApiModelProperty(value = "是否打印纸质票据")
    private String isPrtPaper; // 0未打印，1已打印

    @ApiModelProperty(value = "纸质票据代码")
    private String pBillBatchCode;

    @ApiModelProperty(value = "纸质票据号码")
    private String pBillNo;

    @ApiModelProperty(value = "纸质票据生成时间")
    private String pCreateTime;

    @ApiModelProperty(value = "纸质票据业务发生时间")
    private String pBillBusDate;

    @ApiModelProperty(value = "是否已开红票")
    private String isScarlet; // 0未开红票，1已开红票

    @ApiModelProperty(value = "电子红票代码")
    private String scarletBillBatchCode;

    @ApiModelProperty(value = "电子红票号码")
    private String scarletBillNo;

    @ApiModelProperty(value = "电子红票随机码")
    private String scarletRandom;

    @ApiModelProperty(value = "电子红票二维码图片")
    private String scarletBillQRCode; // BASE64编码

    @ApiModelProperty(value = "电子红票生成时间")
    private String scarletCreateTime;

    @ApiModelProperty(value = "电子红票业务发生时间")
    private String scarletBillBusDate;

    // 扩展字段
    @ApiModelProperty(value = "错误代码")
    private String errorCode;

    @ApiModelProperty(value = "错误描述")
    private String errorMessage;

    @ApiModelProperty(value = "是否成功")
    private Boolean success;

    /**
     * 创建成功响应
     */
    public static BillStatusResponse success() {
        BillStatusResponse response = new BillStatusResponse();
        response.setResult("S0000");
        response.setSuccess(true);
        return response;
    }

    /**
     * 创建失败响应
     */
    public static BillStatusResponse failure(String errorCode, String errorMessage) {
        BillStatusResponse response = new BillStatusResponse();
        response.setResult(errorCode);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        response.setSuccess(false);
        return response;
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return "S0000".equals(result) || (success != null && success);
    }

    /**
     * 获取状态描述
     */
    public String getStateDesc() {
        if ("1".equals(state)) {
            return "正常";
        } else if ("2".equals(state)) {
            return "作废";
        }
        return "未知";
    }

    /**
     * 是否已打印纸质票据
     */
    public boolean isPaperPrinted() {
        return "1".equals(isPrtPaper);
    }

    /**
     * 是否已开红票
     */
    public boolean isScarletBill() {
        return "1".equals(isScarlet);
    }
}