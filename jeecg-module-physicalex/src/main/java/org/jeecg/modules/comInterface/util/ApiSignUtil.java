package org.jeecg.modules.comInterface.util;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * @Description: API签名工具类
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
public class ApiSignUtil {

    /**
     * 生成API签名
     * 签名算法：MD5(apiKey + apiSecret + timestamp + nonce)
     * 
     * @param apiKey API密钥
     * @param apiSecret API秘钥
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return 签名字符串
     */
    public static String generateSignature(String apiKey, String apiSecret, String timestamp, String nonce) {
        String signString = apiKey + apiSecret + timestamp + nonce;
        return DigestUtils.md5Hex(signString).toUpperCase();
    }

    /**
     * 验证API签名
     * 
     * @param apiKey API密钥
     * @param apiSecret API秘钥
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param signature 待验证的签名
     * @return 验证结果
     */
    public static boolean verifySignature(String apiKey, String apiSecret, String timestamp, String nonce, String signature) {
        String expectedSignature = generateSignature(apiKey, apiSecret, timestamp, nonce);
        return expectedSignature.equals(signature);
    }
}
