package org.jeecg.modules.comInterface.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 批量任务状态
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
@Data
@TableName("batch_task_status")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class BatchTaskStatus implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 关联业务ID（如企业预约ID）
     */
    private String businessId;

    /**
     * 任务状态：PENDING-等待中, PROCESSING-处理中, COMPLETED-已完成, FAILED-失败
     */
    private String status;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 成功数量
     */
    private Integer successCount;

    /**
     * 失败数量
     */
    private Integer failureCount;

    /**
     * 当前处理数量
     */
    private Integer processedCount;

    /**
     * 进度百分比
     */
    private Integer progressPercent;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 结果详情（JSON格式）
     */
    private String resultDetail;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 完成时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /**
     * 创建人
     */
    private String createBy;
}
