package org.jeecg.modules.comInterface.service;

import org.jeecg.modules.comInterface.dto.CompanyRegBatchCreateDTO;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CompanyTeam;
import org.jeecg.modules.reg.entity.CustomerReg;

/**
 * @Description: 企业预约处理服务接口
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
public interface ICompanyRegProcessService {

    /**
     * 根据ID获取CompanyTeam
     */
    CompanyTeam getCompanyTeamById(String teamId);

    /**
     * 处理单个人员信息
     */
    CustomerReg processPersonnelInfo(CompanyRegBatchCreateDTO.ExaminedPersonnelInfoDTO personnelInfo,
                                   CompanyReg companyReg,
                                   CompanyTeam companyTeam) throws Exception;

    /**
     * 记录失败信息
     */
    void recordFailure(CompanyRegBatchCreateDTO.ExaminedPersonnelInfoDTO personnelInfo,
                      CompanyReg companyReg,
                      CompanyTeam companyTeam,
                      String errorMessage);

    /**
     * 从人员信息创建CustomerReg（用于失败记录）
     */
    CustomerReg createCustomerRegFromPersonnel(CompanyRegBatchCreateDTO.ExaminedPersonnelInfoDTO personnelInfo,
                                             CompanyReg companyReg,
                                             CompanyTeam companyTeam);

    /**
     * 通知企业进度
     */
    void notifyEnterpriseProgress(String companyRegId, BatchResultVO<CustomerReg> result);
}
