package org.jeecg.modules.comInterface.util;

import org.apache.commons.codec.digest.DigestUtils;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

/**
 * @Description: API密钥生成工具类
 * @Author: system
 * @Date: 2024-07-31
 * @Version: V1.0
 */
public class ApiKeyGenerator {

    private static final String CHARS_ALPHANUMERIC = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final String CHARS_ALPHANUMERIC_SPECIAL = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    private static final String CHARS_HEX = "0123456789ABCDEF";
    
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    /**
     * 生成标准API密钥（32位字母数字）
     */
    public static String generateApiKey() {
        return generateRandomString(32, CHARS_ALPHANUMERIC);
    }

    /**
     * 生成标准API秘钥（64位字母数字）
     */
    public static String generateApiSecret() {
        return generateRandomString(64, CHARS_ALPHANUMERIC);
    }

    /**
     * 生成高强度API密钥（32位字母数字+特殊字符）
     */
    public static String generateSecureApiKey() {
        return generateRandomString(32, CHARS_ALPHANUMERIC_SPECIAL);
    }

    /**
     * 生成高强度API秘钥（64位字母数字+特殊字符）
     */
    public static String generateSecureApiSecret() {
        return generateRandomString(64, CHARS_ALPHANUMERIC_SPECIAL);
    }

    /**
     * 生成十六进制API密钥
     */
    public static String generateHexApiKey() {
        return generateRandomString(32, CHARS_HEX);
    }

    /**
     * 生成十六进制API秘钥
     */
    public static String generateHexApiSecret() {
        return generateRandomString(64, CHARS_HEX);
    }

    /**
     * 生成Base64编码的API密钥
     */
    public static String generateBase64ApiKey() {
        byte[] randomBytes = new byte[24]; // 24字节 = 32字符Base64
        SECURE_RANDOM.nextBytes(randomBytes);
        return Base64.getEncoder().encodeToString(randomBytes);
    }

    /**
     * 生成Base64编码的API秘钥
     */
    public static String generateBase64ApiSecret() {
        byte[] randomBytes = new byte[48]; // 48字节 = 64字符Base64
        SECURE_RANDOM.nextBytes(randomBytes);
        return Base64.getEncoder().encodeToString(randomBytes);
    }

    /**
     * 生成带时间戳的API密钥
     */
    public static String generateTimestampedApiKey() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomPart = generateRandomString(18, CHARS_ALPHANUMERIC);
        return timestamp + randomPart;
    }

    /**
     * 生成自定义长度的随机字符串
     */
    public static String generateCustomKey(int length, boolean includeSpecialChars) {
        String chars = includeSpecialChars ? CHARS_ALPHANUMERIC_SPECIAL : CHARS_ALPHANUMERIC;
        return generateRandomString(length, chars);
    }

    /**
     * 生成随机字符串
     */
    private static String generateRandomString(int length, String chars) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int index = SECURE_RANDOM.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }
        return sb.toString();
    }

    /**
     * 验证密钥格式
     */
    public static boolean isValidApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 16) {
            return false;
        }
        
        // 检查是否只包含允许的字符
        return apiKey.matches("[A-Za-z0-9!@#$%^&*]+");
    }

    /**
     * 计算密钥强度分数
     */
    public static int calculateKeyStrength(String key) {
        if (key == null || key.isEmpty()) {
            return 0;
        }
        
        int score = 0;
        
        // 长度分数
        if (key.length() >= 32) score += 2;
        else if (key.length() >= 16) score += 1;
        
        // 字符类型分数
        boolean hasUpper = key.matches(".*[A-Z].*");
        boolean hasLower = key.matches(".*[a-z].*");
        boolean hasDigit = key.matches(".*[0-9].*");
        boolean hasSpecial = key.matches(".*[!@#$%^&*].*");
        
        if (hasUpper) score++;
        if (hasLower) score++;
        if (hasDigit) score++;
        if (hasSpecial) score++;
        
        // 随机性检查（简单）
        if (!hasRepeatingPatterns(key)) score++;
        
        return Math.min(score, 10); // 最高10分
    }

    /**
     * 检查是否有重复模式
     */
    private static boolean hasRepeatingPatterns(String str) {
        // 检查连续重复字符
        for (int i = 0; i < str.length() - 2; i++) {
            if (str.charAt(i) == str.charAt(i + 1) && str.charAt(i + 1) == str.charAt(i + 2)) {
                return true;
            }
        }
        
        // 检查简单的重复模式
        if (str.length() >= 6) {
            for (int len = 2; len <= str.length() / 3; len++) {
                for (int i = 0; i <= str.length() - len * 3; i++) {
                    String pattern = str.substring(i, i + len);
                    if (str.substring(i + len, i + len * 2).equals(pattern) &&
                        str.substring(i + len * 2, i + len * 3).equals(pattern)) {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }

    /**
     * 生成密钥对（包含API Key和Secret）
     */
    public static class KeyPair {
        private final String apiKey;
        private final String apiSecret;
        private final int keyStrength;
        private final int secretStrength;
        private final String generationType;

        public KeyPair(String apiKey, String apiSecret, String generationType) {
            this.apiKey = apiKey;
            this.apiSecret = apiSecret;
            this.generationType = generationType;
            this.keyStrength = calculateKeyStrength(apiKey);
            this.secretStrength = calculateKeyStrength(apiSecret);
        }

        public String getApiKey() { return apiKey; }
        public String getApiSecret() { return apiSecret; }
        public int getKeyStrength() { return keyStrength; }
        public int getSecretStrength() { return secretStrength; }
        public String getGenerationType() { return generationType; }
        
        public int getOverallStrength() {
            return (keyStrength + secretStrength) / 2;
        }
    }

    /**
     * 生成标准密钥对
     */
    public static KeyPair generateStandardKeyPair() {
        return new KeyPair(generateApiKey(), generateApiSecret(), "STANDARD");
    }

    /**
     * 生成高强度密钥对
     */
    public static KeyPair generateSecureKeyPair() {
        return new KeyPair(generateSecureApiKey(), generateSecureApiSecret(), "SECURE");
    }

    /**
     * 生成十六进制密钥对
     */
    public static KeyPair generateHexKeyPair() {
        return new KeyPair(generateHexApiKey(), generateHexApiSecret(), "HEX");
    }

    /**
     * 生成Base64密钥对
     */
    public static KeyPair generateBase64KeyPair() {
        return new KeyPair(generateBase64ApiKey(), generateBase64ApiSecret(), "BASE64");
    }
}
