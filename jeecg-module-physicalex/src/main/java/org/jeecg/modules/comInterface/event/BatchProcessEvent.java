package org.jeecg.modules.comInterface.event;

import lombok.Getter;
import org.jeecg.modules.comInterface.dto.CompanyRegBatchCreateDTO;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.springframework.context.ApplicationEvent;

/**
 * @Description: 批量处理事件
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
@Getter
public class BatchProcessEvent extends ApplicationEvent {

    private final String taskId;
    private final CompanyRegBatchCreateDTO batchCreateDTO;
    private final CompanyReg companyReg;

    public BatchProcessEvent(Object source, String taskId, CompanyRegBatchCreateDTO batchCreateDTO, CompanyReg companyReg) {
        super(source);
        this.taskId = taskId;
        this.batchCreateDTO = batchCreateDTO;
        this.companyReg = companyReg;
    }
}
