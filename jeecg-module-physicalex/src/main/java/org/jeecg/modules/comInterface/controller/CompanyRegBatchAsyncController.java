package org.jeecg.modules.comInterface.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.comInterface.dto.CompanyRegBatchCreateDTO;
import org.jeecg.modules.comInterface.entity.BatchTaskStatus;
import org.jeecg.modules.comInterface.service.ICompanyRegApiService;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description: 企业预约批量处理异步API控制器
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
@Api(tags = "企业预约批量处理异步API")
@RestController
@RequestMapping("/api/v1/company-reg/batch")
@Slf4j
public class CompanyRegBatchAsyncController {

    @Autowired
    private ICompanyRegApiService companyRegApiService;

    /**
     * 异步批量创建企业预约数据
     */
    @ApiOperation(value = "异步批量创建企业预约数据", notes = "提交异步批量处理任务，返回任务ID")
    @PostMapping("/async")
    public Result<String> batchCreateCompanyRegAsync(
            @ApiParam(value = "批量创建DTO", required = true)
            @RequestBody CompanyRegBatchCreateDTO batchCreateDTO) {

        try {
            log.info("Received async batch create request for enterprise registration");

            String taskId = companyRegApiService.batchCreateCompanyRegAsync(batchCreateDTO);

            return Result.OK(taskId, "异步批量处理任务已提交，任务ID: " + taskId);

        } catch (Exception e) {
            log.error("Failed to submit async batch task", e);
            return Result.error("提交异步批量处理任务失败: " + e.getMessage());
        }
    }

    /**
     * 查询批量任务状态
     */
    @ApiOperation(value = "查询批量任务状态", notes = "根据任务ID查询批量处理任务的状态和进度")
    @GetMapping("/status/{taskId}")
    public Result<BatchTaskStatus> getBatchTaskStatus(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {

        try {
            BatchTaskStatus taskStatus = companyRegApiService.getBatchTaskStatus(taskId);

            if (taskStatus == null) {
                return Result.error("任务不存在: " + taskId);
            }

            return Result.OK(taskStatus);

        } catch (Exception e) {
            log.error("Failed to get batch task status for taskId: {}", taskId, e);
            return Result.error("查询任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取批量任务结果
     */
    @ApiOperation(value = "获取批量任务结果", notes = "根据任务ID获取批量处理任务的详细结果")
    @GetMapping("/result/{taskId}")
    public Result<BatchResultVO<CustomerReg>> getBatchTaskResult(
            @ApiParam(value = "任务ID", required = true)
            @PathVariable String taskId) {

        try {
            BatchResultVO<CustomerReg> result = companyRegApiService.getBatchTaskResult(taskId);

            if (result == null) {
                // 先检查任务是否存在
                BatchTaskStatus taskStatus = companyRegApiService.getBatchTaskStatus(taskId);
                if (taskStatus == null) {
                    return Result.error("任务不存在: " + taskId);
                } else if (!"COMPLETED".equals(taskStatus.getStatus())) {
                    return Result.error("任务尚未完成，当前状态: " + taskStatus.getStatus() +
                                      "，进度: " + taskStatus.getProgressPercent() + "%");
                } else {
                    return Result.error("无法获取任务结果");
                }
            }

            return Result.OK(result);

        } catch (Exception e) {
            log.error("Failed to get batch task result for taskId: {}", taskId, e);
            return Result.error("获取任务结果失败: " + e.getMessage());
        }
    }

    /**
     * 同步批量创建企业预约数据（保持向后兼容）
     */
    @ApiOperation(value = "同步批量创建企业预约数据", notes = "同步批量处理，直接返回处理结果")
    @PostMapping("/sync")
    public Result<BatchResultVO<CustomerReg>> batchCreateCompanyRegSync(
            @ApiParam(value = "批量创建DTO", required = true)
            @RequestBody CompanyRegBatchCreateDTO batchCreateDTO) {

        try {
            log.info("Received sync batch create request for enterprise registration");

            BatchResultVO<CustomerReg> result = companyRegApiService.batchCreateCompanyReg(batchCreateDTO);

            return Result.OK(result);

        } catch (Exception e) {
            log.error("Failed to process sync batch request", e);
            return Result.error("同步批量处理失败: " + e.getMessage());
        }
    }
}
