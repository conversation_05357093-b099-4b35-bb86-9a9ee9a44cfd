package org.jeecg.modules.comInterface.service;

import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.reg.entity.CustomerReg;

/**
 * @Description: 企业端通知服务接口
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
public interface IEnterpriseNotificationService {

    /**
     * 通知企业端批量处理进度
     *
     * @param companyRegId 企业预约ID
     * @param result 批量处理结果
     * @param taskId 任务ID（可选）
     */
    void notifyBatchProgress(String companyRegId, BatchResultVO<CustomerReg> result, String taskId);

    /**
     * 通知企业端异步任务开始
     *
     * @param companyRegId 企业预约ID
     * @param taskId 任务ID
     * @param totalCount 总数量
     */
    void notifyAsyncTaskStarted(String companyRegId, String taskId, int totalCount);

    /**
     * 通知企业端异步任务完成
     *
     * @param companyRegId 企业预约ID
     * @param taskId 任务ID
     * @param result 处理结果
     */
    void notifyAsyncTaskCompleted(String companyRegId, String taskId, BatchResultVO<CustomerReg> result);

    /**
     * 通知企业端异步任务失败
     *
     * @param companyRegId 企业预约ID
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     */
    void notifyAsyncTaskFailed(String companyRegId, String taskId, String errorMessage);
}
