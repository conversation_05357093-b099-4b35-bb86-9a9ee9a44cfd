package org.jeecg.modules.comInterface.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: API客户端配置
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Data
@TableName("api_client")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "api_client对象", description = "API客户端配置")
public class ApiClient implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 客户端名称
     */
    @ApiModelProperty(value = "客户端名称")
    private String clientName;

    /**
     * API密钥
     */
    @ApiModelProperty(value = "API密钥")
    private String apiKey;

    /**
     * API秘钥
     */
    @ApiModelProperty(value = "API秘钥")
    private String apiSecret;

    /**
     * 状态：1-启用，0-禁用
     */
    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    private Integer status;

    /**
     * 过期时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "过期时间")
    private Date expireTime;

    /**
     * IP白名单，多个IP用逗号分隔
     */
    @ApiModelProperty(value = "IP白名单")
    private String ipWhitelist;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 删除标志：0-正常，1-删除
     */
    @TableLogic
    @ApiModelProperty(value = "删除标志")
    private Integer delFlag;
}
