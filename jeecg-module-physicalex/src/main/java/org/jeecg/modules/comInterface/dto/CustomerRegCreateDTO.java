package org.jeecg.modules.comInterface.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 客户登记创建DTO
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CustomerRegCreateDTO", description = "客户登记创建DTO")
public class CustomerRegCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty(value = "姓名", required = true)
    private String name;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空")
    @ApiModelProperty(value = "性别", required = true)
    private String gender;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空")
    @ApiModelProperty(value = "身份证号", required = true)
    private String idCard;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private String cardType;

    /**
     * 出生日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer age;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @ApiModelProperty(value = "手机号", required = true)
    private String phone;

    /**
     * 分组名称
     */
    @ApiModelProperty(value = "分组名称")
    private String teamName;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    private String marriageStatus;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private String companyDeptId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String companyDeptName;

    /**
     * 危险因素
     */
    @ApiModelProperty(value = "危险因素")
    private String riskFactor;

    /**
     * 岗位状态
     */
    @ApiModelProperty(value = "岗位状态")
    private String jobStatus;

    /**
     * 预约日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "预约日期")
    private Date appointmentDate;

    /**
     * 总工龄（年）
     */
    @ApiModelProperty(value = "总工龄（年）")
    private String workYears;

    /**
     * 总工龄（月）
     */
    @ApiModelProperty(value = "总工龄（月）")
    private String workMonths;

    /**
     * 接害工龄（年）
     */
    @ApiModelProperty(value = "接害工龄（年）")
    private String riskYears;

    /**
     * 接害工龄（月）
     */
    @ApiModelProperty(value = "接害工龄（月）")
    private String riskMonths;

    /**
     * 工种
     */
    @ApiModelProperty(value = "工种")
    private String workType;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workShop;
}
