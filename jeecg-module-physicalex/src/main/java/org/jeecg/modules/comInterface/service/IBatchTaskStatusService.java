package org.jeecg.modules.comInterface.service;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.comInterface.entity.BatchTaskStatus;

/**
 * @Description: 批量任务状态服务接口
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
public interface IBatchTaskStatusService extends IService<BatchTaskStatus> {

    /**
     * 创建任务
     */
    BatchTaskStatus createTask(String taskType, String businessId, Integer totalCount);

    /**
     * 更新任务进度
     */
    void updateProgress(String taskId, Integer processedCount, Integer successCount, Integer failureCount);

    /**
     * 完成任务
     */
    void completeTask(String taskId, String resultDetail);

    /**
     * 失败任务
     */
    void failTask(String taskId, String errorMessage);

    /**
     * 获取任务状态
     */
    BatchTaskStatus getTaskStatus(String taskId);
}

