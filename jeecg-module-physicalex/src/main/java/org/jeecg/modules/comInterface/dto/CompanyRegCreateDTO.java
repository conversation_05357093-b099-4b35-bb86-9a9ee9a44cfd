package org.jeecg.modules.comInterface.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 企业预约创建DTO
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CompanyRegCreateDTO", description = "企业预约创建DTO")
public class CompanyRegCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 所在单位ID
     */
    @NotBlank(message = "所在单位ID不能为空")
    @ApiModelProperty(value = "所在单位ID", required = true)
    private String companyId;

    /**
     * 所在单位名称
     */
    @NotBlank(message = "所在单位名称不能为空")
    @ApiModelProperty(value = "所在单位名称", required = true)
    private String companyName;

    /**
     * 预约名称
     */
    @NotBlank(message = "预约名称不能为空")
    @ApiModelProperty(value = "预约名称", required = true)
    private String regName;

    /**
     * 助记码
     */
    @ApiModelProperty(value = "助记码")
    private String helpChar;

    /**
     * 体检类别
     */
    @NotBlank(message = "体检类别不能为空")
    @ApiModelProperty(value = "体检类别", required = true)
    private String examType;

    /**
     * 人员类别
     */
    @ApiModelProperty(value = "人员类别")
    private String personCategory;

    /**
     * 单位负责人
     */
    @ApiModelProperty(value = "单位负责人")
    private String linkMan;

    /**
     * 开始日期
     */
    @NotNull(message = "开始日期不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始日期", required = true)
    private Date startCheckDate;

    /**
     * 结束日期
     */
    @NotNull(message = "结束日期不能为空")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束日期", required = true)
    private Date endCheckDate;

    /**
     * 预约人数
     */
    @NotNull(message = "预约人数不能为空")
    @ApiModelProperty(value = "预约人数", required = true)
    private Integer personCount;

    /**
     * 客服专员
     */
    @ApiModelProperty(value = "客服专员")
    private String serviceManager;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 分组列表
     */
    @ApiModelProperty(value = "分组列表")
    private List<CompanyTeamCreateDTO> teams;
}
