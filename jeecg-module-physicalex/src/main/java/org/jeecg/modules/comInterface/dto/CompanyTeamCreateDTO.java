package org.jeecg.modules.comInterface.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description: 企业分组创建DTO
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CompanyTeamCreateDTO", description = "企业分组创建DTO")
public class CompanyTeamCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分组编码
     */
    @NotBlank(message = "分组编码不能为空")
    @ApiModelProperty(value = "分组编码", required = true)
    private String teamNum;

    /**
     * 分组名称
     */
    @NotBlank(message = "分组名称不能为空")
    @ApiModelProperty(value = "分组名称", required = true)
    private String name;

    /**
     * 助记码
     */
    @ApiModelProperty(value = "助记码")
    private String helpChar;

    /**
     * 体检分类
     */
    @NotBlank(message = "体检分类不能为空")
    @ApiModelProperty(value = "体检分类", required = true)
    private String examCategory;

    /**
     * 岗位类别
     */
    @ApiModelProperty(value = "岗位类别")
    private String post;

    /**
     * 性别限制
     */
    @ApiModelProperty(value = "性别限制")
    private String sexLimit;

    /**
     * 最小年龄
     */
    @ApiModelProperty(value = "最小年龄")
    private Integer minAge;

    /**
     * 最大年龄
     */
    @ApiModelProperty(value = "最大年龄")
    private Integer maxAge;

    /**
     * 婚姻状况
     */
    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;

    /**
     * 危险因素
     */
    @ApiModelProperty(value = "危险因素")
    private String risks;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    /**
     * 折扣
     */
    @ApiModelProperty(value = "折扣")
    private BigDecimal discount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
