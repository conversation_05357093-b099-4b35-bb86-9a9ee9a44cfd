package org.jeecg.modules.comInterface.service;

import org.jeecg.modules.comInterface.dto.CompanyRegBatchCreateDTO;
import org.jeecg.modules.comInterface.dto.CompanyRegCreateDTO;
import org.jeecg.modules.comInterface.dto.CustomerRegBatchCreateDTO;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.comInterface.entity.BatchTaskStatus;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CustomerReg;

/**
 * @Description: 企业预约API服务接口
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
public interface ICompanyRegApiService {

    /**
     * 创建企业预约（包含分组）
     *
     * @param createDTO 创建DTO
     * @return 企业预约信息
     */
    CompanyReg createCompanyReg(CompanyRegCreateDTO createDTO);

    /**
     * 企业端批量添加单位预约数据（同步版本，保持向后兼容）
     *
     * @param batchCreateDTO 企业端批量数据DTO
     * @return 批量操作结果
     */
    BatchResultVO<CustomerReg> batchCreateCompanyReg(CompanyRegBatchCreateDTO batchCreateDTO);

    /**
     * 企业端批量添加单位预约数据（异步版本）
     *
     * @param batchCreateDTO 企业端批量数据DTO
     * @return 任务ID，可用于查询任务状态和结果
     */
    String batchCreateCompanyRegAsync(CompanyRegBatchCreateDTO batchCreateDTO);

    /**
     * 查询批量任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    BatchTaskStatus getBatchTaskStatus(String taskId);

    /**
     * 获取批量任务结果
     *
     * @param taskId 任务ID
     * @return 批量操作结果，如果任务未完成返回null
     */
    BatchResultVO<CustomerReg> getBatchTaskResult(String taskId);

    /**
     * 批量创建客户登记
     *
     * @param batchCreateDTO 批量创建DTO
     * @return 批量操作结果
     */
    BatchResultVO<CustomerReg> batchCreateCustomerReg(CustomerRegBatchCreateDTO batchCreateDTO);

    /**
     * 根据ID获取企业预约信息
     *
     * @param companyRegId 企业预约ID
     * @return 企业预约信息
     */
    CompanyReg getCompanyRegById(String companyRegId);

    /**
     * 根据企业预约ID获取客户登记列表
     *
     * @param companyRegId 企业预约ID
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 客户登记列表
     */
    BatchResultVO<CustomerReg> getCustomerRegList(String companyRegId, Integer pageNo, Integer pageSize);
}
