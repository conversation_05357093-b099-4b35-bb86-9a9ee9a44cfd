package org.jeecg.modules.comInterface.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.comInterface.entity.BatchTaskStatus;
import org.jeecg.modules.comInterface.mapper.BatchTaskStatusMapper;
import org.jeecg.modules.comInterface.service.IBatchTaskStatusService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Description: 批量任务状态服务实现
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
@Slf4j
@Service
public class BatchTaskStatusServiceImpl extends ServiceImpl<BatchTaskStatusMapper, BatchTaskStatus> implements IBatchTaskStatusService {

    @Override
    public BatchTaskStatus createTask(String taskType, String businessId, Integer totalCount) {
        BatchTaskStatus task = new BatchTaskStatus();
        task.setTaskType(taskType);
        task.setBusinessId(businessId);
        task.setStatus("PENDING");
        task.setTotalCount(totalCount);
        task.setSuccessCount(0);
        task.setFailureCount(0);
        task.setProcessedCount(0);
        task.setProgressPercent(0);
        task.setCreateTime(new Date());
        task.setUpdateTime(new Date());

        save(task);
        log.info("Created batch task: {}", task.getId());
        return task;
    }

    @Override
    public void updateProgress(String taskId, Integer processedCount, Integer successCount, Integer failureCount) {
        BatchTaskStatus task = getById(taskId);
        if (task != null) {
            task.setProcessedCount(processedCount);
            task.setSuccessCount(successCount);
            task.setFailureCount(failureCount);
            task.setStatus("PROCESSING");

            // 计算进度百分比
            if (task.getTotalCount() > 0) {
                task.setProgressPercent((processedCount * 100) / task.getTotalCount());
            }

            task.setUpdateTime(new Date());
            updateById(task);
        }
    }

    @Override
    public void completeTask(String taskId, String resultDetail) {
        BatchTaskStatus task = getById(taskId);
        if (task != null) {
            task.setStatus("COMPLETED");
            task.setProgressPercent(100);
            task.setResultDetail(resultDetail);
            task.setCompleteTime(new Date());
            task.setUpdateTime(new Date());
            updateById(task);
            log.info("Completed batch task: {}", taskId);
        }
    }

    @Override
    public void failTask(String taskId, String errorMessage) {
        BatchTaskStatus task = getById(taskId);
        if (task != null) {
            task.setStatus("FAILED");
            task.setErrorMessage(errorMessage);
            task.setCompleteTime(new Date());
            task.setUpdateTime(new Date());
            updateById(task);
            log.error("Failed batch task: {}, error: {}", taskId, errorMessage);
        }
    }

    @Override
    public BatchTaskStatus getTaskStatus(String taskId) {
        return getById(taskId);
    }
}
