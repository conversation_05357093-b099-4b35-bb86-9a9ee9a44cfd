package org.jeecg.modules.comInterface.interceptor;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.comInterface.entity.ApiClient;
import org.jeecg.modules.comInterface.service.IApiClientService;
import org.jeecg.modules.comInterface.vo.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * @Description: API鉴权拦截器
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Slf4j
@Component
public class ApiAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private IApiClientService apiClientService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取请求头中的鉴权信息
        String apiKey = request.getHeader("X-API-Key");
        String secret = request.getHeader("X-Secret");

        // 检查必要参数
        if (StringUtils.isAnyBlank(apiKey, secret)) {
            log.warn("API authentication failed: missing required headers");
            writeErrorResponse(response, ApiResponse.badRequest("Missing required authentication headers"));
            return false;
        }

        // 查询API客户端配置
        ApiClient apiClient = apiClientService.getByApiKey(apiKey);
        if (apiClient == null) {
            log.warn("API authentication failed: invalid apiKey: {}", apiKey);
            writeErrorResponse(response, ApiResponse.unauthorized("Invalid API key"));
            return false;
        }

        // 直接比较secret
        if (!secret.equals(apiClient.getApiSecret())) {
            log.warn("API authentication failed: invalid secret, apiKey: {}", apiKey);
            writeErrorResponse(response, ApiResponse.unauthorized("Invalid secret"));
            return false;
        }

        // 将API客户端信息存储到请求属性中，供后续使用
        request.setAttribute("apiClient", apiClient);
        
        log.info("API authentication success, apiKey: {}, clientName: {}", apiKey, apiClient.getClientName());
        return true;
    }


    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, ApiResponse<?> apiResponse) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(apiResponse.getCode());
        
        try (PrintWriter writer = response.getWriter()) {
            writer.write(JSON.toJSONString(apiResponse));
            writer.flush();
        }
    }
}
