package org.jeecg.modules.comInterface.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.comInterface.service.IEnterpriseNotificationService;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 企业端通知服务实现
 * @Author: system
 * @Date: 2024-12-08
 * @Version: V1.0
 */
@Slf4j
@Service
public class EnterpriseNotificationServiceImpl implements IEnterpriseNotificationService {

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    // 企业端通知接口配置，可通过配置文件配置
    @Value("${enterprise.notification.base-url:}")
    private String enterpriseBaseUrl;

    @Value("${enterprise.notification.enabled:true}")
    private boolean notificationEnabled;

    @Value("${enterprise.notification.timeout:30000}")
    private int notificationTimeout;

    @Override
    public void notifyBatchProgress(String companyRegId, BatchResultVO<CustomerReg> result, String taskId) {
        if (!notificationEnabled) {
            log.debug("Enterprise notification is disabled");
            return;
        }

        try {
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("companyRegId", companyRegId);
            notificationData.put("taskId", taskId);
            notificationData.put("type", "BATCH_PROGRESS");
            notificationData.put("total", result.getTotal());
            notificationData.put("successCount", result.getSuccessCount());
            notificationData.put("failureCount", result.getFailureCount());
            notificationData.put("isAsync", result.getIsAsync());
            notificationData.put("message", result.getMessage());
            notificationData.put("timestamp", System.currentTimeMillis());

            sendNotification("/api/notification/batch-progress", notificationData);

        } catch (Exception e) {
            log.error("Failed to notify enterprise about batch progress for companyRegId: {}", companyRegId, e);
        }
    }

    @Override
    public void notifyAsyncTaskStarted(String companyRegId, String taskId, int totalCount) {
        if (!notificationEnabled) {
            log.debug("Enterprise notification is disabled");
            return;
        }

        try {
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("companyRegId", companyRegId);
            notificationData.put("taskId", taskId);
            notificationData.put("type", "ASYNC_TASK_STARTED");
            notificationData.put("totalCount", totalCount);
            notificationData.put("status", "STARTED");
            notificationData.put("timestamp", System.currentTimeMillis());

            sendNotification("/api/notification/async-task-started", notificationData);

        } catch (Exception e) {
            log.error("Failed to notify enterprise about async task start for companyRegId: {}, taskId: {}",
                     companyRegId, taskId, e);
        }
    }

    @Override
    public void notifyAsyncTaskCompleted(String companyRegId, String taskId, BatchResultVO<CustomerReg> result) {
        if (!notificationEnabled) {
            log.debug("Enterprise notification is disabled");
            return;
        }

        try {
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("companyRegId", companyRegId);
            notificationData.put("taskId", taskId);
            notificationData.put("type", "ASYNC_TASK_COMPLETED");
            notificationData.put("status", "COMPLETED");
            notificationData.put("total", result.getTotal());
            notificationData.put("successCount", result.getSuccessCount());
            notificationData.put("failureCount", result.getFailureCount());
            notificationData.put("timestamp", System.currentTimeMillis());

            // 如果有失败数据，包含失败详情
            if (result.getFailureCount() > 0 && result.getFailureList() != null) {
                notificationData.put("failureDetails", result.getFailureList());
            }

            sendNotification("/api/notification/async-task-completed", notificationData);

        } catch (Exception e) {
            log.error("Failed to notify enterprise about async task completion for companyRegId: {}, taskId: {}",
                     companyRegId, taskId, e);
        }
    }

    @Override
    public void notifyAsyncTaskFailed(String companyRegId, String taskId, String errorMessage) {
        if (!notificationEnabled) {
            log.debug("Enterprise notification is disabled");
            return;
        }

        try {
            Map<String, Object> notificationData = new HashMap<>();
            notificationData.put("companyRegId", companyRegId);
            notificationData.put("taskId", taskId);
            notificationData.put("type", "ASYNC_TASK_FAILED");
            notificationData.put("status", "FAILED");
            notificationData.put("errorMessage", errorMessage);
            notificationData.put("timestamp", System.currentTimeMillis());

            sendNotification("/api/notification/async-task-failed", notificationData);

        } catch (Exception e) {
            log.error("Failed to notify enterprise about async task failure for companyRegId: {}, taskId: {}",
                     companyRegId, taskId, e);
        }
    }

    /**
     * 发送通知到企业端
     */
    private void sendNotification(String endpoint, Map<String, Object> data) {
        if (enterpriseBaseUrl == null || enterpriseBaseUrl.trim().isEmpty()) {
            log.warn("Enterprise base URL is not configured, skipping notification");
            return;
        }

        try {
            String url = enterpriseBaseUrl.endsWith("/") ?
                        enterpriseBaseUrl + endpoint.substring(1) :
                        enterpriseBaseUrl + endpoint;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("User-Agent", "PhysicalEx-System/1.0");
            // 可以添加认证头信息
            // headers.add("Authorization", "Bearer " + token);

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(data, headers);

            log.info("Sending notification to enterprise: {}", url);
            log.debug("Notification data: {}", objectMapper.writeValueAsString(data));

            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("Successfully sent notification to enterprise. Response: {}", response.getBody());
            } else {
                log.warn("Enterprise notification returned non-success status: {} - {}",
                        response.getStatusCode(), response.getBody());
            }

        } catch (Exception e) {
            log.error("Failed to send notification to enterprise endpoint: {}", endpoint, e);
            // 不抛出异常，避免影响主业务流程
        }
    }
}
