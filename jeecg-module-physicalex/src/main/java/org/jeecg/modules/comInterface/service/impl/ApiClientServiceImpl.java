package org.jeecg.modules.comInterface.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.comInterface.entity.ApiClient;
import org.jeecg.modules.comInterface.mapper.ApiClientMapper;
import org.jeecg.modules.comInterface.service.IApiClientService;
import org.jeecg.modules.comInterface.util.ApiKeyGenerator;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.UUID;

/**
 * @Description: API客户端配置Service实现
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Slf4j
@Service
public class ApiClientServiceImpl extends ServiceImpl<ApiClientMapper, ApiClient> implements IApiClientService {

    private static final String CACHE_NAME = "apiClient";
    private static final String CACHE_KEY_PREFIX = "apiKey:";

    @Override
    @Cacheable(value = CACHE_NAME, key = "#apiKey", unless = "#result == null")
    public ApiClient getByApiKey(String apiKey) {
        log.debug("从数据库查询API客户端配置，apiKey: {}", apiKey);
        LambdaQueryWrapper<ApiClient> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiClient::getApiKey, apiKey);
        queryWrapper.eq(ApiClient::getDelFlag, 0);
        ApiClient result = this.getOne(queryWrapper);
        if (result != null) {
            log.debug("查询到API客户端配置，clientName: {}", result.getClientName());
        } else {
            log.debug("未找到API客户端配置，apiKey: {}", apiKey);
        }
        return result;
    }

    @Override
    @CacheEvict(value = CACHE_NAME, key = "#entity.apiKey", condition = "#entity.apiKey != null")
    public boolean save(ApiClient entity) {
        log.debug("保存API客户端配置并清除缓存，apiKey: {}", entity.getApiKey());
        return super.save(entity);
    }

    @Override
    @CacheEvict(value = CACHE_NAME, key = "#entity.apiKey", condition = "#entity.apiKey != null")
    public boolean updateById(ApiClient entity) {
        log.debug("更新API客户端配置并清除缓存，apiKey: {}", entity.getApiKey());
        return super.updateById(entity);
    }

    @Override
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public boolean removeById(java.io.Serializable id) {
        log.debug("删除API客户端配置并清除所有缓存，id: {}", id);
        return super.removeById(id);
    }

    /**
     * 手动清除指定apiKey的缓存
     */
    @CacheEvict(value = CACHE_NAME, key = "#apiKey")
    public void evictCache(String apiKey) {
        log.debug("手动清除API客户端缓存，apiKey: {}", apiKey);
    }

    /**
     * 清除所有API客户端缓存
     */
    @CacheEvict(value = CACHE_NAME, allEntries = true)
    public void evictAllCache() {
        log.debug("清除所有API客户端缓存");
    }

    @Override
    public ApiClient generateApiKeys() {
        ApiClient apiClient = new ApiClient();

        // 生成API密钥（32位UUID去掉横线）
        String apiKey = UUID.randomUUID().toString().replace("-", "");
        apiClient.setApiKey(apiKey);

        // 生成API秘钥（64位UUID去掉横线）
        String apiSecret = UUID.randomUUID().toString().replace("-", "") +
                          UUID.randomUUID().toString().replace("-", "");
        apiClient.setApiSecret(apiSecret);

        // 默认启用状态
        apiClient.setStatus(1);
        apiClient.setDelFlag(0);

        return apiClient;
    }

    @Override
    public ApiClient generateSecureApiKeys() {
        ApiClient apiClient = new ApiClient();

        // 使用ApiKeyGenerator生成高强度密钥
        ApiKeyGenerator.KeyPair keyPair = ApiKeyGenerator.generateSecureKeyPair();

        apiClient.setApiKey(keyPair.getApiKey());
        apiClient.setApiSecret(keyPair.getApiSecret());

        // 默认启用状态
        apiClient.setStatus(1);
        apiClient.setDelFlag(0);

        return apiClient;
    }

    @Override
    public int validateKeyStrength(String apiKey, String apiSecret) {
        if (apiKey == null || apiSecret == null) {
            return 1; // 弱
        }

        // 使用ApiKeyGenerator计算强度
        int keyStrength = ApiKeyGenerator.calculateKeyStrength(apiKey);
        int secretStrength = ApiKeyGenerator.calculateKeyStrength(apiSecret);

        // 计算平均强度并转换为1-3等级
        int avgStrength = (keyStrength + secretStrength) / 2;

        if (avgStrength >= 7) return 3; // 强
        if (avgStrength >= 4) return 2; // 中
        return 1; // 弱
    }


}
