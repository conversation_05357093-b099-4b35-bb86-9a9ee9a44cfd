package org.jeecg.modules.comInterface.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * @Description: 客户登记批量创建DTO
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Data
@ApiModel(value = "CustomerRegBatchCreateDTO", description = "客户登记批量创建DTO")
public class CustomerRegBatchCreateDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 企业预约ID
     */
    @NotBlank(message = "企业预约ID不能为空")
    @ApiModelProperty(value = "企业预约ID", required = true)
    private String companyRegId;

    /**
     * 客户登记列表
     */
    @NotEmpty(message = "客户登记列表不能为空")
    @Valid
    @ApiModelProperty(value = "客户登记列表", required = true)
    private List<CustomerRegCreateDTO> customerList;
}
