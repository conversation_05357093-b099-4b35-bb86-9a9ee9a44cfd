package org.jeecg.modules.calcite.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.calcite.service.CalcitePositiveCaseStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 使用Apache Calcite优化的阳性病例统计API接口
 * 支持大数据量查询和分页处理
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Api(tags = "阳性病例统计-Calcite优化版")
@RestController
@RequestMapping("/api/calcite/positiveResult")
@Slf4j
public class CalcitePositiveCaseController {
    
    @Autowired
    private CalcitePositiveCaseStatService calcitePositiveCaseStatService;
    
    /**
     * 分页查询阳性病例统计数据
     */
    @ApiOperation(value = "分页查询阳性病例", notes = "使用Calcite优化的阳性病例分页查询，支持大数据量处理")
    @GetMapping("/list")
    public Map<String, Object> getPositiveCaseList(@ApiParam(value = "开始日期") @RequestParam(required = false) String startTime, @ApiParam(value = "结束日期", example = "2024-01-31") @RequestParam(required = false) String endTime, @ApiParam(value = "页码", example = "1")@RequestParam(defaultValue = "1") Integer pageNo, @ApiParam(value = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer pageSize,@ApiParam(value = "组合项目名称") @RequestParam(required = false) String itemGroupName,@ApiParam(value = "体检号") @RequestParam(required = false) String examNo,@ApiParam(value = "姓名") @RequestParam(required = false) String name) {
        
        try {
            // 参数验证
            if (pageNo < 1) pageNo = 1;
            if (pageSize < 1 || pageSize > 1000) pageSize = 20;
            
            // 参数验证和默认值设置
            String[] dateRange = validateAndSetDefaultDates(startTime, endTime);
            startTime = dateRange[0];
            endTime = dateRange[1];
            
            log.info("分页查询阳性病例数据，时间范围：{} - {}，页码：{}，每页：{}，组合项目：{}，体检号：{}，姓名：{}",
                startTime, endTime, pageNo, pageSize, itemGroupName, examNo, name);

            // 执行Calcite优化查询
            Map<String, Object> pageResult = calcitePositiveCaseStatService.getPositiveCaseStatistics(
                startTime, endTime, pageNo, pageSize, itemGroupName, examNo, name);
            
            return pageResult;
            
        } catch (Exception e) {
            log.error("分页查询阳性病例数据失败", e);
            return null;
        }
    }
    
    /**
     * 获取阳性病例汇总统计
     */
    @ApiOperation(value = "获取阳性病例汇总统计", notes = "获取指定时间范围内的阳性病例汇总统计数据")
    @GetMapping("/summary")
    public Result<Map<String, Object>> getPositiveCaseSummary(@ApiParam(value = "开始日期") @RequestParam(required = false) String startTime, @ApiParam(value = "结束日期") @RequestParam(required = false) String endTime) {
        
        try {
            // 参数验证和默认值设置
            String[] dateRange = validateAndSetDefaultDates(startTime, endTime);
            startTime = dateRange[0];
            endTime = dateRange[1];
            
            log.info("查询阳性病例汇总统计，时间范围：{} - {}", startTime, endTime);
            
            // 执行汇总查询
            Map<String, Object> summary = calcitePositiveCaseStatService.getPositiveCaseSummary(startTime, endTime);
            
            return Result.OK(summary);
            
        } catch (Exception e) {
            log.error("查询阳性病例汇总统计失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取阳性病例项目统计
     */
    @ApiOperation(value = "获取阳性病例项目统计", notes = "获取各检查项目的阳性病例统计数据")
    @GetMapping("/item-statistics")
    public Result<List<Map<String, Object>>> getPositiveCaseItemStatistics(
            @ApiParam(value = "开始日期", example = "2024-01-01") 
            @RequestParam(required = false) String startTime,
            
            @ApiParam(value = "结束日期", example = "2024-01-31") 
            @RequestParam(required = false) String endTime) {
        
        try {
            // 参数验证和默认值设置
            String[] dateRange = validateAndSetDefaultDates(startTime, endTime);
            startTime = dateRange[0];
            endTime = dateRange[1];
            
            log.info("查询阳性病例项目统计，时间范围：{} - {}", startTime, endTime);
            
            // 执行项目统计查询
            List<Map<String, Object>> itemStats = calcitePositiveCaseStatService.getPositiveCaseItemStatistics(startTime, endTime);
            
            return Result.OK(itemStats);
            
        } catch (Exception e) {
            log.error("查询阳性病例项目统计失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当月阳性病例统计
     */
    @ApiOperation(value = "获取当月阳性病例统计", notes = "获取当前月份的阳性病例统计数据")
    @GetMapping("/current-month")
    public Result<Map<String, Object>> getCurrentMonthStatistics(
            @ApiParam(value = "页码", example = "1") 
            @RequestParam(defaultValue = "1") Integer pageNo,
            
            @ApiParam(value = "每页大小", example = "50") 
            @RequestParam(defaultValue = "50") Integer pageSize) {
        
        try {
            LocalDate now = LocalDate.now();
            LocalDate startOfMonth = now.withDayOfMonth(1);
            LocalDate endOfMonth = now.withDayOfMonth(now.lengthOfMonth());
            
            String startTime = startOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String endTime = endOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            
            log.info("查询当月阳性病例统计：{} - {}，页码：{}，每页：{}", 
                startTime, endTime, pageNo, pageSize);
            
            // 获取分页数据
            Map<String, Object> pageResult = calcitePositiveCaseStatService.getPositiveCaseStatistics(
                startTime, endTime, pageNo, pageSize, null, null, null);
            
            // 获取汇总数据
            Map<String, Object> summary = calcitePositiveCaseStatService.getPositiveCaseSummary(startTime, endTime);
            
            // 获取项目统计
            List<Map<String, Object>> itemStats = calcitePositiveCaseStatService.getPositiveCaseItemStatistics(startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("page_data", pageResult);
            result.put("summary", summary);
            result.put("item_statistics", itemStats);
            result.put("month", now.format(DateTimeFormatter.ofPattern("yyyy年MM月")));
            result.put("date_range", startTime + " 至 " + endTime);
            
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("查询当月阳性病例统计失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 导出阳性病例数据
     */
    @ApiOperation(value = "导出阳性病例数据", notes = "导出指定时间范围内的阳性病例数据")
    @GetMapping("/export")
    public Result<Map<String, Object>> exportPositiveCaseData(
            @ApiParam(value = "开始日期", example = "2024-01-01") 
            @RequestParam(required = false) String startTime,
            
            @ApiParam(value = "结束日期", example = "2024-01-31") 
            @RequestParam(required = false) String endTime,
            
            @ApiParam(value = "导出数量限制", example = "10000") 
            @RequestParam(defaultValue = "10000") Integer limit) {
        
        try {
            // 参数验证
            if (limit > 50000) {
                return Result.error("导出数量不能超过50000条，请缩小查询范围");
            }
            
            // 参数验证和默认值设置
            String[] dateRange = validateAndSetDefaultDates(startTime, endTime);
            startTime = dateRange[0];
            endTime = dateRange[1];
            
            log.info("导出阳性病例数据，时间范围：{} - {}，限制：{}条", startTime, endTime, limit);
            
            // 计算需要查询的页数
            int pageSize = 1000; // 每次查询1000条
            int totalPages = (limit + pageSize - 1) / pageSize;
            
            List<Map<String, Object>> allData = new ArrayList<>();
            
            for (int pageNo = 1; pageNo <= totalPages; pageNo++) {
                int currentPageSize = Math.min(pageSize, limit - allData.size());
                if (currentPageSize <= 0) break;
                
                Map<String, Object> pageResult = calcitePositiveCaseStatService.getPositiveCaseStatistics(
                    startTime, endTime, pageNo, currentPageSize,null, null, null);
                
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> records = (List<Map<String, Object>>) pageResult.get("records");
                
                if (records != null && !records.isEmpty()) {
                    allData.addAll(records);
                } else {
                    break; // 没有更多数据
                }
            }
            
            Map<String, Object> exportResult = new HashMap<>();
            exportResult.put("data", allData);
            exportResult.put("total_exported", allData.size());
            exportResult.put("date_range", startTime + " 至 " + endTime);
            exportResult.put("export_time", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            log.info("阳性病例数据导出完成，共导出{}条记录", allData.size());
            
            return Result.OK(exportResult);
            
        } catch (Exception e) {
            log.error("导出阳性病例数据失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取阳性病例趋势数据
     */
    @ApiOperation(value = "获取阳性病例趋势", notes = "获取指定时间范围内的阳性病例趋势数据，用于图表展示")
    @GetMapping("/trend")
    public Result<Map<String, Object>> getPositiveCaseTrend(
            @ApiParam(value = "开始日期", example = "2024-01-01") 
            @RequestParam(required = false) String startTime,
            
            @ApiParam(value = "结束日期", example = "2024-01-31") 
            @RequestParam(required = false) String endTime,
            
            @ApiParam(value = "分组类型", example = "day", allowableValues = "day,week,month") 
            @RequestParam(defaultValue = "day") String groupType) {
        
        try {
            // 参数验证和默认值设置
            String[] dateRange = validateAndSetDefaultDates(startTime, endTime);
            startTime = dateRange[0];
            endTime = dateRange[1];
            
            log.info("查询阳性病例趋势数据，时间范围：{} - {}，分组类型：{}", startTime, endTime, groupType);
            
            // 构建趋势查询SQL
            String trendSql = buildTrendSql(groupType);
            
            // 执行趋势查询
            // 这里可以扩展实现具体的趋势查询逻辑
            
            Map<String, Object> trendResult = new HashMap<>();
            trendResult.put("group_type", groupType);
            trendResult.put("date_range", startTime + " 至 " + endTime);
            trendResult.put("message", "趋势查询功能待实现");
            
            return Result.OK(trendResult);
            
        } catch (Exception e) {
            log.error("查询阳性病例趋势数据失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    
    /**
     * 构建趋势查询SQL
     */
    private String buildTrendSql(String groupType) {
        String dateFormat;
        switch (groupType) {
            case "week":
                dateFormat = "%Y-%u";
                break;
            case "month":
                dateFormat = "%Y-%m";
                break;
            default:
                dateFormat = "%Y-%m-%d";
                break;
        }
        
        return String.format("""
            SELECT 
                DATE_FORMAT(r.reg_time, '%s') AS time_period,
                COUNT(*) AS case_count,
                COUNT(DISTINCT r.exam_no) AS person_count
            FROM mysql.customer_reg r
            INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
            WHERE r.status = '已登记'
                AND r.del_flag != 1
                AND ir.abnormal_flag = 1
                AND (? IS NULL OR r.reg_time >= ?)
                AND (? IS NULL OR r.reg_time <= ?)
            GROUP BY DATE_FORMAT(r.reg_time, '%s')
            ORDER BY time_period
            """, dateFormat, dateFormat);
    }
    
    /**
     * 验证并设置默认日期
     */
    private String[] validateAndSetDefaultDates(String startTime, String endTime) {
        
        LocalDate now = LocalDate.now();
        
        // 设置默认开始时间（当月第一天）
        if (startTime == null || startTime.trim().isEmpty()) {
            startTime = now.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        
        // 设置默认结束时间（今天）
        if (endTime == null || endTime.trim().isEmpty()) {
            endTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        
        // 验证日期格式
        try {
            LocalDate.parse(startTime);
            LocalDate.parse(endTime);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式错误，请使用 yyyy-MM-dd格式");
        }
        
        return new String[]{startTime, endTime};
    }

    /**
     * 分页查询阳性病例按年度统计数据
     */
    @ApiOperation(value = "按年度统计阳性病例", notes = "使用Calcite优化的阳性病例按公司分页查询，支持按年度和公司分组统计")
    @GetMapping("/yearStats")
    public Map<String, Object> getPositiveCaseByCompanyStatistics(
            @ApiParam(value = "开始日期", example = "2024-01-01")
            @RequestParam(required = false) String startTime,

            @ApiParam(value = "结束日期", example = "2024-01-31")
            @RequestParam(required = false) String endTime,

            @ApiParam(value = "公司名称")
            @RequestParam(required = false) String companyRegName,

            @ApiParam(value = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer pageNo,

            @ApiParam(value = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") Integer pageSize) {

        try {
            // 参数验证
            if (pageNo < 1) pageNo = 1;
            if (pageSize < 1 || pageSize > 1000) pageSize = 20;

            // 参数验证和默认值设置
            String[] dateRange = validateAndSetDefaultDates(startTime, endTime);
            startTime = dateRange[0];
            endTime = dateRange[1];

            log.info("查询阳性病例按公司统计数据，时间范围：{} - {}，公司：{}，页码：{}，每页：{}",
                startTime, endTime, companyRegName, pageNo, pageSize);

            // 执行Calcite优化查询
            Map<String, Object> pageResult = calcitePositiveCaseStatService.getPositiveCaseByYearStatistics(
                startTime, endTime, companyRegName, pageNo, pageSize);

            return pageResult;

        } catch (Exception e) {
            log.error("查询阳性病例按公司统计数据失败", e);
            return null;
        }
    }
}
