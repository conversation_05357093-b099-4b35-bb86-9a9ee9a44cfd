package org.jeecg.modules.calcite.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.jdbc.CalciteConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.time.Duration;
import java.util.*;

/**
 * Apache Calcite查询执行器
 * 支持流式处理和内存控制
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@Slf4j
public class CalciteQueryExecutor {
    
    @Autowired
    private CalciteConnection calciteConnection;

    private Cache<String, PreparedStatement> statementCache;
    private final Runtime runtime = Runtime.getRuntime();
    
    @PostConstruct
    public void init() {
        // 初始化语句缓存
        this.statementCache = Caffeine.newBuilder()
            .maximumSize(100)
            .expireAfterAccess(Duration.ofMinutes(60))
            .build();

        log.info("Calcite query executor initialized");
    }
    
    /**
     * 执行查询并返回Map结果列表 - 主要方法
     */
    public List<Map<String, Object>> executeQuery(String sql, Object... params) {

        long startTime = System.currentTimeMillis();
        String queryId = generateQueryId(sql, params);

        try {
            log.debug("Executing Calcite query: {}", queryId);

            // 内存检查
            checkMemoryUsage();

            // 性能预警
            if (isLargeQuery(sql)) {
                log.warn("Executing potentially large query: {}", queryId);
            }

            // 执行查询
            List<Map<String, Object>> results = executeQueryInternal(sql, params);

            long duration = System.currentTimeMillis() - startTime;

            // 性能监控和告警
            if (duration > 10000) { // 超过10秒
                log.warn("Slow query detected: {} executed in {}ms, returned {} rows",
                    queryId, duration, results.size());
                logPerformanceAdvice(sql, duration);
            } else if (duration > 5000) { // 超过5秒
                log.warn("Query {} executed in {}ms, returned {} rows",
                    queryId, duration, results.size());
                logPerformanceAdvice(sql, duration);
            } else {
                log.info("Calcite query {} executed in {}ms, returned {} rows",
                    queryId, duration, results.size());
            }

            return results;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Calcite query execution failed for {} after {}ms: {}",
                queryId, duration, e.getMessage(), e);
            throw new RuntimeException("Failed to execute Calcite query", e);
        }
    }

    /**
     * 使用命名参数执行查询 - 更清晰的参数管理
     *
     * @param sql SQL查询语句，使用 :paramName 格式的命名参数
     * @param namedParams 命名参数Map
     * @return 查询结果列表
     */
    public List<Map<String, Object>> executeQueryWithNamedParams(String sql, Map<String, Object> namedParams) {

        long startTime = System.currentTimeMillis();
        String queryId = generateQueryId(sql, namedParams);

        try {
            log.debug("Executing Calcite query with named params: {}", queryId);
            log.debug("Named parameters: {}", namedParams);

            // 内存检查
            checkMemoryUsage();

            // 性能预警
            if (isLargeQuery(sql)) {
                log.warn("Executing potentially large query: {}", queryId);
            }

            // 转换命名参数为位置参数
            ParsedQuery parsedQuery = parseNamedParameters(sql, namedParams);

            // 执行查询
            List<Map<String, Object>> results = executeQueryInternal(parsedQuery.sql, parsedQuery.params);

            long duration = System.currentTimeMillis() - startTime;

            // 性能监控和告警
            if (duration > 10000) { // 超过10秒
                log.warn("Very slow named query detected: {} executed in {}ms, returned {} rows",
                    queryId, duration, results.size());
                logPerformanceAdvice(sql, duration);
            } else if (duration > 5000) { // 超过5秒
                log.warn("Slow named query detected: {} executed in {}ms, returned {} rows",
                    queryId, duration, results.size());
                logPerformanceAdvice(sql, duration);
            } else {
                log.info("Calcite named query {} executed in {}ms, returned {} rows",
                    queryId, duration, results.size());
            }

            return results;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Calcite named query execution failed for {} after {}ms: {}",
                queryId, duration, e.getMessage(), e);
            throw new RuntimeException("Failed to execute Calcite named query", e);
        }
    }

    /**
     * 内部执行查询方法
     */
    private List<Map<String, Object>> executeQueryInternal(String sql, Object... params) throws SQLException {

        // 获取或创建预编译语句
        PreparedStatement stmt = getOrCreateStatement(sql);

        // 设置查询超时（30秒）
        stmt.setQueryTimeout(30);

        // 设置参数
        setParameters(stmt, params);

        // 执行查询并处理结果
        List<Map<String, Object>> results = new ArrayList<>();

        try (ResultSet rs = stmt.executeQuery()) {

            int batchCount = 0;
            int totalRows = 0;
            int batchSize = 1000; // 减小批次大小，提高响应性
            long lastLogTime = System.currentTimeMillis();

            while (rs.next()) {
                Map<String, Object> row = mapResultSetToMap(rs);
                results.add(row);

                totalRows++;

                // 批次处理和内存控制
                if (totalRows % batchSize == 0) {
                    batchCount++;
                    long currentTime = System.currentTimeMillis();

                    // 每5秒输出一次进度日志
                    if (currentTime - lastLogTime > 5000) {
                        log.info("Processing progress: {} rows processed in {} batches", totalRows, batchCount);
                        lastLogTime = currentTime;
                    }

                    // 检查内存使用
                    if (getMemoryUsagePercentage() > 75) {
                        log.warn("Memory usage high at batch {}, triggering GC", batchCount);
                        System.gc();

                        // 如果GC后内存仍然不足，暂停处理
                        if (getMemoryUsagePercentage() > 85) {
                            log.warn("Memory still high after GC, pausing processing");
                            Thread.sleep(100);
                        }
                    }

                    // 检查是否应该提前返回（对于大结果集）
                    if (totalRows > 50000) {
                        log.warn("Large result set detected ({}+ rows), consider adding LIMIT clause", totalRows);
                    }
                }
            }

            log.info("Query completed: {} rows in {} batches", totalRows, batchCount);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Query processing interrupted", e);
        }

        return results;
    }
    
    /**
     * 获取或创建预编译语句
     */
    private PreparedStatement getOrCreateStatement(String sql) throws SQLException {
        return statementCache.get(sql, key -> {
            try {
                return calciteConnection.prepareStatement(key);
            } catch (SQLException e) {
                throw new RuntimeException("Failed to prepare statement", e);
            }
        });
    }
    
    /**
     * 设置查询参数
     */
    private void setParameters(PreparedStatement stmt, Object... params) throws SQLException {
        for (int i = 0; i < params.length; i++) {
            stmt.setObject(i + 1, params[i]);
        }
    }
    
    /**
     * 映射ResultSet到Map
     */
    private Map<String, Object> mapResultSetToMap(ResultSet rs) throws SQLException {
        Map<String, Object> row = new HashMap<>();
        ResultSetMetaData metaData = rs.getMetaData();
        
        for (int i = 1; i <= metaData.getColumnCount(); i++) {
            String columnName = metaData.getColumnLabel(i);
            Object value = rs.getObject(i);
            row.put(columnName, value);
        }
        
        return row;
    }
    
    /**
     * 检查内存使用
     */
    private void checkMemoryUsage() {
        double memoryUsage = getMemoryUsagePercentage();
        
        if (memoryUsage > 80) {
            log.warn("High memory usage detected: {}%", String.format("%.1f", memoryUsage));
            System.gc();
        }
    }
    
    /**
     * 获取内存使用百分比
     */
    private double getMemoryUsagePercentage() {
        long maxMemory = runtime.maxMemory();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        
        return (usedMemory * 100.0) / maxMemory;
    }
    
    /**
     * 生成查询ID
     */
    private String generateQueryId(String sql, Object... params) {
        return String.format("query_%d", (sql + java.util.Arrays.toString(params)).hashCode());
    }
    
    /**
     * 清理缓存
     */
    public void clearCache() {
        statementCache.invalidateAll();
        log.info("Statement cache cleared");
    }
    
    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return statementCache.stats().toString();
    }
    
    /**
     * 获取内存状态信息
     */
    public Map<String, Object> getMemoryStatus() {
        Map<String, Object> status = new HashMap<>();
        
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        status.put("maxMemory", formatBytes(maxMemory));
        status.put("totalMemory", formatBytes(totalMemory));
        status.put("usedMemory", formatBytes(usedMemory));
        status.put("freeMemory", formatBytes(freeMemory));
        status.put("usagePercentage", String.format("%.1f%%", getMemoryUsagePercentage()));
        
        return status;
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }



    /**
     * 检测是否为大查询
     */
    private boolean isLargeQuery(String sql) {
        String lowerSql = sql.toLowerCase();

        // 检测可能的大查询特征
        boolean hasJoins = lowerSql.contains("join");
        boolean hasGroupBy = lowerSql.contains("group by");
        boolean hasOrderBy = lowerSql.contains("order by");
        boolean noLimit = !lowerSql.contains("limit");
        boolean hasCount = lowerSql.contains("count(");

        // 如果有多个复杂操作且没有LIMIT，认为是大查询
        int complexityScore = 0;
        if (hasJoins) complexityScore++;
        if (hasGroupBy) complexityScore++;
        if (hasOrderBy) complexityScore++;
        if (hasCount) complexityScore++;

        return complexityScore >= 2 && noLimit;
    }

    /**
     * 优化查询性能的建议
     */
    private void logPerformanceAdvice(String sql, long duration) {
        if (duration > 5000) { // 超过5秒
            log.warn("Performance advice for slow query ({}ms):", duration);

            String lowerSql = sql.toLowerCase();
            if (!lowerSql.contains("limit")) {
                log.warn("- Consider adding LIMIT clause to reduce result set size");
            }
            if (lowerSql.contains("order by") && !lowerSql.contains("limit")) {
                log.warn("- ORDER BY without LIMIT can be expensive for large datasets");
            }
            if (lowerSql.contains("count(*)") && lowerSql.contains("group by")) {
                log.warn("- COUNT(*) with GROUP BY can be slow, consider using approximate counts");
            }
            if (lowerSql.contains("distinct") && lowerSql.contains("count")) {
                log.warn("- COUNT(DISTINCT) operations are expensive, consider alternative approaches");
            }
        }
    }

    /**
     * 生成查询ID用于日志跟踪（命名参数版本）
     */
    private String generateQueryId(String sql, Map<String, Object> namedParams) {
        return "named_query_" + (sql + namedParams.toString()).hashCode();
    }

    /**
     * 解析命名参数，转换为位置参数
     */
    private ParsedQuery parseNamedParameters(String sql, Map<String, Object> namedParams) {
        List<Object> params = new ArrayList<>();
        String parsedSql = sql;

        // 查找所有命名参数 :paramName
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(":(\\w+)");
        java.util.regex.Matcher matcher = pattern.matcher(sql);

        // 记录参数顺序
        List<String> paramOrder = new ArrayList<>();
        while (matcher.find()) {
            String paramName = matcher.group(1);
            if (!paramOrder.contains(paramName)) {
                paramOrder.add(paramName);
            }
        }

        // 替换命名参数为 ?
        for (String paramName : paramOrder) {
            parsedSql = parsedSql.replaceAll(":" + paramName + "\\b", "?");
            Object paramValue = namedParams.get(paramName);
            if (paramValue == null) {
                log.warn("Named parameter '{}' not found in provided parameters", paramName);
            }
            params.add(paramValue);
        }

        log.debug("Parsed SQL: {}", parsedSql);
        log.debug("Parameter order: {}", paramOrder);
        log.debug("Parameter values: {}", params);

        return new ParsedQuery(parsedSql, params.toArray());
    }

    /**
     * 解析后的查询对象
     */
    private static class ParsedQuery {
        final String sql;
        final Object[] params;

        ParsedQuery(String sql, Object[] params) {
            this.sql = sql;
            this.params = params;
        }
    }
}
