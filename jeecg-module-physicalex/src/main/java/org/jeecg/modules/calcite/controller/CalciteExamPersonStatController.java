package org.jeecg.modules.calcite.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.calcite.service.CalciteExamPersonStatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 使用Apache Calcite优化的体检统计API接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Api(tags = "体检统计-Calcite优化版")
@RestController
@RequestMapping("/api/exam/stat")
@Slf4j
public class CalciteExamPersonStatController {
    
    @Autowired
    private CalciteExamPersonStatService calciteExamPersonStatService;
    
    /**
     * 获取体检人数统计数据（按日期分组）
     */
    @ApiOperation(value = "获取体检人数统计", notes = "使用Calcite优化的体检人数统计查询，支持分页和按日期范围查询")
    @GetMapping("/examTotal")
    public Map<String, Object> getDailyExamStatistics(@ApiParam(value = "开始日期") @RequestParam(required = false) String startTime, @ApiParam(value = "结束日期") @RequestParam(required = false) String endTime, @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNo, @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Integer pageSize) {

        try {
            // 参数验证和默认值设置
            String[] dateRange = validateAndSetDefaultDates(startTime, endTime);
            startTime = dateRange[0];
            endTime = dateRange[1];

            log.info("查询体检统计数据，时间范围：{} - {}，页码：{}，每页：{}",
                startTime, endTime, pageNo, pageSize);

            // 执行Calcite优化分页查询
            Map<String, Object> pageResult = calciteExamPersonStatService.getExamTotalStat(
                startTime, endTime, pageNo, pageSize);

            return pageResult;

        } catch (Exception e) {
            log.error("查询体检统计数据失败", e);
            return null;
        }
    }
    
    /**
     * 获取体检统计汇总数据
     */
    @ApiOperation(value = "获取体检统计汇总", notes = "获取指定时间范围内的体检统计汇总数据")
    @GetMapping("/summary")
    public Result<Map<String, Object>> getSummaryStatistics(
            @ApiParam(value = "开始日期", example = "2024-01-01") 
            @RequestParam(required = false) String startTime,
            
            @ApiParam(value = "结束日期", example = "2024-01-31") 
            @RequestParam(required = false) String endTime) {
        
        try {
            // 参数验证和默认值设置
            String[] dateRange = validateAndSetDefaultDates(startTime, endTime);
            startTime = dateRange[0];
            endTime = dateRange[1];
            
            log.info("查询体检统计汇总数据，时间范围：{} - {}", startTime, endTime);
            
            // 执行汇总查询
            Map<String, Object> summary = calciteExamPersonStatService.getSummaryStatistics(startTime, endTime);
            
            return Result.OK(summary);
            
        } catch (Exception e) {
            log.error("查询体检统计汇总数据失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }
    


    /**
     * 验证并设置默认日期
     */
    private String[] validateAndSetDefaultDates(String startTime, String endTime) {
        
        LocalDate now = LocalDate.now();
        
        // 设置默认开始时间（当月第一天）
        if (startTime == null || startTime.trim().isEmpty()) {
            startTime = now.withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        
        // 设置默认结束时间（今天）
        if (endTime == null || endTime.trim().isEmpty()) {
            endTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        
        // 验证日期格式
        try {
            LocalDate.parse(startTime);
            LocalDate.parse(endTime);
        } catch (Exception e) {
            throw new IllegalArgumentException("日期格式错误，请使用 yyyy-MM-dd 格式");
        }
        
        return new String[]{startTime, endTime};
    }
    

    /**
     * 安全获取整数值
     */
    private Integer getIntValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return 0;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }
}
