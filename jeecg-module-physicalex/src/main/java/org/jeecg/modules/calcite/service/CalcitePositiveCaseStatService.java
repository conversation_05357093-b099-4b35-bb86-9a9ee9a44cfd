package org.jeecg.modules.calcite.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 使用Apache Calcite优化的阳性病例统计服务
 * 支持大数据量查询和流式处理
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@Slf4j
public class CalcitePositiveCaseStatService {
    
    @Autowired
    private CalciteQueryExecutor calciteQueryExecutor;
    
    /**
     * 获取阳性病例统计数据 - 分页查询
     *
     * @param startTime 开始时间 (yyyy-MM-dd)
     * @param endTime 结束时间 (yyyy-MM-dd)
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @param itemGroupName 组合项目名称
     * @param examNo 体检号
     * @param name 姓名
     * @return 分页结果
     */
    public Map<String, Object> getPositiveCaseStatistics(String startTime, String endTime,
                                                        Integer pageNo, Integer pageSize,
                                                        String itemGroupName, String examNo, String name) {
        
        long startTimeMs = System.currentTimeMillis();
        log.info("开始执行阳性病例统计查询，时间范围：{} - {}，页码：{}，每页：{}，组合项目：{}，体检号：{}，姓名：{}",
            startTime, endTime, pageNo, pageSize, itemGroupName, examNo, name);

        try {
            // 构建优化的SQL查询
            String optimizedSql = buildOptimizedSql(pageNo, pageSize, itemGroupName, examNo, name);

            // 准备查询参数
            List<Object> params = buildQueryParams(startTime, endTime, itemGroupName, examNo, name, pageSize, pageNo);
            
            // 执行Calcite优化查询
            List<Map<String, Object>> results = calciteQueryExecutor.executeQuery(
                optimizedSql, 
                params.toArray()
            );
            
            // 获取总数
            Long totalCount = getTotalCount(startTime, endTime, itemGroupName, examNo, name);
            
            // 后处理结果数据
            List<Map<String, Object>> processedResults = postProcessResults(results);
            
            // 构建分页结果
            Map<String, Object> pageResult = buildPageResult(processedResults, totalCount, pageNo, pageSize);
            
            long duration = System.currentTimeMillis() - startTimeMs;
            log.info("阳性病例统计查询完成，耗时：{}ms，返回{}条记录，总数：{}", 
                duration, processedResults.size(), totalCount);
            
            return pageResult;
            
        } catch (Exception e) {
            log.error("阳性病例统计查询执行失败", e);
            throw new RuntimeException("阳性病例统计查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建优化的SQL查询 - 使用Calcite优化
     * 简化版本，避免复杂的CASE语句导致解析错误
     */
    private String buildOptimizedSql(Integer pageNo, Integer pageSize, String itemGroupName, String examNo, String name) {
        StringBuilder sql = new StringBuilder("""
            SELECT
                r.exam_no,
                r.name,
                r.gender,
                r.age,
                r.company_reg_name,
                r.reg_time,
                ir.item_group_name,
                ir.item_name,
                ir.`value` AS item_value,
                ir.abnormal_symbol,
                ir.abnormal_flag_desc,
                ir.value_ref_range,
                ir.unit,
                ir.doctor_name,
                ir.create_time,
                ir.value_type,
                ir.abnormal_flag,
                r.id AS customer_reg_id,
                ir.id AS item_result_id
            FROM mysql.customer_reg r
            INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
            INNER JOIN mysql.item_group ig ON ig.id = ir.item_group_id
            WHERE r.status = ?
                AND r.del_flag = 0
                AND ir.abnormal_flag = 1
                AND (? IS NULL OR r.reg_time >= ?)
                AND (? IS NULL OR r.reg_time <= ?)
            """);

        // 添加组合项目名称条件
        if (itemGroupName != null && !itemGroupName.trim().isEmpty()) {
            sql.append("AND ir.item_group_name LIKE ? ");
        }

        // 添加体检号条件
        if (examNo != null && !examNo.trim().isEmpty()) {
            sql.append("AND r.exam_no LIKE ? ");
        }

        // 添加姓名条件
        if (name != null && !name.trim().isEmpty()) {
            sql.append("AND r.name LIKE ? ");
        }

        sql.append("""
            ORDER BY r.reg_time DESC, ir.create_time DESC
            LIMIT ?
            OFFSET ?
            """);

        return sql.toString();
    }
    
    /**
     * 获取总数查询
     */
    private Long getTotalCount(String startTime, String endTime, String itemGroupName, String examNo, String name) {
        
        StringBuilder countSql = new StringBuilder("""
            SELECT COUNT(*) AS total_count
            FROM mysql.customer_reg r
            INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
            INNER JOIN mysql.item_group ig ON ig.id = ir.item_group_id
            WHERE r.status = ?
                AND r.del_flag = 0
                AND ir.abnormal_flag = 1
                AND (? IS NULL OR r.reg_time >= ?)
                AND (? IS NULL OR r.reg_time <= ?)
            """);

        // 添加额外的查询条件
        if (itemGroupName != null && !itemGroupName.trim().isEmpty()) {
            countSql.append("AND ir.item_group_name LIKE ? ");
        }

        if (examNo != null && !examNo.trim().isEmpty()) {
            countSql.append("AND r.exam_no LIKE ? ");
        }

        if (name != null && !name.trim().isEmpty()) {
            countSql.append("AND r.name LIKE ? ");
        }

        // 构建参数列表
        List<Object> params = buildCountParams(startTime, endTime, itemGroupName, examNo, name);
        
        try {
            List<Map<String, Object>> countResult = calciteQueryExecutor.executeQuery(countSql.toString(), params.toArray());
            if (!countResult.isEmpty()) {
                Object count = countResult.get(0).get("total_count");
                return count instanceof Number ? ((Number) count).longValue() : 0L;
            }
        } catch (Exception e) {
            log.error("获取阳性病例总数失败", e);
        }
        
        return 0L;
    }
    
    /**
     * 后处理查询结果
     */
    private List<Map<String, Object>> postProcessResults(List<Map<String, Object>> results) {
        
        List<Map<String, Object>> processedResults = new ArrayList<>();
        
        for (Map<String, Object> row : results) {
            Map<String, Object> processedRow = new HashMap<>(row);

            // 处理公司名称
            String companyName = (String) row.get("company_reg_name");
            if (companyName == null || companyName.trim().isEmpty()) {
                processedRow.put("company_reg_name", "个人体检");
            }

            // 格式化日期
            Object regTime = row.get("reg_time");
            if (regTime != null) {
                processedRow.put("reg_time_formatted", formatDateTime(regTime));
            }

            Object createTime = row.get("create_time");
            if (createTime != null) {
                processedRow.put("create_time_formatted", formatDateTime(createTime));
            }
            
            // 处理异常标识
            String abnormalSymbol = (String) row.get("abnormal_symbol");
            if (abnormalSymbol != null) {
                processedRow.put("abnormal_level", getAbnormalLevel(abnormalSymbol));
            }
            
            // 处理数值和结果字段 - 根据value_type分离显示
            String valueType = (String) row.get("value_type");
            String itemValue = (String) row.get("item_value");
            String refRange = (String) row.get("value_ref_range");
            String unit = (String) row.get("unit");

            if ("数值型".equals(valueType)) {
                processedRow.put("value", itemValue != null ? itemValue : "");
                processedRow.put("result", "");
                if (itemValue != null && !itemValue.trim().isEmpty()) {
                    processedRow.put("value_with_unit", itemValue + (unit != null ? " " + unit : ""));
                    processedRow.put("value_with_ref", itemValue + (unit != null ? " " + unit : "") +
                        (refRange != null ? " (参考值: " + refRange + ")" : ""));
                }
            } else if ("说明型".equals(valueType)) {
                processedRow.put("value", "");
                processedRow.put("result", itemValue != null ? itemValue : "");
                processedRow.put("value_with_unit", "");
                processedRow.put("value_with_ref", itemValue != null ? itemValue : "");
            } else {
                processedRow.put("value", "");
                processedRow.put("result", itemValue != null ? itemValue : "");
                processedRow.put("value_with_unit", "");
                processedRow.put("value_with_ref", itemValue != null ? itemValue : "");
            }

            // 添加风险等级
            processedRow.put("risk_level", calculateRiskLevel(abnormalSymbol, itemValue, refRange));
            
            processedResults.add(processedRow);
        }
        
        return processedResults;
    }
    
    /**
     * 构建分页结果
     */
    private Map<String, Object> buildPageResult(List<Map<String, Object>> results, Long totalCount, 
                                               Integer pageNo, Integer pageSize) {
        
        Map<String, Object> pageResult = new HashMap<>();
        
        pageResult.put("data", results);
        pageResult.put("count", totalCount);
        pageResult.put("size", pageSize);
        pageResult.put("current", pageNo);
        pageResult.put("total", (totalCount + pageSize - 1) / pageSize);
        
        // 添加统计信息
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("total_positive_cases", totalCount);
        statistics.put("current_page_count", results.size());
        statistics.put("has_next_page", pageNo * pageSize < totalCount);
        statistics.put("has_prev_page", pageNo > 1);
        
        pageResult.put("statistics", statistics);
        
        return pageResult;
    }
    
    /**
     * 获取阳性病例汇总统计
     */
    public Map<String, Object> getPositiveCaseSummary(String startTime, String endTime) {
        
        String summarySql = """
            SELECT
                COUNT(*) AS total_cases,
                COUNT(DISTINCT r.exam_no) AS total_persons,
                COUNT(DISTINCT ir.item_group_name) AS total_item_groups,
                COUNT(DISTINCT ir.item_name) AS total_items,
                COUNT(DISTINCT r.company_reg_name) AS total_companies
            FROM mysql.customer_reg r
            INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
            INNER JOIN mysql.item_group ig ON ig.id = ir.item_group_id
            WHERE r.status = ?
                AND r.del_flag = 0
                AND ir.abnormal_flag = 1
                AND (? IS NULL OR r.reg_time >= ?)
                AND (? IS NULL OR r.reg_time <= ?)
            """;

        List<Object> params = new ArrayList<>();

        params.add("已登记");

        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 23:59:59");
        } else {
            params.add(null);
        }
        
        try {
            List<Map<String, Object>> summaryResult = calciteQueryExecutor.executeQuery(summarySql, params.toArray());

            if (!summaryResult.isEmpty()) {
                Map<String, Object> summary = new HashMap<>(summaryResult.get(0));

                // 获取额外的统计信息
                addGenderStatistics(summary, startTime, endTime);
                addAgeStatistics(summary, startTime, endTime);
                addCompanyTypeStatistics(summary, startTime, endTime);

                summary.put("query_time_range", startTime + " 至 " + endTime);
                summary.put("query_time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

                return summary;
            }
        } catch (Exception e) {
            log.error("获取阳性病例汇总统计失败", e);
        }
        
        return new HashMap<>();
    }
    
    /**
     * 获取阳性病例项目统计
     */
    public List<Map<String, Object>> getPositiveCaseItemStatistics(String startTime, String endTime) {
        
        String itemStatSql = """
            SELECT
                ir.item_group_name,
                ir.item_name,
                COUNT(*) AS case_count,
                COUNT(DISTINCT r.exam_no) AS person_count
            FROM mysql.customer_reg r
            INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
            INNER JOIN mysql.item_group ig ON ig.id = ir.item_group_id
            WHERE r.status = ?
                AND r.del_flag = 0
                AND ir.abnormal_flag = 1
                AND (? IS NULL OR r.reg_time >= ?)
                AND (? IS NULL OR r.reg_time <= ?)
            GROUP BY ir.item_group_name, ir.item_name
            ORDER BY case_count DESC, person_count DESC
            """;

        List<Object> params = new ArrayList<>();

        params.add("已登记");

        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 23:59:59");
        } else {
            params.add(null);
        }
        
        try {
            List<Map<String, Object>> itemStats = calciteQueryExecutor.executeQuery(itemStatSql, params.toArray());

            // 为每个项目添加额外的统计信息
            for (Map<String, Object> stat : itemStats) {
                String itemGroupName = (String) stat.get("item_group_name");
                String itemName = (String) stat.get("item_name");

                // 添加性别和异常程度统计
                addItemGenderStatistics(stat, itemGroupName, itemName, startTime, endTime);
                addItemAbnormalStatistics(stat, itemGroupName, itemName, startTime, endTime);
            }

            return itemStats;

        } catch (Exception e) {
            log.error("获取阳性病例项目统计失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 格式化日期时间
     */
    private String formatDateTime(Object dateTime) {
        if (dateTime == null) return "";
        
        try {
            if (dateTime instanceof LocalDateTime) {
                return ((LocalDateTime) dateTime).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                return dateTime.toString();
            }
        } catch (Exception e) {
            return dateTime.toString();
        }
    }
    
    /**
     * 获取异常等级
     */
    private String getAbnormalLevel(String abnormalSymbol) {
        if (abnormalSymbol == null) return "正常";
        
        if (abnormalSymbol.contains("↑↑") || abnormalSymbol.contains("++")) {
            return "严重异常";
        } else if (abnormalSymbol.contains("↑") || abnormalSymbol.contains("+")) {
            return "轻度异常";
        } else if (abnormalSymbol.contains("↓↓") || abnormalSymbol.contains("--")) {
            return "严重偏低";
        } else if (abnormalSymbol.contains("↓") || abnormalSymbol.contains("-")) {
            return "轻度偏低";
        } else {
            return "异常";
        }
    }
    
    /**
     * 计算风险等级
     */
    private String calculateRiskLevel(String abnormalSymbol, String value, String refRange) {
        String abnormalLevel = getAbnormalLevel(abnormalSymbol);
        
        switch (abnormalLevel) {
            case "严重异常":
            case "严重偏低":
                return "高风险";
            case "轻度异常":
            case "轻度偏低":
                return "中风险";
            default:
                return "低风险";
        }
    }
    
    /**
     * 安全获取Long值
     */
    private Long getLongValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return 0L;
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    /**
     * 添加性别统计
     */
    private void addGenderStatistics(Map<String, Object> summary, String startTime, String endTime) {
        try {
            String genderSql = """
                SELECT
                    r.gender,
                    COUNT(*) AS count
                FROM mysql.customer_reg r
                INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
                WHERE r.status = ?
                    AND r.del_flag = 0
                    AND ir.abnormal_flag = 1
                    AND (? IS NULL OR r.reg_time >= ?)
                    AND (? IS NULL OR r.reg_time <= ?)
                GROUP BY r.gender
                """;

            List<Object> params = buildParams(startTime, endTime);
            List<Map<String, Object>> genderResult = calciteQueryExecutor.executeQuery(genderSql, params.toArray());

            long maleCount = 0, femaleCount = 0;
            for (Map<String, Object> row : genderResult) {
                String gender = (String) row.get("gender");
                long count = getLongValue(row, "count");
                if ("男".equals(gender)) {
                    maleCount = count;
                } else if ("女".equals(gender)) {
                    femaleCount = count;
                }
            }

            summary.put("male_count", maleCount);
            summary.put("female_count", femaleCount);

            long totalCases = getLongValue(summary, "total_cases");
            if (totalCases > 0) {
                summary.put("male_rate", String.format("%.1f%%", (maleCount * 100.0) / totalCases));
                summary.put("female_rate", String.format("%.1f%%", (femaleCount * 100.0) / totalCases));
            }

        } catch (Exception e) {
            log.error("获取性别统计失败", e);
            summary.put("male_count", 0L);
            summary.put("female_count", 0L);
        }
    }

    /**
     * 添加年龄段统计
     */
    private void addAgeStatistics(Map<String, Object> summary, String startTime, String endTime) {
        try {
            String ageSql = """
                SELECT
                    r.age,
                    COUNT(*) AS count
                FROM mysql.customer_reg r
                INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
                WHERE r.status = ?
                    AND r.del_flag = 0
                    AND ir.abnormal_flag = 1
                    AND (? IS NULL OR r.reg_time >= ?)
                    AND (? IS NULL OR r.reg_time <= ?)
                GROUP BY r.age
                """;

            List<Object> params = buildParams(startTime, endTime);
            List<Map<String, Object>> ageResult = calciteQueryExecutor.executeQuery(ageSql, params.toArray());

            long ageUnder18 = 0, age18_30 = 0, age31_45 = 0, age46_60 = 0, ageOver60 = 0;

            for (Map<String, Object> row : ageResult) {
                Integer age = (Integer) row.get("age");
                long count = getLongValue(row, "count");

                if (age != null) {
                    if (age < 18) ageUnder18 += count;
                    else if (age <= 30) age18_30 += count;
                    else if (age <= 45) age31_45 += count;
                    else if (age <= 60) age46_60 += count;
                    else ageOver60 += count;
                }
            }

            summary.put("age_under_18", ageUnder18);
            summary.put("age_18_30", age18_30);
            summary.put("age_31_45", age31_45);
            summary.put("age_46_60", age46_60);
            summary.put("age_over_60", ageOver60);

        } catch (Exception e) {
            log.error("获取年龄段统计失败", e);
            summary.put("age_under_18", 0L);
            summary.put("age_18_30", 0L);
            summary.put("age_31_45", 0L);
            summary.put("age_46_60", 0L);
            summary.put("age_over_60", 0L);
        }
    }

    /**
     * 添加公司类型统计
     */
    private void addCompanyTypeStatistics(Map<String, Object> summary, String startTime, String endTime) {
        try {
            String companySql = """
                SELECT
                    r.company_reg_name,
                    COUNT(*) AS count
                FROM mysql.customer_reg r
                INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
                WHERE r.status = ?
                    AND r.del_flag = 0
                    AND ir.abnormal_flag = 1
                    AND (? IS NULL OR r.reg_time >= ?)
                    AND (? IS NULL OR r.reg_time <= ?)
                GROUP BY r.company_reg_name
                """;

            List<Object> params = buildParams(startTime, endTime);
            List<Map<String, Object>> companyResult = calciteQueryExecutor.executeQuery(companySql, params.toArray());

            long personalCount = 0, companyCount = 0;
            for (Map<String, Object> row : companyResult) {
                String companyName = (String) row.get("company_reg_name");
                long count = getLongValue(row, "count");
                if (companyName == null || companyName.trim().isEmpty()) {
                    personalCount = count;
                } else {
                    companyCount += count;
                }
            }

            summary.put("personal_count", personalCount);
            summary.put("company_count", companyCount);

            long totalCases = getLongValue(summary, "total_cases");
            if (totalCases > 0) {
                summary.put("personal_rate", String.format("%.1f%%", (personalCount * 100.0) / totalCases));
                summary.put("company_rate", String.format("%.1f%%", (companyCount * 100.0) / totalCases));
            }

        } catch (Exception e) {
            log.error("获取公司类型统计失败", e);
            summary.put("personal_count", 0L);
            summary.put("company_count", 0L);
        }
    }

    /**
     * 构建查询参数
     */
    private List<Object> buildParams(String startTime, String endTime) {
        List<Object> params = new ArrayList<>();

        params.add("已登记");

        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 00:00:00");
            params.add(endTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        return params;
    }

    /**
     * 为项目添加性别统计
     */
    private void addItemGenderStatistics(Map<String, Object> stat, String itemGroupName, String itemName, String startTime, String endTime) {
        try {
            String genderSql = """
                SELECT
                    r.gender,
                    COUNT(*) AS count
                FROM mysql.customer_reg r
                INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
                WHERE r.status = ?
                    AND r.del_flag = 0
                    AND ir.abnormal_flag = 1
                    AND ir.item_group_name = ?
                    AND ir.item_name = ?
                    AND (? IS NULL OR r.reg_time >= ?)
                    AND (? IS NULL OR r.reg_time <= ?)
                GROUP BY r.gender
                """;

            List<Object> params = new ArrayList<>();
            params.add("已登记");
            params.add(itemGroupName);
            params.add(itemName);
            params.addAll(buildParams(startTime, endTime));

            List<Map<String, Object>> genderResult = calciteQueryExecutor.executeQuery(genderSql, params.toArray());

            long maleCount = 0, femaleCount = 0;
            for (Map<String, Object> row : genderResult) {
                String gender = (String) row.get("gender");
                long count = getLongValue(row, "count");
                if ("男".equals(gender)) {
                    maleCount = count;
                } else if ("女".equals(gender)) {
                    femaleCount = count;
                }
            }

            stat.put("male_count", maleCount);
            stat.put("female_count", femaleCount);

            long caseCount = getLongValue(stat, "case_count");
            if (caseCount > 0) {
                stat.put("male_rate", String.format("%.1f%%", (maleCount * 100.0) / caseCount));
                stat.put("female_rate", String.format("%.1f%%", (femaleCount * 100.0) / caseCount));
            }

        } catch (Exception e) {
            log.error("获取项目性别统计失败: {} - {}", itemGroupName, itemName, e);
            stat.put("male_count", 0L);
            stat.put("female_count", 0L);
        }
    }

    /**
     * 为项目添加异常程度统计
     */
    private void addItemAbnormalStatistics(Map<String, Object> stat, String itemGroupName, String itemName, String startTime, String endTime) {
        try {
            String abnormalSql = """
                SELECT
                    ir.abnormal_symbol,
                    COUNT(*) AS count
                FROM mysql.customer_reg r
                INNER JOIN mysql.customer_reg_item_result ir ON r.id = ir.customer_reg_id
                WHERE r.status = ?
                    AND r.del_flag = 0
                    AND ir.abnormal_flag = 1
                    AND ir.item_group_name = ?
                    AND ir.item_name = ?
                    AND (? IS NULL OR r.reg_time >= ?)
                    AND (? IS NULL OR r.reg_time <= ?)
                GROUP BY ir.abnormal_symbol
                """;

            List<Object> params = new ArrayList<>();
            params.add("已登记");
            params.add(itemGroupName);
            params.add(itemName);
            params.addAll(buildParams(startTime, endTime));

            List<Map<String, Object>> abnormalResult = calciteQueryExecutor.executeQuery(abnormalSql, params.toArray());

            long highCount = 0, lowCount = 0, positiveCount = 0;
            for (Map<String, Object> row : abnormalResult) {
                String abnormalSymbol = (String) row.get("abnormal_symbol");
                long count = getLongValue(row, "count");

                if (abnormalSymbol != null) {
                    if (abnormalSymbol.contains("↑")) {
                        highCount += count;
                    } else if (abnormalSymbol.contains("↓")) {
                        lowCount += count;
                    } else if (abnormalSymbol.contains("+")) {
                        positiveCount += count;
                    }
                }
            }

            stat.put("high_count", highCount);
            stat.put("low_count", lowCount);
            stat.put("positive_count", positiveCount);

        } catch (Exception e) {
            log.error("获取项目异常程度统计失败: {} - {}", itemGroupName, itemName, e);
            stat.put("high_count", 0L);
            stat.put("low_count", 0L);
            stat.put("positive_count", 0L);
        }
    }

    /**
     * 构建查询参数列表
     */
    private List<Object> buildQueryParams(String startTime, String endTime, String itemGroupName,
                                         String examNo, String name, Integer pageSize, Integer pageNo) {
        List<Object> params = new ArrayList<>();

        // 第一个参数：status
        params.add("已登记");

        // 时间参数
        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 23:59:59");
            params.add(endTime + " 23:59:59");
        } else {
            params.add(null);
            params.add(null);
        }

        // 添加额外的查询条件参数
        if (itemGroupName != null && !itemGroupName.trim().isEmpty()) {
            params.add("%" + itemGroupName + "%");
        }

        if (examNo != null && !examNo.trim().isEmpty()) {
            params.add("%" + examNo + "%");
        }

        if (name != null && !name.trim().isEmpty()) {
            params.add("%" + name + "%");
        }

        // 分页参数
        params.add(pageSize);
        int offset = (pageNo - 1) * pageSize;
        params.add(offset);

        return params;
    }

    /**
     * 构建统计查询参数列表
     */
    private List<Object> buildCountParams(String startTime, String endTime, String itemGroupName,
                                         String examNo, String name) {
        List<Object> params = new ArrayList<>();

        // 第一个参数：status
        params.add("已登记");

        // 时间参数
        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 23:59:59");
            params.add(endTime + " 23:59:59");
        } else {
            params.add(null);
            params.add(null);
        }

        // 添加额外的查询条件参数
        if (itemGroupName != null && !itemGroupName.trim().isEmpty()) {
            params.add("%" + itemGroupName + "%");
        }

        if (examNo != null && !examNo.trim().isEmpty()) {
            params.add("%" + examNo + "%");
        }

        if (name != null && !name.trim().isEmpty()) {
            params.add("%" + name + "%");
        }

        return params;
    }

    /**
     * 获取阳性病例按年度和公司统计数据 - 分页查询
     *
     * @param startTime 开始时间 (yyyy-MM-dd)
     * @param endTime 结束时间 (yyyy-MM-dd)
     * @param companyRegName 公司名称
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public Map<String, Object> getPositiveCaseByYearStatistics(String startTime, String endTime,
                                                               String companyRegName, Integer pageNo, Integer pageSize) {

        long startTimeMs = System.currentTimeMillis();
        log.info("开始执行阳性病例按公司统计查询，时间范围：{} - {}，公司：{}，页码：{}，每页：{}",
            startTime, endTime, companyRegName, pageNo, pageSize);

        try {
            // 设置默认分页参数
            if (pageNo == null || pageNo < 1) pageNo = 1;
            if (pageSize == null || pageSize < 1) pageSize = 20;

            // 构建优化的SQL查询
            String optimizedSql = buildCompanyStatOptimizedSql();

            // 准备查询参数
            List<Object> params = buildCompanyStatQueryParams(startTime, endTime, companyRegName, pageSize, pageNo);

            // 执行分页查询
            List<Map<String, Object>> results = calciteQueryExecutor.executeQuery(
                optimizedSql,
                params.toArray()
            );

            // 获取总数
            Long totalCount = getCompanyStatTotalCount(startTime, endTime, companyRegName);

            // 后处理结果数据
            List<Map<String, Object>> processedResults = postProcessCompanyStatResults(results, startTime, endTime);

            // 构建分页结果
            Map<String, Object> pageResult = new HashMap<>();
            pageResult.put("data", processedResults);
            pageResult.put("count", totalCount);
            pageResult.put("size", pageSize);
            pageResult.put("current", pageNo);
            pageResult.put("total", (totalCount + pageSize - 1) / pageSize);

            long duration = System.currentTimeMillis() - startTimeMs;
            log.info("阳性病例按公司统计查询完成，耗时：{}ms，返回{}条记录，总数：{}",
                duration, processedResults.size(), totalCount);

            return pageResult;

        } catch (Exception e) {
            log.error("阳性病例按公司统计查询执行失败", e);
            throw new RuntimeException("阳性病例按公司统计查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建按公司统计的优化SQL查询
     */
    private String buildCompanyStatOptimizedSql() {
        return """
            SELECT
                EXTRACT(YEAR FROM cc.reg_time) AS exam_year,
                cc.company_reg_name,
                r.item_name,
                r.abnormal_flag_desc,
                COUNT(DISTINCT r.customer_reg_id) AS count_num
            FROM mysql.customer_reg_item_result r
            INNER JOIN mysql.customer_reg cc ON r.customer_reg_id = cc.id
            WHERE r.abnormal_flag = 1
                AND (? IS NULL OR cc.reg_time >= ?)
                AND (? IS NULL OR cc.reg_time <= ?)
                AND (? IS NULL OR cc.company_reg_name LIKE ?)
            GROUP BY EXTRACT(YEAR FROM cc.reg_time), cc.company_reg_name, r.item_name, r.abnormal_flag_desc
            ORDER BY exam_year DESC, count_num DESC
            LIMIT ?
            OFFSET ?
            """;
    }

    /**
     * 构建按公司统计的查询参数
     */
    private List<Object> buildCompanyStatQueryParams(String startTime, String endTime, String companyRegName, Integer pageSize, Integer pageNo) {
        List<Object> params = new ArrayList<>();

        // 时间参数
        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 00:00:00");
            params.add(endTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        // 公司名称参数（模糊查询）
        if (companyRegName != null && !companyRegName.trim().isEmpty()) {
            String likePattern = "%" + companyRegName.trim() + "%";
            params.add(likePattern);
            params.add(likePattern);
        } else {
            params.add(null);
            params.add(null);
        }

        // 分页参数
        params.add(pageSize);
        int offset = (pageNo - 1) * pageSize;
        params.add(offset);

        return params;
    }

    /**
     * 获取按公司统计的总数
     */
    private Long getCompanyStatTotalCount(String startTime, String endTime, String companyRegName) {

        String countSql = """
            SELECT COUNT(*) AS total_count
            FROM (
                SELECT
                    EXTRACT(YEAR FROM cc.reg_time) AS exam_year,
                    cc.company_reg_name,
                    r.item_name,
                    r.abnormal_flag_desc
                FROM mysql.customer_reg_item_result r
                INNER JOIN mysql.customer_reg cc ON r.customer_reg_id = cc.id
                WHERE r.abnormal_flag = 1
                    AND (? IS NULL OR cc.reg_time >= ?)
                    AND (? IS NULL OR cc.reg_time <= ?)
                    AND (? IS NULL OR cc.company_reg_name LIKE ?)
                GROUP BY EXTRACT(YEAR FROM cc.reg_time), cc.company_reg_name, r.item_name, r.abnormal_flag_desc
            ) AS subquery
            """;

        List<Object> params = new ArrayList<>();

        // 时间参数
        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 23:59:59");
            params.add(endTime + " 23:59:59");
        } else {
            params.add(null);
            params.add(null);
        }

        // 公司名称参数（模糊查询）
        if (companyRegName != null && !companyRegName.trim().isEmpty()) {
            String likePattern = "%" + companyRegName.trim() + "%";
            params.add(likePattern);
            params.add(likePattern);
        } else {
            params.add(null);
            params.add(null);
        }

        try {
            List<Map<String, Object>> countResult = calciteQueryExecutor.executeQuery(countSql, params.toArray());
            if (!countResult.isEmpty()) {
                Object totalCount = countResult.get(0).get("total_count");
                if (totalCount instanceof Number) {
                    return ((Number) totalCount).longValue();
                }
            }
            return 0L;
        } catch (Exception e) {
            log.error("获取阳性病例按公司统计总数失败", e);
            return 0L;
        }
    }

    /**
     * 后处理按公司统计的结果数据
     */
    private List<Map<String, Object>> postProcessCompanyStatResults(List<Map<String, Object>> results, String startTime, String endTime) {

        List<Map<String, Object>> processedResults = new ArrayList<>();

        for (Map<String, Object> row : results) {
            Map<String, Object> processedRow = new HashMap<>(row);

            // 添加查询时间范围
            processedRow.put("startDate", startTime != null ? startTime : "");
            processedRow.put("endDate", endTime != null ? endTime : "");

            // 格式化年份
            Object examYear = row.get("exam_year");
            if (examYear != null) {
                processedRow.put("exam_year", examYear.toString());
            }

            // 手动拼接disease_desc
            String itemName = (String) row.get("item_name");
            String abnormalFlagDesc = (String) row.get("abnormal_flag_desc");

            if (itemName != null) {
                String diseaseDesc = itemName;
                if (abnormalFlagDesc != null && !abnormalFlagDesc.isEmpty()) {
                    diseaseDesc += abnormalFlagDesc;
                }
                processedRow.put("disease_desc", diseaseDesc);

                // 移除原始字段
                processedRow.remove("item_name");
                processedRow.remove("abnormal_flag_desc");
            }

            // 格式化计数
            Object countNum = row.get("count_num");
            if (countNum instanceof Number) {
                processedRow.put("count_num", ((Number) countNum).intValue());
            }

            processedResults.add(processedRow);
        }

        return processedResults;
    }
}
