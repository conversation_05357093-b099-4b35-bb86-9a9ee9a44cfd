package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.adapter.jdbc.JdbcSchema;
import org.apache.calcite.jdbc.CalciteConnection;
import org.apache.calcite.schema.Schema;
import org.apache.calcite.schema.SchemaPlus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Enumeration;
import java.util.Properties;

/**
 * Apache Calcite Tomcat部署专用配置类
 * 解决WAR包部署时的JDBC驱动注册问题
 * 
 * <AUTHOR>
 * @since 2024-07-08
 */
// @Configuration - 已禁用，使用CalciteSimpleConfiguration替代
// @ConditionalOnProperty(name = "calcite.enabled", havingValue = "true", matchIfMissing = false)
@Slf4j
public class CalciteTomcatConfiguration {
    
    @Value("${spring.datasource.dynamic.datasource.master.url}")
    private String mysqlUrl;
    
    @Value("${spring.datasource.dynamic.datasource.master.username}")
    private String mysqlUsername;
    
    @Value("${spring.datasource.dynamic.datasource.master.password}")
    private String mysqlPassword;
    
    @Bean
    @Primary
    public CalciteConnection calciteConnection() throws SQLException {
        // 确保驱动已注册
        ensureDriversRegistered();
        
        Properties info = new Properties();
        
        // 基础配置
        info.setProperty("lex", "MYSQL");
        info.setProperty("conformance", "MYSQL_5");
        info.setProperty("caseSensitive", "false");
        
        // 内存优化配置
        info.setProperty("streaming", "true");
        info.setProperty("memoryLimit", "2GB");
        info.setProperty("spillToDisk", "true");
        info.setProperty("tempDirectory", "./temp/calcite");
        
        // 创建连接
        Connection connection = DriverManager.getConnection("jdbc:calcite:", info);
        CalciteConnection calciteConn = connection.unwrap(CalciteConnection.class);
        
        // 添加MySQL数据源
        addMySQLSchema(calciteConn.getRootSchema());
        
        log.info("Apache Calcite connection established successfully for Tomcat deployment");
        return calciteConn;
    }
    
    /**
     * 确保所有必要的JDBC驱动都已注册
     * 专门处理Tomcat部署环境的驱动注册问题
     */
    private void ensureDriversRegistered() {
        try {
            // 检查当前已注册的驱动
            logRegisteredDrivers();

            // 方法1: 使用多种类加载器尝试加载Calcite驱动
            boolean driverLoaded = false;

            // 尝试使用当前线程的类加载器
            try {
                Class.forName("org.apache.calcite.jdbc.Driver", true,
                             Thread.currentThread().getContextClassLoader());
                driverLoaded = true;
                log.info("Calcite driver loaded using thread context classloader");
            } catch (ClassNotFoundException e) {
                log.warn("Failed to load Calcite driver using thread context classloader: {}", e.getMessage());
            }

            // 如果失败，尝试使用当前类的类加载器
            if (!driverLoaded) {
                try {
                    Class.forName("org.apache.calcite.jdbc.Driver", true,
                                 this.getClass().getClassLoader());
                    driverLoaded = true;
                    log.info("Calcite driver loaded using current class classloader");
                } catch (ClassNotFoundException e) {
                    log.warn("Failed to load Calcite driver using current class classloader: {}", e.getMessage());
                }
            }

            // 如果还是失败，尝试使用系统类加载器
            if (!driverLoaded) {
                try {
                    Class.forName("org.apache.calcite.jdbc.Driver", true,
                                 ClassLoader.getSystemClassLoader());
                    driverLoaded = true;
                    log.info("Calcite driver loaded using system classloader");
                } catch (ClassNotFoundException e) {
                    log.error("Failed to load Calcite driver using system classloader: {}", e.getMessage());
                    throw e;
                }
            }

            // 方法2: 手动创建并注册驱动实例
            try {
                org.apache.calcite.jdbc.Driver calciteDriver = new org.apache.calcite.jdbc.Driver();
                DriverManager.registerDriver(calciteDriver);
                log.info("Calcite driver instance registered successfully");
            } catch (Exception e) {
                log.error("Failed to register Calcite driver instance: {}", e.getMessage());
                throw e;
            }

            // 确保MySQL驱动也已注册
            try {
                Class.forName("com.mysql.cj.jdbc.Driver", true,
                             Thread.currentThread().getContextClassLoader());
                log.info("MySQL driver verified");
            } catch (ClassNotFoundException e) {
                log.warn("MySQL driver verification failed: {}", e.getMessage());
            }

            log.info("All JDBC drivers registered successfully for Tomcat deployment");

            // 再次检查驱动注册状态
            logRegisteredDrivers();

        } catch (Exception e) {
            log.error("Failed to register JDBC drivers for Tomcat deployment", e);
            throw new RuntimeException("Failed to register JDBC drivers", e);
        }
    }
    
    /**
     * 记录当前已注册的JDBC驱动
     */
    private void logRegisteredDrivers() {
        try {
            Enumeration<Driver> drivers = DriverManager.getDrivers();
            log.info("Currently registered JDBC drivers:");
            while (drivers.hasMoreElements()) {
                Driver driver = drivers.nextElement();
                log.info("  - {}", driver.getClass().getName());
            }
        } catch (Exception e) {
            log.warn("Failed to list registered drivers", e);
        }
    }
    
    /**
     * 添加MySQL数据源到Calcite
     */
    private void addMySQLSchema(SchemaPlus rootSchema) {
        try {
            // 创建JDBC Schema连接到MySQL
            DataSource mysqlDataSource = createMySQLDataSource();
            Schema mysqlSchema = JdbcSchema.create(rootSchema, "mysql", mysqlDataSource, null, null);
            rootSchema.add("mysql", mysqlSchema);
            
            log.info("MySQL schema added to Calcite successfully for Tomcat deployment");
            
        } catch (Exception e) {
            log.error("Failed to add MySQL schema to Calcite for Tomcat deployment", e);
            throw new RuntimeException("Failed to configure Calcite MySQL schema", e);
        }
    }
    
    /**
     * 创建优化的MySQL数据源
     */
    private DataSource createMySQLDataSource() {
        // 使用HikariCP创建优化的MySQL连接池
        com.zaxxer.hikari.HikariConfig config = new com.zaxxer.hikari.HikariConfig();
        
        // 构建优化的JDBC URL
        String optimizedUrl = buildOptimizedMySQLUrl();
        config.setJdbcUrl(optimizedUrl);
        config.setUsername(mysqlUsername);
        config.setPassword(mysqlPassword);
        config.setDriverClassName("com.mysql.cj.jdbc.Driver");
        
        // 连接池优化配置
        config.setMaximumPoolSize(10);
        config.setMinimumIdle(2);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        
        // 查询优化配置
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        
        return new com.zaxxer.hikari.HikariDataSource(config);
    }
    
    /**
     * 构建优化的MySQL JDBC URL
     */
    private String buildOptimizedMySQLUrl() {
        StringBuilder urlBuilder = new StringBuilder(mysqlUrl);
        
        // 如果URL中没有参数，添加?
        if (!mysqlUrl.contains("?")) {
            urlBuilder.append("?");
        } else if (!mysqlUrl.endsWith("&")) {
            urlBuilder.append("&");
        }
        
        // 添加性能优化参数
        urlBuilder.append("useServerPrepStmts=true&");
        urlBuilder.append("cachePrepStmts=true&");
        urlBuilder.append("prepStmtCacheSize=250&");
        urlBuilder.append("prepStmtCacheSqlLimit=2048&");
        urlBuilder.append("rewriteBatchedStatements=true&");
        urlBuilder.append("useLocalSessionState=true&");
        urlBuilder.append("elideSetAutoCommits=true&");
        urlBuilder.append("cacheResultSetMetadata=true&");
        urlBuilder.append("cacheServerConfiguration=true&");
        urlBuilder.append("maintainTimeStats=false&");
        urlBuilder.append("useStreamLengthsInPrepStmts=true&");
        urlBuilder.append("useCursorFetch=true&");
        urlBuilder.append("defaultFetchSize=1000");
        
        String result = urlBuilder.toString();
        log.debug("Optimized MySQL URL for Tomcat: {}", maskPassword(result));
        
        return result;
    }
    
    /**
     * 掩码密码用于日志输出
     */
    private String maskPassword(String url) {
        if (url == null) return null;
        return url.replaceAll("password=[^&]*", "password=***");
    }
}
