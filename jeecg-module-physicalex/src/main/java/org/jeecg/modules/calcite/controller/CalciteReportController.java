package org.jeecg.modules.calcite.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.calcite.service.CalciteQueryExecutor;
import org.jeecg.modules.calcite.util.CalciteTypeHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Apache Calcite报表控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Api(tags = "Apache Calcite报表接口")
@RestController
@RequestMapping("/calcite/report")
@Slf4j
public class CalciteReportController {
    
    @Autowired
    private CalciteQueryExecutor queryExecutor;
    

    
    @ApiOperation(value = "获取系统状态", notes = "查看Calcite系统状态和性能指标")
    @GetMapping("/system-status")
    public Result<Map<String, Object>> getSystemStatus() {
        
        Map<String, Object> status = new HashMap<>();
        
        try {
            // 内存状态
            status.put("memory", queryExecutor.getMemoryStatus());
            
            // 缓存状态
            status.put("cacheStats", queryExecutor.getCacheStats());
            
            // 系统信息
            Runtime runtime = Runtime.getRuntime();
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("availableProcessors", runtime.availableProcessors());
            systemInfo.put("javaVersion", System.getProperty("java.version"));
            systemInfo.put("osName", System.getProperty("os.name"));
            systemInfo.put("osVersion", System.getProperty("os.version"));
            status.put("system", systemInfo);
            
            // GC信息
            Map<String, Object> gcInfo = new HashMap<>();
            java.lang.management.ManagementFactory.getGarbageCollectorMXBeans().forEach(gc -> {
                gcInfo.put(gc.getName() + "_collections", gc.getCollectionCount());
                gcInfo.put(gc.getName() + "_time", gc.getCollectionTime() + "ms");
            });
            status.put("gc", gcInfo);
            
            status.put("status", "HEALTHY");
            
        } catch (Exception e) {
            log.error("Failed to get system status", e);
            status.put("status", "ERROR");
            status.put("error", e.getMessage());
        }
        
        return Result.OK(status);
    }
    
    @ApiOperation(value = "清理缓存", notes = "清理Calcite查询缓存")
    @PostMapping("/clear-cache")
    public Result<String> clearCache() {
        try {
            queryExecutor.clearCache();
            log.info("Calcite cache cleared successfully");
            return Result.OK("缓存清理成功");
        } catch (Exception e) {
            log.error("Failed to clear cache", e);
            return Result.error("缓存清理失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "强制垃圾回收", notes = "触发JVM垃圾回收")
    @PostMapping("/force-gc")
    public Result<Map<String, Object>> forceGarbageCollection() {
        try {
            Map<String, Object> beforeGC = queryExecutor.getMemoryStatus();
            
            System.gc();
            Thread.sleep(1000); // 等待GC完成
            
            Map<String, Object> afterGC = queryExecutor.getMemoryStatus();
            
            Map<String, Object> result = new HashMap<>();
            result.put("beforeGC", beforeGC);
            result.put("afterGC", afterGC);
            
            log.info("Forced garbage collection completed");
            return Result.OK(result);
            
        } catch (Exception e) {
            log.error("Failed to force garbage collection", e);
            return Result.error("垃圾回收失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "执行自定义查询", notes = "执行自定义Calcite SQL查询（仅用于测试）")
    @PostMapping("/custom-query")
    public Result<Object> executeCustomQuery(@RequestBody Map<String, Object> request) {
        
        if (!log.isDebugEnabled()) {
            return Result.error("自定义查询仅在DEBUG模式下可用");
        }
        
        try {
            String sql = (String) request.get("sql");
            if (sql == null || sql.trim().isEmpty()) {
                return Result.error("SQL语句不能为空");
            }
            
            // 安全检查：只允许SELECT语句
            if (!sql.trim().toUpperCase().startsWith("SELECT")) {
                return Result.error("仅允许执行SELECT查询");
            }
            
            Object[] params = (Object[]) request.get("params");
            if (params == null) {
                params = new Object[0];
            }
            
            long startTime = System.currentTimeMillis();
            
            List<Map<String, Object>> results = CalciteTypeHelper.castToMapList(
                queryExecutor.executeQuery(sql, Map.class, params));
            
            long duration = System.currentTimeMillis() - startTime;
            
            Map<String, Object> response = new HashMap<>();
            response.put("results", results);
            response.put("rowCount", results.size());
            response.put("executionTime", duration + "ms");
            
            return Result.OK(response);
            
        } catch (Exception e) {
            log.error("Failed to execute custom query", e);
            return Result.error("查询执行失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "健康检查", notes = "检查Calcite服务健康状态")
    @GetMapping("/health")
    public Result<Map<String, Object>> healthCheck() {
        
        Map<String, Object> health = new HashMap<>();
        
        try {
            // 执行简单查询测试连接
            String testSql = "SELECT 1 as test_value";
            List<Map<String, Object>> testResult = CalciteTypeHelper.castToMapList(
                queryExecutor.executeQuery(testSql, Map.class));
            
            if (!testResult.isEmpty() && "1".equals(testResult.get(0).get("test_value").toString())) {
                health.put("status", "UP");
                health.put("calcite", "CONNECTED");
            } else {
                health.put("status", "DOWN");
                health.put("calcite", "DISCONNECTED");
            }
            
            // 内存检查
            Map<String, Object> memoryStatus = queryExecutor.getMemoryStatus();
            String memoryUsage = (String) memoryStatus.get("usagePercentage");
            double usage = Double.parseDouble(memoryUsage.replace("%", ""));
            
            if (usage > 90) {
                health.put("memory", "CRITICAL");
            } else if (usage > 75) {
                health.put("memory", "WARNING");
            } else {
                health.put("memory", "OK");
            }
            
            health.put("memoryUsage", memoryUsage);
            health.put("timestamp", System.currentTimeMillis());
            
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            log.error("Health check failed", e);
        }
        
        return Result.OK(health);
    }
}
