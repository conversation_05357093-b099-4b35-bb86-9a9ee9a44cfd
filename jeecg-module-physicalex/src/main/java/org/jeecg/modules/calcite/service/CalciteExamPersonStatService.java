package org.jeecg.modules.calcite.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 使用Apache Calcite优化的体检统计服务
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@Slf4j
public class CalciteExamPersonStatService {
    
    @Autowired
    private CalciteQueryExecutor calciteQueryExecutor;
    
    /**
     * 获取不同体检情况人数统计数据 - 分页查询
     *
     * @param startTime 开始时间 (yyyy-MM-dd)
     * @param endTime 结束时间 (yyyy-MM-dd)
     * @param pageNo 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    public Map<String, Object> getExamTotalStat(String startTime, String endTime, Integer pageNo, Integer pageSize) {
        
        long startTimeMs = System.currentTimeMillis();
        log.info("开始执行体检统计分页查询，时间范围：{} - {}，页码：{}，每页：{}",
            startTime, endTime, pageNo, pageSize);

        try {
            // 设置默认分页参数
            if (pageNo == null || pageNo < 1) pageNo = 1;
            if (pageSize == null || pageSize < 1) pageSize = 10;

            // 构建分页SQL查询
            String optimizedSql = buildExamTotalOptimizedSql(pageNo, pageSize);

            // 准备查询参数
            List<Object> params = buildQueryParams(startTime, endTime, pageSize, pageNo);

            // 执行分页查询
            List<Map<String, Object>> results = calciteQueryExecutor.executeQuery(
                optimizedSql,
                params.toArray()
            );

            // 获取总数
            Long totalCount = getTotalCount(startTime, endTime);

            // 后处理结果数据
            List<Map<String, Object>> processedResults = postProcessResults(results);

            // 构建分页结果
            Map<String, Object> pageResult = new HashMap<>();
            pageResult.put("data", processedResults);
            pageResult.put("count", totalCount);
            pageResult.put("size", pageSize);
            pageResult.put("current", pageNo);
            pageResult.put("total", (totalCount + pageSize - 1) / pageSize);

            long duration = System.currentTimeMillis() - startTimeMs;
            log.info("体检统计分页查询完成，耗时：{}ms，返回{}条记录，总数：{}",
                duration, processedResults.size(), totalCount);

            return pageResult;

        } catch (Exception e) {
            log.error("体检统计分页查询执行失败", e);
            throw new RuntimeException("体检统计查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建简化的SQL查询 - 支持分页
     * 避免复杂的CASE语句，防止Calcite生成不兼容的SQL
     */
    private String buildExamTotalOptimizedSql(Integer pageNo, Integer pageSize) {
        return """
            SELECT
                CAST(g.create_time AS DATE) AS create_time,
                COUNT(DISTINCT c.exam_no) AS reg_total
                
            FROM mysql.customer_reg_item_group g
            INNER JOIN mysql.customer_reg c ON g.customer_reg_id = c.id
            WHERE c.del_flag = 0
                AND c.status = ?
                AND (? IS NULL OR g.create_time >= ?)
                AND (? IS NULL OR g.create_time <= ?)
            GROUP BY CAST(g.create_time AS DATE)
            ORDER BY create_time DESC
            LIMIT ?
            OFFSET ?
            """;
    }
    
    /**
     * 后处理查询结果
     * 添加额外的计算字段和格式化
     */
    private List<Map<String, Object>> postProcessResults(List<Map<String, Object>> results) {
        
        List<Map<String, Object>> processedResults = new ArrayList<>();
        
        for (Map<String, Object> row : results) {
            Map<String, Object> processedRow = new HashMap<>(row);

            // 添加默认的统计字段（暂时设为0，后续可以通过多个查询来获取详细数据）
            processedRow.put("personal_reg_total", 0);
            processedRow.put("company_reg_total", 0);
            processedRow.put("start_total", 0);
            processedRow.put("personal_start_total", 0);
            processedRow.put("company_start_total", 0);
            processedRow.put("summary_total", 0);
            processedRow.put("personal_summary_total", 0);
            processedRow.put("company_summary_total", 0);
            processedRow.put("print_total", 0);
            processedRow.put("personal_print_total", 0);
            processedRow.put("company_print_total", 0);
            processedRow.put("take_total", 0);
            processedRow.put("personal_take_total", 0);
            processedRow.put("company_take_total", 0);
            processedRow.put("day_of_week", "");

            // 计算完成率（暂时设为0%，因为我们简化了查询）
            processedRow.put("start_rate", "0.0%");
            processedRow.put("summary_rate", "0.0%");
            processedRow.put("print_rate", "0.0%");
            processedRow.put("take_rate", "0.0%");
            
            // 添加日期格式化
            Object createTimeObj = row.get("create_time");
            if (createTimeObj != null) {
                try {
                    String createTimeStr;
                    if (createTimeObj instanceof java.sql.Date) {
                        // 如果是java.sql.Date，转换为LocalDate再格式化
                        java.sql.Date sqlDate = (java.sql.Date) createTimeObj;
                        LocalDate date = sqlDate.toLocalDate();
                        createTimeStr = date.toString(); // yyyy-MM-dd格式
                        processedRow.put("create_time", createTimeStr);
                        processedRow.put("formatted_date", date.format(DateTimeFormatter.ofPattern("MM月dd日")));
                    } else if (createTimeObj instanceof String) {
                        // 如果是字符串，直接解析
                        createTimeStr = (String) createTimeObj;
                        LocalDate date = LocalDate.parse(createTimeStr);
                        processedRow.put("create_time", createTimeStr);
                        processedRow.put("formatted_date", date.format(DateTimeFormatter.ofPattern("MM月dd日")));
                    } else {
                        // 其他类型，转换为字符串
                        createTimeStr = createTimeObj.toString();
                        processedRow.put("create_time", createTimeStr);
                        processedRow.put("formatted_date", createTimeStr);
                    }
                } catch (Exception e) {
                    // 如果转换失败，使用原始值
                    String fallbackStr = createTimeObj.toString();
                    processedRow.put("create_time", fallbackStr);
                    processedRow.put("formatted_date", fallbackStr);
                }
            } else {
                processedRow.put("create_time", "");
                processedRow.put("formatted_date", "");
            }
            
            processedResults.add(processedRow);
        }
        
        return processedResults;
    }
    
    /**
     * 安全获取整数值
     */
    private Integer getIntValue(Map<String, Object> row, String key) {
        Object value = row.get(key);
        if (value == null) return 0;
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    
    /**
     * 获取汇总统计数据
     */
    public Map<String, Object> getSummaryStatistics(String startTime, String endTime) {
        
        // 获取所有数据用于汇总统计（使用大的页面大小）
        Map<String, Object> pageResult = getExamTotalStat(startTime, endTime, 1, 10000);
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> dailyStats = (List<Map<String, Object>>) pageResult.get("records");
        
        Map<String, Object> summary = new HashMap<>();
        
        int totalReg = 0, totalStart = 0, totalSummary = 0, totalPrint = 0, totalTake = 0;
        int personalReg = 0, personalStart = 0, personalSummary = 0, personalPrint = 0, personalTake = 0;
        int companyReg = 0, companyStart = 0, companySummary = 0, companyPrint = 0, companyTake = 0;
        
        for (Map<String, Object> row : dailyStats) {
            totalReg += getIntValue(row, "reg_total");
            totalStart += getIntValue(row, "start_total");
            totalSummary += getIntValue(row, "summary_total");
            totalPrint += getIntValue(row, "print_total");
            totalTake += getIntValue(row, "take_total");
            
            personalReg += getIntValue(row, "personal_reg_total");
            personalStart += getIntValue(row, "personal_start_total");
            personalSummary += getIntValue(row, "personal_summary_total");
            personalPrint += getIntValue(row, "personal_print_total");
            personalTake += getIntValue(row, "personal_take_total");
            
            companyReg += getIntValue(row, "company_reg_total");
            companyStart += getIntValue(row, "company_start_total");
            companySummary += getIntValue(row, "company_summary_total");
            companyPrint += getIntValue(row, "company_print_total");
            companyTake += getIntValue(row, "company_take_total");
        }
        
        summary.put("total_reg", totalReg);
        summary.put("total_start", totalStart);
        summary.put("total_summary", totalSummary);
        summary.put("total_print", totalPrint);
        summary.put("total_take", totalTake);
        
        summary.put("personal_total", personalReg);
        summary.put("company_total", companyReg);
        
        if (totalReg > 0) {
            summary.put("start_rate", String.format("%.1f%%", (totalStart * 100.0) / totalReg));
            summary.put("summary_rate", String.format("%.1f%%", (totalSummary * 100.0) / totalReg));
            summary.put("print_rate", String.format("%.1f%%", (totalPrint * 100.0) / totalReg));
            summary.put("take_rate", String.format("%.1f%%", (totalTake * 100.0) / totalReg));
        }
        
        summary.put("query_date_range", startTime + " 至 " + endTime);
        summary.put("daily_count", dailyStats.size());
        
        return summary;
    }

    /**
     * 构建查询参数列表
     */
    private List<Object> buildQueryParams(String startTime, String endTime, Integer pageSize, Integer pageNo) {
        List<Object> params = new ArrayList<>();

        // 状态参数
        params.add("已登记");

        // 时间参数（4个）
        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 00:00:00");
            params.add(endTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        // 分页参数
        params.add(pageSize);
        int offset = (pageNo - 1) * pageSize;
        params.add(offset);

        return params;
    }

    /**
     * 获取总数查询
     */
    private Long getTotalCount(String startTime, String endTime) {

        String countSql = """
            SELECT COUNT(DISTINCT CAST(g.create_time AS DATE)) AS total_count
            FROM mysql.customer_reg_item_group g
            INNER JOIN mysql.customer_reg c ON g.customer_reg_id = c.id
            WHERE c.del_flag = 0
                AND c.status = ?
                AND (? IS NULL OR g.create_time >= ?)
                AND (? IS NULL OR g.create_time <= ?)
            """;

        List<Object> params = new ArrayList<>();

        // 状态参数
        params.add("已登记");

        if (startTime != null && !startTime.trim().isEmpty()) {
            params.add(startTime + " 00:00:00");
            params.add(startTime + " 00:00:00");
        } else {
            params.add(null);
            params.add(null);
        }

        if (endTime != null && !endTime.trim().isEmpty()) {
            params.add(endTime + " 23:59:59");
            params.add(endTime + " 23:59:59");
        } else {
            params.add(null);
            params.add(null);
        }

        try {
            List<Map<String, Object>> countResult = calciteQueryExecutor.executeQuery(countSql, params.toArray());
            if (!countResult.isEmpty()) {
                Object totalCount = countResult.get(0).get("total_count");
                if (totalCount instanceof Number) {
                    return ((Number) totalCount).longValue();
                }
            }
            return 0L;
        } catch (Exception e) {
            log.error("获取体检统计总数失败", e);
            return 0L;
        }
    }

    /**
     * 转换星期几数字为中文
     */
    private String convertDayOfWeek(Object dayOfWeekNum) {
        if (dayOfWeekNum == null) {
            return "";
        }

        int dayNum;
        if (dayOfWeekNum instanceof Number) {
            dayNum = ((Number) dayOfWeekNum).intValue();
        } else {
            try {
                dayNum = Integer.parseInt(dayOfWeekNum.toString());
            } catch (NumberFormatException e) {
                return "";
            }
        }

        // Calcite的DAYOFWEEK函数：1=星期日, 2=星期一, ..., 7=星期六
        switch (dayNum) {
            case 1: return "星期日";
            case 2: return "星期一";
            case 3: return "星期二";
            case 4: return "星期三";
            case 5: return "星期四";
            case 6: return "星期五";
            case 7: return "星期六";
            default: return "";
        }
    }
}
