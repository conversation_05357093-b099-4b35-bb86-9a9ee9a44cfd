package org.jeecg.modules.calcite.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.calcite.adapter.jdbc.JdbcSchema;
import org.apache.calcite.schema.Schema;
import org.apache.calcite.schema.SchemaPlus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * Calcite MySQL连接优化器
 * 专门针对亿级数据和2GB内存限制进行优化
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Component
@Slf4j
public class CalciteMySQLOptimizer {

    @Value("${spring.datasource.dynamic.datasource.master.url}")
    private String mysqlUrl;

    @Value("${spring.datasource.dynamic.datasource.master.username}")
    private String mysqlUsername;

    @Value("${spring.datasource.dynamic.datasource.master.password}")
    private String mysqlPassword;
    
    @PostConstruct
    public void init() {
        log.info("Initializing Calcite MySQL optimizer for large dataset processing");
        validateConfiguration();
    }
    
    /**
     * 创建优化的MySQL Schema
     */
    public Schema createOptimizedMySQLSchema(SchemaPlus rootSchema) {

        // 构建优化的JDBC URL
        String optimizedUrl = buildOptimizedJdbcUrl(mysqlUrl);

        Map<String, Object> operand = new HashMap<>();
        operand.put("jdbcUrl", optimizedUrl);
        operand.put("jdbcUser", mysqlUsername);
        operand.put("jdbcPassword", mysqlPassword);
        operand.put("jdbcDriver", "com.mysql.cj.jdbc.Driver");

        // 连接池优化配置
        configureConnectionPool(operand);

        // 查询优化配置
        configureQueryOptimization(operand);

        Schema mysqlSchema = JdbcSchema.create(rootSchema, "mysql", operand);

        log.info("Optimized MySQL schema created with URL: {}", maskPassword(optimizedUrl));

        return mysqlSchema;
    }
    
    /**
     * 构建优化的JDBC URL
     */
    private String buildOptimizedJdbcUrl(String baseUrl) {
        
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        
        // 如果URL中没有参数，添加?
        if (!baseUrl.contains("?")) {
            urlBuilder.append("?");
        } else if (!baseUrl.endsWith("&")) {
            urlBuilder.append("&");
        }
        
        // 批处理优化
        urlBuilder.append("rewriteBatchedStatements=true&");
        urlBuilder.append("allowMultiQueries=true&");
        urlBuilder.append("useServerPrepStmts=true&");
        
        // 缓存优化
        urlBuilder.append("cachePrepStmts=true&");
        urlBuilder.append("prepStmtCacheSize=250&");
        urlBuilder.append("prepStmtCacheSqlLimit=2048&");
        urlBuilder.append("cacheCallableStmts=true&");
        urlBuilder.append("cacheServerConfiguration=true&");
        urlBuilder.append("cacheResultSetMetadata=true&");
        
        // 性能优化
        urlBuilder.append("useLocalSessionState=true&");
        urlBuilder.append("elideSetAutoCommits=true&");
        urlBuilder.append("alwaysSendSetIsolation=false&");
        urlBuilder.append("enableQueryTimeouts=false&");
        urlBuilder.append("useInformationSchema=true&");
        urlBuilder.append("maintainTimeStats=false&");
        urlBuilder.append("generateSimpleParameterMetadata=true&");
        
        // 网络优化
        urlBuilder.append("useReadAheadInput=false&");
        urlBuilder.append("useUnbufferedInput=false&");
        urlBuilder.append("useStreamLengthsInPrepStmts=true&");
        urlBuilder.append("autoClosePStmtStreams=true&");
        
        // 大数据量优化
        urlBuilder.append("useCursorFetch=true&");
        urlBuilder.append("defaultFetchSize=1000&");
        urlBuilder.append("useDirectRowUnpack=true&");
        urlBuilder.append("largeRowSizeThreshold=2048&");
        
        // 内存优化
        urlBuilder.append("useBlobToStoreUTF8OutsideBMP=false&");
        urlBuilder.append("useJDBCCompliantTimezoneShift=true&");
        urlBuilder.append("useLegacyDatetimeCode=false&");
        urlBuilder.append("sendFractionalSeconds=false&");
        urlBuilder.append("treatUtilDateAsTimestamp=false&");
        
        // 解析优化
        urlBuilder.append("useFastDateParsing=true&");
        urlBuilder.append("useFastIntParsing=true&");
        urlBuilder.append("dumpQueriesOnException=false&");
        
        // 安全优化
        urlBuilder.append("connectionAttributes=none&");
        urlBuilder.append("verifyServerCertificate=false&");
        
        // 移除最后的&
        String result = urlBuilder.toString();
        if (result.endsWith("&")) {
            result = result.substring(0, result.length() - 1);
        }
        
        return result;
    }
    
    /**
     * 配置连接池
     */
    private void configureConnectionPool(Map<String, Object> operand) {

        // 连接池大小配置
        operand.put("maxPoolSize", 20);
        operand.put("minPoolSize", 5);
        operand.put("initialSize", 5);

        // 超时配置
        operand.put("connectionTimeout", 60000); // 60秒
        operand.put("idleTimeout", 600000); // 10分钟
        operand.put("maxLifetime", 1800000); // 30分钟
        operand.put("validationTimeout", 5000); // 5秒

        // 连接验证
        operand.put("testWhileIdle", true);
        operand.put("testOnBorrow", false);
        operand.put("testOnReturn", false);
        operand.put("validationQuery", "SELECT 1");
        operand.put("validationInterval", 30000);

        // 连接泄漏检测
        operand.put("leakDetectionThreshold", 60000);

        log.debug("Connection pool configured: maxPoolSize=20, minPoolSize=5, connectionTimeout=60s");
    }
    
    /**
     * 配置查询优化
     */
    private void configureQueryOptimization(Map<String, Object> operand) {

        // 查询超时
        operand.put("queryTimeout", 600); // 10分钟
        operand.put("socketTimeout", 300000); // 5分钟

        // 获取大小优化
        operand.put("defaultFetchSize", 1000);
        operand.put("defaultRowPrefetch", 100);

        // 预处理语句优化
        operand.put("useServerPrepStmts", true);
        operand.put("cachePrepStmts", true);
        operand.put("prepStmtCacheSize", 250);
        operand.put("prepStmtCacheSqlLimit", 2048);

        // 批处理优化
        operand.put("rewriteBatchedStatements", true);
        operand.put("useLocalSessionState", true);
        operand.put("elideSetAutoCommits", true);

        log.debug("Query optimization configured: queryTimeout=600s, fetchSize=1000, usePrepStmts=true");
    }
    

    
    /**
     * 验证配置
     */
    private void validateConfiguration() {

        // 验证连接配置
        if (mysqlUrl == null || mysqlUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("MySQL URL cannot be null or empty");
        }

        if (mysqlUsername == null || mysqlUsername.trim().isEmpty()) {
            throw new IllegalArgumentException("MySQL username cannot be null or empty");
        }

        log.info("Configuration validation passed");
    }
    
    /**
     * 掩码密码用于日志输出
     */
    private String maskPassword(String url) {
        if (url == null) return null;
        return url.replaceAll("password=[^&]*", "password=***");
    }
    
    /**
     * 获取优化建议
     */
    public String getOptimizationRecommendations() {

        StringBuilder recommendations = new StringBuilder();
        recommendations.append("=== Calcite MySQL Optimization Recommendations ===\n");
        recommendations.append("- Connection pool configured with 20 max connections\n");
        recommendations.append("- Query timeout set to 600 seconds for complex queries\n");
        recommendations.append("- Batch processing enabled with fetch size 1000\n");
        recommendations.append("- PreparedStatement caching enabled\n");
        recommendations.append("- Memory usage will be monitored during query execution\n");
        recommendations.append("=== End of Recommendations ===");

        return recommendations.toString();
    }
}
