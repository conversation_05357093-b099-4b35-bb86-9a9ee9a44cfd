package org.jeecg.modules.formrule.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.formrule.dto.FormRuleConfigurationDTO;
import org.jeecg.modules.formrule.entity.FormFieldMetadata;
import org.jeecg.modules.formrule.entity.FormRuleConfig;
import org.jeecg.modules.formrule.mapper.FormFieldMetadataMapper;
import org.jeecg.modules.formrule.mapper.FormRuleConfigMapper;
import org.jeecg.modules.formrule.service.IFormRuleConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Arrays;

/**
 * 表单规则配置服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@Slf4j
public class FormRuleConfigurationServiceImpl implements IFormRuleConfigurationService {

    @Autowired
    private FormRuleConfigMapper formRuleConfigMapper;

    @Autowired
    private FormFieldMetadataMapper formFieldMetadataMapper;

    @Override
    public FormRuleConfigurationDTO getFormConfiguration(String formCode) {
        try {
            // 获取表单规则配置
            QueryWrapper<FormRuleConfig> wrapper = new QueryWrapper<>();
            wrapper.eq("form_code", formCode);
            FormRuleConfig config = formRuleConfigMapper.selectOne(wrapper);

            if (config == null) {
                return null;
            }

            // 获取字段元数据
            List<FormFieldMetadata> fieldMetadataList = formFieldMetadataMapper.getFieldsByFormCode(formCode);

            // 构建配置DTO
            FormRuleConfigurationDTO dto = new FormRuleConfigurationDTO();

            // 设置表单基本信息
            FormRuleConfigurationDTO.FormBasicInfo formInfo = new FormRuleConfigurationDTO.FormBasicInfo()
                    .setFormCode(config.getFormCode())
                    .setFormName(config.getFormName())
                    .setDescription(config.getDescription())
                    .setVersion(config.getVersion())
                    .setStatus(config.getStatus());
            dto.setFormInfo(formInfo);

            // 解析字段规则
            List<FormRuleConfigurationDTO.FieldConfigInfo> fields = parseFieldRules(config.getFieldRules(), fieldMetadataList);
            dto.setFields(fields);

            // 解析联动规则
            List<FormRuleConfigurationDTO.DependencyRuleInfo> dependencies = parseDependencyRules(config.getDependencyRules());
            dto.setDependencies(dependencies);

            return dto;
        } catch (Exception e) {
            log.error("获取表单配置失败: formCode={}", formCode, e);
            return null;
        }
    }

    @Override
    @Transactional
    public boolean saveFormConfiguration(FormRuleConfigurationDTO configuration) {
        try {
            String formCode = configuration.getFormInfo().getFormCode();

            // 保存字段元数据
            if (configuration.getFields() != null) {
                saveFieldMetadata(formCode, configuration.getFields());
            }

            // 构建字段规则JSON
            String fieldRulesJson = buildFieldRulesJson(configuration.getFields());

            // 构建联动规则JSON
            String dependencyRulesJson = buildDependencyRulesJson(configuration.getDependencies());

            // 保存或更新表单规则配置
            QueryWrapper<FormRuleConfig> wrapper = new QueryWrapper<>();
            wrapper.eq("form_code", formCode);
            FormRuleConfig existingConfig = formRuleConfigMapper.selectOne(wrapper);

            if (existingConfig != null) {
                // 更新
                existingConfig.setFormName(configuration.getFormInfo().getFormName());
                existingConfig.setDescription(configuration.getFormInfo().getDescription());
                existingConfig.setStatus(configuration.getFormInfo().getStatus());
                existingConfig.setFieldRules(fieldRulesJson);
                existingConfig.setDependencyRules(dependencyRulesJson);
                existingConfig.setVersion(existingConfig.getVersion() + 1);
                formRuleConfigMapper.updateById(existingConfig);
            } else {
                // 新增
                FormRuleConfig newConfig = new FormRuleConfig();
                newConfig.setFormCode(formCode);
                newConfig.setFormName(configuration.getFormInfo().getFormName());
                newConfig.setDescription(configuration.getFormInfo().getDescription());
                newConfig.setStatus(configuration.getFormInfo().getStatus());
                newConfig.setFieldRules(fieldRulesJson);
                newConfig.setDependencyRules(dependencyRulesJson);
                newConfig.setVersion(1);
                formRuleConfigMapper.insert(newConfig);
            }

            return true;
        } catch (Exception e) {
            log.error("保存表单配置失败", e);
            return false;
        }
    }

    @Override
    public List<FormFieldMetadata> getFormFields(String formCode) {
        return formFieldMetadataMapper.getFieldsByFormCode(formCode);
    }

    @Override
    @Transactional
    public boolean saveFormFields(String formCode, List<FormFieldMetadata> fields) {
        try {
            // 删除现有字段
            QueryWrapper<FormFieldMetadata> wrapper = new QueryWrapper<>();
            wrapper.eq("form_code", formCode);
            formFieldMetadataMapper.delete(wrapper);

            // 插入新字段
            for (FormFieldMetadata field : fields) {
                field.setFormCode(formCode);
                formFieldMetadataMapper.insert(field);
            }

            return true;
        } catch (Exception e) {
            log.error("保存表单字段失败: formCode={}", formCode, e);
            return false;
        }
    }

    @Override
    public List<FormFieldMetadata> generateFieldsFromTable(String formCode, String tableName) {
        try {
            log.info("从数据库表生成字段元数据: formCode={}, tableName={}", formCode, tableName);

            // 使用原生SQL查询表结构信息
            List<Map<String, Object>> columns = formRuleConfigMapper.selectTableColumns(tableName);

            List<FormFieldMetadata> fields = new ArrayList<>();
            int sortOrder = 1;

            for (Map<String, Object> column : columns) {
                String columnName = (String) column.get("columnName");
                String columnComment = (String) column.get("columnComment");
                String dataType = (String) column.get("dataType");
                Integer maxLength = (Integer) column.get("maxLength");
                String isNullable = (String) column.get("isNullable");
                String defaultValue = (String) column.get("defaultValue");

                // 跳过系统字段
                if (isSystemField(columnName)) {
                    continue;
                }

                FormFieldMetadata field = new FormFieldMetadata();
                field.setFormCode(formCode);
                field.setFieldCode(columnName);
                field.setFieldName(StringUtils.isNotBlank(columnComment) ? columnComment : columnName);
                field.setFieldType(mapDataTypeToFieldType(dataType));
                field.setDataType(dataType);
                field.setMaxLength(maxLength);
                field.setIsNullable("YES".equals(isNullable) ? 1 : 0);
                field.setDefaultValue(defaultValue);
                field.setSortOrder(sortOrder++);
                field.setDescription("从数据表 " + tableName + " 自动生成");

                // 为特定字段类型生成选项数据
                if ("enum".equals(dataType) || isCommonSelectField(columnName)) {
                    field.setOptionsData(generateOptionsForField(columnName, dataType));
                }

                fields.add(field);
            }

            log.info("从数据库表生成字段完成: formCode={}, tableName={}, fieldCount={}",
                    formCode, tableName, fields.size());
            return fields;

        } catch (Exception e) {
            log.error("从数据库表生成字段失败: formCode={}, tableName={}", formCode, tableName, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<FormFieldMetadata> generateFieldsFromEntity(String formCode, String entityClass) {
        // TODO: 实现从实体类生成字段元数据的逻辑
        // 这里需要通过反射解析实体类，生成对应的字段元数据
        log.info("从实体类生成字段元数据: formCode={}, entityClass={}", formCode, entityClass);
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getValidationRuleTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        
        // 基础验证规则
        types.add(createRuleType("required", "必填验证", "检查字段是否为空"));
        types.add(createRuleType("minLength", "最小长度", "检查字符串最小长度"));
        types.add(createRuleType("maxLength", "最大长度", "检查字符串最大长度"));
        types.add(createRuleType("pattern", "正则表达式", "使用正则表达式验证"));
        types.add(createRuleType("email", "邮箱格式", "验证邮箱格式"));
        types.add(createRuleType("phone", "手机号格式", "验证手机号格式"));
        types.add(createRuleType("idCard", "身份证格式", "验证身份证格式"));
        types.add(createRuleType("number", "数字验证", "验证是否为数字"));
        types.add(createRuleType("min", "最小值", "验证数字最小值"));
        types.add(createRuleType("max", "最大值", "验证数字最大值"));
        types.add(createRuleType("date", "日期格式", "验证日期格式"));
        types.add(createRuleType("dateRange", "日期范围", "验证日期范围"));
        
        return types;
    }

    @Override
    public List<Map<String, Object>> getDependencyRuleTypes() {
        List<Map<String, Object>> types = new ArrayList<>();
        
        // 联动规则类型
        types.add(createRuleType("visible", "可见性联动", "控制字段显示/隐藏"));
        types.add(createRuleType("required", "必填联动", "控制字段必填状态"));
        types.add(createRuleType("disabled", "禁用联动", "控制字段禁用状态"));
        types.add(createRuleType("options", "选项联动", "控制下拉选项内容"));
        types.add(createRuleType("value", "值联动", "控制字段默认值"));
        
        return types;
    }

    @Override
    public Map<String, Object> previewFormRules(String formCode, Map<String, Object> testData) {
        // TODO: 实现表单规则预览功能
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "预览功能待实现");
        result.put("data", testData);
        return result;
    }

    @Override
    public boolean copyFormConfiguration(String sourceFormCode, String targetFormCode, String targetFormName) {
        try {
            // 获取源配置
            FormRuleConfigurationDTO sourceConfig = getFormConfiguration(sourceFormCode);
            if (sourceConfig == null) {
                return false;
            }

            // 修改为目标配置
            sourceConfig.getFormInfo().setFormCode(targetFormCode);
            sourceConfig.getFormInfo().setFormName(targetFormName);
            sourceConfig.getFormInfo().setVersion(1);

            // 保存目标配置
            return saveFormConfiguration(sourceConfig);
        } catch (Exception e) {
            log.error("复制表单配置失败: source={}, target={}", sourceFormCode, targetFormCode, e);
            return false;
        }
    }

    @Override
    public String exportFormConfiguration(String formCode) {
        try {
            FormRuleConfigurationDTO config = getFormConfiguration(formCode);
            return JSON.toJSONString(config, true);
        } catch (Exception e) {
            log.error("导出表单配置失败: formCode={}", formCode, e);
            return null;
        }
    }

    @Override
    public boolean importFormConfiguration(String formCode, String configJson) {
        try {
            FormRuleConfigurationDTO config = JSON.parseObject(configJson, FormRuleConfigurationDTO.class);
            config.getFormInfo().setFormCode(formCode);
            return saveFormConfiguration(config);
        } catch (Exception e) {
            log.error("导入表单配置失败: formCode={}", formCode, e);
            return false;
        }
    }

    // 私有辅助方法

    private List<FormRuleConfigurationDTO.FieldConfigInfo> parseFieldRules(String fieldRulesJson, List<FormFieldMetadata> fieldMetadataList) {
        List<FormRuleConfigurationDTO.FieldConfigInfo> fields = new ArrayList<>();
        
        if (StringUtils.isBlank(fieldRulesJson)) {
            return fields;
        }

        try {
            JSONArray fieldRulesArray = JSON.parseArray(fieldRulesJson);
            Map<String, FormFieldMetadata> metadataMap = fieldMetadataList.stream()
                    .collect(Collectors.toMap(FormFieldMetadata::getFieldCode, f -> f));

            for (int i = 0; i < fieldRulesArray.size(); i++) {
                JSONObject fieldRule = fieldRulesArray.getJSONObject(i);
                String fieldCode = fieldRule.getString("fieldCode");
                FormFieldMetadata metadata = metadataMap.get(fieldCode);

                FormRuleConfigurationDTO.FieldConfigInfo fieldInfo = new FormRuleConfigurationDTO.FieldConfigInfo();
                fieldInfo.setFieldCode(fieldCode);
                fieldInfo.setFieldName(fieldRule.getString("fieldName"));
                fieldInfo.setRequired(fieldRule.getBoolean("isRequired"));
                fieldInfo.setRequiredMessage(fieldRule.getString("requiredMessage"));
                fieldInfo.setVisible(fieldRule.getBoolean("visible"));
                fieldInfo.setDisabled(fieldRule.getBoolean("disabled"));
                fieldInfo.setSortOrder(fieldRule.getInteger("sortOrder"));

                if (metadata != null) {
                    fieldInfo.setFieldType(metadata.getFieldType());
                    fieldInfo.setDataType(metadata.getDataType());
                    fieldInfo.setMaxLength(metadata.getMaxLength());
                    fieldInfo.setNullable(metadata.getIsNullable() == 1);
                    fieldInfo.setDefaultValue(metadata.getDefaultValue());
                    fieldInfo.setDescription(metadata.getDescription());
                    
                    // 解析选项数据
                    if (StringUtils.isNotBlank(metadata.getOptionsData())) {
                        JSONArray optionsArray = JSON.parseArray(metadata.getOptionsData());
                        List<FormRuleConfigurationDTO.OptionItem> options = new ArrayList<>();
                        for (int j = 0; j < optionsArray.size(); j++) {
                            JSONObject option = optionsArray.getJSONObject(j);
                            FormRuleConfigurationDTO.OptionItem item = new FormRuleConfigurationDTO.OptionItem()
                                    .setValue(option.get("value"))
                                    .setLabel(option.getString("label"))
                                    .setDisabled(option.getBoolean("disabled"));
                            options.add(item);
                        }
                        fieldInfo.setOptions(options);
                    }
                }

                // 解析验证规则
                JSONArray validationRulesArray = fieldRule.getJSONArray("validationRules");
                if (validationRulesArray != null) {
                    List<FormRuleConfigurationDTO.ValidationRule> validationRules = new ArrayList<>();
                    for (int j = 0; j < validationRulesArray.size(); j++) {
                        JSONObject validationRule = validationRulesArray.getJSONObject(j);
                        FormRuleConfigurationDTO.ValidationRule rule = new FormRuleConfigurationDTO.ValidationRule()
                                .setType(validationRule.getString("type"))
                                .setValue(validationRule.get("value"))
                                .setMessage(validationRule.getString("message"))
                                .setEnabled(true);
                        validationRules.add(rule);
                    }
                    fieldInfo.setValidationRules(validationRules);
                }

                fields.add(fieldInfo);
            }
        } catch (Exception e) {
            log.error("解析字段规则失败", e);
        }

        return fields;
    }

    private List<FormRuleConfigurationDTO.DependencyRuleInfo> parseDependencyRules(String dependencyRulesJson) {
        List<FormRuleConfigurationDTO.DependencyRuleInfo> dependencies = new ArrayList<>();
        
        if (StringUtils.isBlank(dependencyRulesJson)) {
            return dependencies;
        }

        try {
            JSONArray dependencyRulesArray = JSON.parseArray(dependencyRulesJson);
            for (int i = 0; i < dependencyRulesArray.size(); i++) {
                JSONObject dependencyRule = dependencyRulesArray.getJSONObject(i);
                FormRuleConfigurationDTO.DependencyRuleInfo dependencyInfo = new FormRuleConfigurationDTO.DependencyRuleInfo()
                        .setId(dependencyRule.getString("id"))
                        .setSourceField(dependencyRule.getString("sourceField"))
                        .setTargetField(dependencyRule.getString("targetField"))
                        .setDependencyType(dependencyRule.getString("dependencyType"))
                        .setConditionType(dependencyRule.getString("conditionType"))
                        .setConditionValue(dependencyRule.get("conditionValue"))
                        .setActionValue(dependencyRule.get("actionValue"))
                        .setPriority(dependencyRule.getInteger("priority"))
                        .setEnabled(true);
                dependencies.add(dependencyInfo);
            }
        } catch (Exception e) {
            log.error("解析联动规则失败", e);
        }

        return dependencies;
    }

    private void saveFieldMetadata(String formCode, List<FormRuleConfigurationDTO.FieldConfigInfo> fields) {
        // 删除现有字段元数据
        QueryWrapper<FormFieldMetadata> wrapper = new QueryWrapper<>();
        wrapper.eq("form_code", formCode);
        formFieldMetadataMapper.delete(wrapper);

        // 保存新的字段元数据
        for (FormRuleConfigurationDTO.FieldConfigInfo field : fields) {
            FormFieldMetadata metadata = new FormFieldMetadata();
            metadata.setFormCode(formCode);
            metadata.setFieldCode(field.getFieldCode());
            metadata.setFieldName(field.getFieldName());
            metadata.setFieldType(field.getFieldType());
            metadata.setDataType(field.getDataType());
            metadata.setMaxLength(field.getMaxLength());
            metadata.setIsNullable(field.getNullable() != null && field.getNullable() ? 1 : 0);
            metadata.setDefaultValue(field.getDefaultValue());
            metadata.setSortOrder(field.getSortOrder());
            metadata.setDescription(field.getDescription());

            // 保存选项数据
            if (field.getOptions() != null && !field.getOptions().isEmpty()) {
                JSONArray optionsArray = new JSONArray();
                for (FormRuleConfigurationDTO.OptionItem option : field.getOptions()) {
                    JSONObject optionObj = new JSONObject();
                    optionObj.put("value", option.getValue());
                    optionObj.put("label", option.getLabel());
                    optionObj.put("disabled", option.getDisabled());
                    optionsArray.add(optionObj);
                }
                metadata.setOptionsData(optionsArray.toJSONString());
            }

            formFieldMetadataMapper.insert(metadata);
        }
    }

    private String buildFieldRulesJson(List<FormRuleConfigurationDTO.FieldConfigInfo> fields) {
        if (fields == null || fields.isEmpty()) {
            return "[]";
        }

        JSONArray fieldRulesArray = new JSONArray();
        for (FormRuleConfigurationDTO.FieldConfigInfo field : fields) {
            JSONObject fieldRule = new JSONObject();
            fieldRule.put("fieldCode", field.getFieldCode());
            fieldRule.put("fieldName", field.getFieldName());
            fieldRule.put("isRequired", field.getRequired());
            fieldRule.put("requiredMessage", field.getRequiredMessage());
            fieldRule.put("visible", field.getVisible());
            fieldRule.put("disabled", field.getDisabled());
            fieldRule.put("sortOrder", field.getSortOrder());

            // 添加验证规则
            if (field.getValidationRules() != null && !field.getValidationRules().isEmpty()) {
                JSONArray validationRulesArray = new JSONArray();
                for (FormRuleConfigurationDTO.ValidationRule rule : field.getValidationRules()) {
                    if (rule.getEnabled() != null && rule.getEnabled()) {
                        JSONObject validationRule = new JSONObject();
                        validationRule.put("type", rule.getType());
                        validationRule.put("value", rule.getValue());
                        validationRule.put("message", rule.getMessage());
                        validationRulesArray.add(validationRule);
                    }
                }
                fieldRule.put("validationRules", validationRulesArray);
            }

            fieldRulesArray.add(fieldRule);
        }

        return fieldRulesArray.toJSONString();
    }

    private String buildDependencyRulesJson(List<FormRuleConfigurationDTO.DependencyRuleInfo> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return "[]";
        }

        JSONArray dependencyRulesArray = new JSONArray();
        for (FormRuleConfigurationDTO.DependencyRuleInfo dependency : dependencies) {
            if (dependency.getEnabled() != null && dependency.getEnabled()) {
                JSONObject dependencyRule = new JSONObject();
                dependencyRule.put("id", dependency.getId());
                dependencyRule.put("sourceField", dependency.getSourceField());
                dependencyRule.put("targetField", dependency.getTargetField());
                dependencyRule.put("dependencyType", dependency.getDependencyType());
                dependencyRule.put("conditionType", dependency.getConditionType());
                dependencyRule.put("conditionValue", dependency.getConditionValue());
                dependencyRule.put("actionValue", dependency.getActionValue());
                dependencyRule.put("priority", dependency.getPriority());
                dependencyRulesArray.add(dependencyRule);
            }
        }

        return dependencyRulesArray.toJSONString();
    }

    private Map<String, Object> createRuleType(String type, String name, String description) {
        Map<String, Object> ruleType = new HashMap<>();
        ruleType.put("type", type);
        ruleType.put("name", name);
        ruleType.put("description", description);
        return ruleType;
    }

    /**
     * 判断是否为系统字段
     */
    private boolean isSystemField(String columnName) {
        String[] systemFields = {
                "id", "create_by", "create_time", "update_by", "update_time",
                "del_flag", "version", "tenant_id"
        };
        return Arrays.asList(systemFields).contains(columnName.toLowerCase());
    }

    /**
     * 将数据库数据类型映射为前端字段类型
     */
    private String mapDataTypeToFieldType(String dataType) {
        if (dataType == null) {
            return "string";
        }

        dataType = dataType.toLowerCase();

        // 文本类型
        if (dataType.contains("varchar") || dataType.contains("char")) {
            return "string";
        }
        // 长文本类型
        if (dataType.contains("text")) {
            return "textarea";
        }
        // 数字类型
        if (dataType.contains("int") || dataType.contains("decimal") ||
            dataType.contains("float") || dataType.contains("double")) {
            return "number";
        }
        // 日期类型
        if (dataType.contains("date")) {
            return "date";
        }
        // 日期时间类型
        if (dataType.contains("datetime") || dataType.contains("timestamp")) {
            return "datetime";
        }
        // 布尔类型
        if (dataType.contains("tinyint") && dataType.contains("(1)")) {
            return "switch";
        }
        // 枚举类型
        if (dataType.contains("enum")) {
            return "select";
        }

        return "string";
    }

    /**
     * 判断是否为常见的选择字段
     */
    private boolean isCommonSelectField(String columnName) {
        String[] selectFields = {
                "status", "type", "category", "gender", "sex", "state",
                "level", "grade", "priority", "source", "channel"
        };
        String lowerColumnName = columnName.toLowerCase();
        return Arrays.stream(selectFields).anyMatch(lowerColumnName::contains);
    }

    /**
     * 为字段生成选项数据
     */
    private String generateOptionsForField(String columnName, String dataType) {
        JSONArray options = new JSONArray();

        String lowerColumnName = columnName.toLowerCase();

        // 性别字段
        if (lowerColumnName.contains("gender") || lowerColumnName.contains("sex")) {
            options.add(createOption("男", "男"));
            options.add(createOption("女", "女"));
        }
        // 状态字段
        else if (lowerColumnName.contains("status") || lowerColumnName.contains("state")) {
            options.add(createOption(1, "启用"));
            options.add(createOption(0, "禁用"));
        }
        // 类型字段
        else if (lowerColumnName.contains("type") || lowerColumnName.contains("category")) {
            options.add(createOption("type1", "类型1"));
            options.add(createOption("type2", "类型2"));
            options.add(createOption("type3", "类型3"));
        }
        // 优先级字段
        else if (lowerColumnName.contains("priority") || lowerColumnName.contains("level")) {
            options.add(createOption("high", "高"));
            options.add(createOption("medium", "中"));
            options.add(createOption("low", "低"));
        }

        return options.isEmpty() ? null : options.toJSONString();
    }

    /**
     * 创建选项对象
     */
    private JSONObject createOption(Object value, String label) {
        JSONObject option = new JSONObject();
        option.put("value", value);
        option.put("label", label);
        option.put("disabled", false);
        return option;
    }
}
