package org.jeecg.modules.formrule.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.modules.formrule.dto.*;
import org.jeecg.modules.formrule.entity.FormRuleConfig;
import org.jeecg.modules.formrule.entity.FormRuleCacheVersion;
import org.jeecg.modules.formrule.mapper.FormRuleConfigMapper;
import org.jeecg.modules.formrule.service.IFormRuleService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 表单规则配置服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
@Slf4j
public class FormRuleServiceImpl extends ServiceImpl<FormRuleConfigMapper, FormRuleConfig> 
        implements IFormRuleService {

    @Autowired
    private FormRuleConfigMapper formRuleConfigMapper;
    
    @Autowired
    private FormRuleCacheService formRuleCacheService;

    @Autowired
    private org.jeecg.modules.formrule.mapper.FormFieldMetadataMapper formFieldMetadataMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CACHE_KEY_PREFIX = "form_rule:";
    private static final String VERSION_KEY_PREFIX = "form_rule_version:";

    @Override
    public FormRuleConfigDTO getByFormCode(String formCode) {
        // 1. 先从缓存获取
        FormRuleConfigDTO cached = formRuleCacheService.getFromCache(formCode);
        if (cached != null) {
            return cached;
        }

        // 2. 从数据库获取
        LambdaQueryWrapper<FormRuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FormRuleConfig::getFormCode, formCode)
                   .eq(FormRuleConfig::getStatus, 1)
                   .orderByDesc(FormRuleConfig::getVersion)
                   .last("LIMIT 1");
        
        FormRuleConfig entity = this.getOne(queryWrapper);
        if (entity == null) {
            return null;
        }

        // 3. 转换为DTO
        FormRuleConfigDTO dto = convertToDTO(entity);
        
        // 4. 放入缓存
        formRuleCacheService.putToCache(formCode, dto);
        
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateFormRule(FormRuleConfigDTO dto) {
        try {
            FormRuleConfig entity;
            boolean isUpdate = false;
            
            if (StringUtils.isNotBlank(dto.getId())) {
                // 更新操作
                entity = this.getById(dto.getId());
                if (entity == null) {
                    throw new JeecgBootException("表单规则配置不存在");
                }
                isUpdate = true;
            } else {
                // 新增操作
                entity = new FormRuleConfig();
                entity.setCreateTime(new Date());
                entity.setCreateBy(getCurrentUsername());
            }

            // 设置基本信息
            entity.setFormCode(dto.getFormCode())
                  .setFormName(dto.getFormName())
                  .setDescription(dto.getDescription())
                  .setStatus(dto.getStatus() != null ? dto.getStatus() : 1)
                  .setUpdateTime(new Date())
                  .setUpdateBy(getCurrentUsername());

            // 转换规则为JSON
            entity.setFieldRules(JSON.toJSONString(dto.getFieldRules()));
            entity.setDependencyRules(JSON.toJSONString(dto.getDependencyRules()));

            // 处理版本号
            if (isUpdate) {
                // 更新时递增版本号
                Integer newVersion = formRuleConfigMapper.incrementVersion(dto.getFormCode());
                entity.setVersion(newVersion);
                dto.setVersion(newVersion);
            } else {
                entity.setVersion(1);
                dto.setVersion(1);
            }

            // 保存到数据库
            boolean result = this.saveOrUpdate(entity);
            
            if (result) {
                // 简单刷新缓存 - 删除旧缓存，下次访问时重新加载
                formRuleCacheService.refreshCache(dto.getFormCode());

                log.info("表单规则配置保存成功: formCode={}, version={}",
                        dto.getFormCode(), dto.getVersion());
            }
            
            return result;
        } catch (Exception e) {
            log.error("保存表单规则配置失败: formCode={}", dto.getFormCode(), e);
            throw new JeecgBootException("保存表单规则配置失败: " + e.getMessage());
        }
    }

    @Override
    public FormRuleVersionDTO getVersionInfo(String formCode) {
        // 先从缓存获取版本信息
        String versionKey = VERSION_KEY_PREFIX + formCode;
        Object cached = redisTemplate.opsForValue().get(versionKey);
        
        if (cached != null) {
            return JSON.parseObject(cached.toString(), FormRuleVersionDTO.class);
        }

        // 从数据库获取
        FormRuleCacheVersion versionEntity = formRuleConfigMapper.getVersionByFormCode(formCode);
        if (versionEntity == null) {
            return null;
        }

        FormRuleVersionDTO dto = new FormRuleVersionDTO()
                .setFormCode(versionEntity.getFormCode())
                .setVersion(versionEntity.getVersion())
                .setLastUpdated(versionEntity.getLastUpdated())
                .setUpdateTime(versionEntity.getUpdateTime());

        // 缓存版本信息
        redisTemplate.opsForValue().set(versionKey, JSON.toJSONString(dto), 
                Duration.ofHours(1));

        return dto;
    }

    @Override
    public Map<String, Integer> getVersionInfoBatch(List<String> formCodes) {
        Map<String, Integer> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(formCodes)) {
            return result;
        }

        // 批量从数据库获取版本信息
        List<FormRuleCacheVersion> versions = formRuleConfigMapper.getVersionsByFormCodes(formCodes);
        
        for (FormRuleCacheVersion version : versions) {
            result.put(version.getFormCode(), version.getVersion());
        }

        return result;
    }

    @Override
    public IPage<FormRuleConfigDTO> queryPage(IPage<FormRuleConfig> page, FormRuleQueryDTO queryDTO) {
        LambdaQueryWrapper<FormRuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotBlank(queryDTO.getFormCode())) {
            queryWrapper.like(FormRuleConfig::getFormCode, queryDTO.getFormCode());
        }
        if (StringUtils.isNotBlank(queryDTO.getFormName())) {
            queryWrapper.like(FormRuleConfig::getFormName, queryDTO.getFormName());
        }
        if (queryDTO.getStatus() != null) {
            queryWrapper.eq(FormRuleConfig::getStatus, queryDTO.getStatus());
        }
        if (StringUtils.isNotBlank(queryDTO.getCreateBy())) {
            queryWrapper.like(FormRuleConfig::getCreateBy, queryDTO.getCreateBy());
        }
        if (queryDTO.getCreateTimeStart() != null) {
            queryWrapper.ge(FormRuleConfig::getCreateTime, queryDTO.getCreateTimeStart());
        }
        if (queryDTO.getCreateTimeEnd() != null) {
            queryWrapper.le(FormRuleConfig::getCreateTime, queryDTO.getCreateTimeEnd());
        }

        queryWrapper.orderByDesc(FormRuleConfig::getUpdateTime);

        IPage<FormRuleConfig> entityPage = this.page(page, queryWrapper);
        
        // 转换为DTO分页结果
        IPage<FormRuleConfigDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(entityPage, dtoPage);
        
        List<FormRuleConfigDTO> dtoList = entityPage.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
        dtoPage.setRecords(dtoList);

        return dtoPage;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(String formCode, Integer status) {
        LambdaUpdateWrapper<FormRuleConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FormRuleConfig::getFormCode, formCode)
                   .set(FormRuleConfig::getStatus, status)
                   .set(FormRuleConfig::getUpdateTime, new Date())
                   .set(FormRuleConfig::getUpdateBy, getCurrentUsername());

        boolean result = this.update(updateWrapper);
        
        if (result) {
            // 简单刷新缓存
            formRuleCacheService.refreshCache(formCode);

            log.info("表单规则状态更新成功: formCode={}, status={}", formCode, status);
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByFormCode(String formCode) {
        LambdaUpdateWrapper<FormRuleConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(FormRuleConfig::getFormCode, formCode)
                   .set(FormRuleConfig::getDelFlag, 1)
                   .set(FormRuleConfig::getUpdateTime, new Date())
                   .set(FormRuleConfig::getUpdateBy, getCurrentUsername());

        boolean result = this.update(updateWrapper);
        
        if (result) {
            // 简单刷新缓存
            formRuleCacheService.refreshCache(formCode);

            log.info("表单规则删除成功: formCode={}", formCode);
        }
        
        return result;
    }

    @Override
    public boolean copyFormRule(String sourceFormCode, String targetFormCode, String targetFormName) {
        FormRuleConfigDTO sourceDto = this.getByFormCode(sourceFormCode);
        if (sourceDto == null) {
            throw new JeecgBootException("源表单规则不存在");
        }

        // 创建新的规则配置
        FormRuleConfigDTO targetDto = new FormRuleConfigDTO()
                .setFormCode(targetFormCode)
                .setFormName(targetFormName)
                .setDescription(sourceDto.getDescription() + "(复制)")
                .setFieldRules(sourceDto.getFieldRules())
                .setDependencyRules(sourceDto.getDependencyRules())
                .setStatus(1);

        return this.saveOrUpdateFormRule(targetDto);
    }

    @Override
    public FormRuleStatisticsVO getStatistics() {
        return formRuleConfigMapper.getStatistics();
    }

    @Override
    public void notifyRuleUpdate(String formCode) {
        // 简化实现：直接刷新缓存即可，无需复杂的实时通知
        formRuleCacheService.refreshCache(formCode);
        log.info("表单规则通知更新: formCode={}", formCode);
    }

    @Override
    public void clearCache(String formCode) {
        formRuleCacheService.refreshCache(formCode);
    }

    @Override
    public void warmupCache(List<String> formCodes) {
        for (String formCode : formCodes) {
            try {
                this.getByFormCode(formCode);
            } catch (Exception e) {
                log.warn("预热缓存失败: formCode={}", formCode, e);
            }
        }
    }

    /**
     * 实体转DTO
     */
    private FormRuleConfigDTO convertToDTO(FormRuleConfig entity) {
        FormRuleConfigDTO dto = new FormRuleConfigDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 解析JSON字段
        if (StringUtils.isNotBlank(entity.getFieldRules())) {
            List<FieldRuleDTO> fieldRules = JSON.parseArray(entity.getFieldRules(), FieldRuleDTO.class);
            dto.setFieldRules(fieldRules);
        }
        
        if (StringUtils.isNotBlank(entity.getDependencyRules())) {
            List<DependencyRuleDTO> dependencyRules = JSON.parseArray(entity.getDependencyRules(), DependencyRuleDTO.class);
            dto.setDependencyRules(dependencyRules);
        }
        
        dto.setLastUpdated(entity.getUpdateTime() != null ? 
                entity.getUpdateTime().getTime() : System.currentTimeMillis());
        
        return dto;
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUsername() {
        try {
            return JwtUtil.getUserNameByToken(SpringContextUtils.getHttpServletRequest());
        } catch (Exception e) {
            return "system";
        }
    }

    @Override
    public List<java.util.Map<String, Object>> getDatabaseTables() {
        try {
            return formRuleConfigMapper.selectDatabaseTables();
        } catch (Exception e) {
            log.error("获取数据库表列表失败", e);
            // 返回默认的表列表
            return java.util.Arrays.asList(
                java.util.Map.of("tableName", "sys_user", "tableComment", "用户表"),
                java.util.Map.of("tableName", "sys_role", "tableComment", "角色表"),
                java.util.Map.of("tableName", "appointment_setting", "tableComment", "预约设置表"),
                java.util.Map.of("tableName", "appointment_record", "tableComment", "预约记录表")
            );
        }
    }
}
