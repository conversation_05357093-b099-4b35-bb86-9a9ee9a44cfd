package org.jeecg.modules.reg.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;

import java.util.List;

/**
 * 添加带检查部位的项目组合请求
 */
@Data
@ApiModel(value = "AddItemGroupWithCheckPartsRequest", description = "添加带检查部位的项目组合请求")
public class AddItemGroupWithCheckPartsRequest {

    @ApiModelProperty(value = "体检登记ID", required = true)
    private String customerRegId;

    @ApiModelProperty(value = "项目组合列表（前端已拼装好的数据）", required = true)
    private List<CustomerRegItemGroup> itemGroups;
}
