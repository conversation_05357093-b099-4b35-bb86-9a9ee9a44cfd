package org.jeecg.modules.reg.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.station.entity.CustomerRegItemResult;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 依赖项目结果DTO
 * @Author: jeecg-boot
 * @Date: 2025-01-03
 * @Version: V1.0
 */
@Data
@ApiModel(value = "DependentItemResultDTO", description = "依赖项目结果数据传输对象")
public class DependentItemResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目类型：GROUP-大项，ITEM-小项
     */
    @ApiModelProperty(value = "项目类型：GROUP-大项，ITEM-小项")
    private String itemType;

    /**
     * 体检登记ID
     */
    @ApiModelProperty(value = "体检登记ID")
    private String customerRegId;

    /**
     * 体检项目组ID
     */
    @ApiModelProperty(value = "体检项目组ID")
    private String customerRegItemGroupId;

    /**
     * 项目ID（大项ID或小项ID）
     */
    @ApiModelProperty(value = "项目ID（大项ID或小项ID）")
    private String itemId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String itemName;

    /**
     * HIS代码
     */
    @ApiModelProperty(value = "HIS代码")
    private String hisCode;

    /**
     * HIS名称
     */
    @ApiModelProperty(value = "HIS名称")
    private String hisName;

    /**
     * 结果值
     */
    @ApiModelProperty(value = "结果值")
    private String value;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    private String unit;

    /**
     * 异常标志：Y-异常，N-正常
     */
    @ApiModelProperty(value = "异常标志：Y-异常，N-正常")
    private String abnormalFlag;

    /**
     * 参考值范围
     */
    @ApiModelProperty(value = "参考值范围")
    private String valueRefRange;

    /**
     * 科室ID
     */
    @ApiModelProperty(value = "科室ID")
    private String departmentId;

    /**
     * 科室名称
     */
    @ApiModelProperty(value = "科室名称")
    private String departmentName;

    /**
     * 检查状态
     */
    @ApiModelProperty(value = "检查状态")
    private String checkStatus;

    /**
     * 是否已完成检查
     */
    @ApiModelProperty(value = "是否已完成检查")
    private Boolean isCompleted;

    /**
     * 结果录入时间
     */
    @ApiModelProperty(value = "结果录入时间")
    private String resultTime;

    /**
     * 录入人员
     */
    @ApiModelProperty(value = "录入人员")
    private String inputOperator;

    /**
     * 小项结果
     */
    @ApiModelProperty(value = "小项结果")
    private List<CustomerRegItemResult> customerRegItemResultList;
    /**
     * 构造方法 - 无参
     */
    public DependentItemResultDTO() {
    }

    /**
     * 构造方法 - 基本参数
     */
    public DependentItemResultDTO(String itemType, String customerRegId, String itemId, String itemName) {
        this.itemType = itemType;
        this.customerRegId = customerRegId;
        this.itemId = itemId;
        this.itemName = itemName;
    }

    /**
     * 构造方法 - 完整参数
     */
    public DependentItemResultDTO(String itemType, String customerRegId, String customerRegItemGroupId,
                                  String itemId, String itemName, String hisCode, String hisName,
                                  String value, String unit, String abnormalFlag, String valueRefRange,
                                  String departmentId, String departmentName, String checkStatus,
                                  Boolean isCompleted, String resultTime, String inputOperator) {
        this.itemType = itemType;
        this.customerRegId = customerRegId;
        this.customerRegItemGroupId = customerRegItemGroupId;
        this.itemId = itemId;
        this.itemName = itemName;
        this.hisCode = hisCode;
        this.hisName = hisName;
        this.value = value;
        this.unit = unit;
        this.abnormalFlag = abnormalFlag;
        this.valueRefRange = valueRefRange;
        this.departmentId = departmentId;
        this.departmentName = departmentName;
        this.checkStatus = checkStatus;
        this.isCompleted = isCompleted;
        this.resultTime = resultTime;
        this.inputOperator = inputOperator;
    }

    /**
     * 判断是否为大项
     */
    public boolean isGroup() {
        return "GROUP".equals(this.itemType);
    }

    /**
     * 判断是否为小项
     */
    public boolean isItem() {
        return "ITEM".equals(this.itemType);
    }

    /**
     * 判断是否异常
     */
    public boolean isAbnormal() {
        return "Y".equals(this.abnormalFlag);
    }

    /**
     * 获取显示名称（优先使用HIS名称）
     */
    public String getDisplayName() {
        return hisName != null && !hisName.trim().isEmpty() ? hisName : itemName;
    }

    /**
     * 获取显示代码（优先使用HIS代码）
     */
    public String getDisplayCode() {
        return hisCode != null && !hisCode.trim().isEmpty() ? hisCode : itemId;
    }
}
