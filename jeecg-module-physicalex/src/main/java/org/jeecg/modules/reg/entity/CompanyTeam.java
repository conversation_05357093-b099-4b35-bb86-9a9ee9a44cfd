package org.jeecg.modules.reg.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.aspect.annotation.Dict;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * @Description: 单位分组
 * @Author: jeecg-boot
 * @Date: 2024-02-19
 * @Version: V1.0
 */
@Data
@TableName("company_team")
@ApiModel(value = "company_team对象", description = "单位分组")
public class CompanyTeam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 分组编码
     */
    @Excel(name = "分组编码", width = 15)
    @ApiModelProperty(value = "分组编码")
    private String teamNum;
    /**
     * 所属登记
     */
    @ApiModelProperty(value = "所属登记")
    private String companyRegId;
    /**
     * 岗位类别
     */
    @Excel(name = "岗位类别", width = 15)
    @Dict(dicCode = "job_status")
    @ApiModelProperty(value = "岗位类别")
    private String post;
    /**
     * 分组名称
     */
    @Excel(name = "分组名称", width = 15)
    @ApiModelProperty(value = "分组名称")
    private String name;
    /**
     * 助记码
     */
    @ApiModelProperty(value = "助记码")
    private String helpChar;
    /**
     * 体检分类
     */
    @Excel(name = "体检分类", width = 15)
    @Dict(dicCode = "id", dicText = "name", dictTable = "exam_category")
    @ApiModelProperty(value = "体检分类")
    private String examCategory;
    /**
     * 性别
     */
    @Excel(name = "性别", width = 15)
    @Dict(dicCode = "sexLimit")
    @ApiModelProperty(value = "性别")
    private String sexLimit;
    /**
     * 最小年龄
     */
    @Excel(name = "最小年龄", width = 15)
    @ApiModelProperty(value = "最小年龄")
    private Integer minAge;
    /**
     * 最大年龄
     */
    @Excel(name = "最大年龄", width = 15)
    @ApiModelProperty(value = "最大年龄")
    private Integer maxAge;

    /**
     * 最大年龄
     */
    @Excel(name = "婚姻状况", width = 15)
    @ApiModelProperty(value = "婚姻状况")
    private String maritalStatus;
    /**
     * 体检场所
     */
    @Excel(name = "体检场所", width = 15)
    @ApiModelProperty(value = "体检场所")
    private String examPlace;
    /**
     * 支付方
     */
    @Excel(name = "支付方", width = 15)
    @Dict(dicCode = "payer_type")
    @ApiModelProperty(value = "支付方")
    private String payerType;
    /**
     * 加项支付方
     */
    @Excel(name = "加项支付方", width = 15)
    @Dict(dicCode = "payer_type")
    @ApiModelProperty(value = "加项支付方")
    private String addItemPayerType;
    /**
     * 分组价格
     */
    @Excel(name = "分组价格", width = 15)
    @ApiModelProperty(value = "分组价格")
    private java.math.BigDecimal teamPrice;
    /**
     * 折后价格
     */
    @Excel(name = "折后价格", width = 15)
    @ApiModelProperty(value = "折后价格")
    private java.math.BigDecimal teamDiscountPrice;
    /**
     * 分组价格折扣率
     */
    @Excel(name = "分组价格折扣率", width = 15)
    @ApiModelProperty(value = "分组价格折扣率")
    private java.math.BigDecimal teamDiscountRate;
    /**
     * 锁定状态
     */
    @Excel(name = "锁定状态", width = 15)
    @ApiModelProperty(value = "锁定状态")
    private Integer lockStatus;
    /**
     * 危害因素
     */
    @Excel(name = "危害因素", width = 15)
    @Dict(dicCode = "id", dicText = "name", dictTable = "zy_risk_factor")
    @ApiModelProperty(value = "危害因素")
    private String risks;
    /**
     * 车间
     */
    @Excel(name = "车间", width = 15)
    @Dict(dicCode = "id", dicText = "name", dictTable = "zy_work_shop")
    @ApiModelProperty(value = "车间")
    private String workShop;
    /**
     * 工种
     */
    @Excel(name = "工种", width = 15)
    @Dict(dicCode = "id", dicText = "name", dictTable = "zy_worktype")
    @ApiModelProperty(value = "工种")
    private String workType;
    /**
     * 是否备孕
     */
    @Excel(name = "是否备孕", width = 15)
    @ApiModelProperty(value = "是否备孕")
    private Integer pregnancyFlag;
    /**
     * 启用健康管理
     */
    @Excel(name = "启用健康管理", width = 15)
    @ApiModelProperty(value = "启用健康管理")
    private Integer healthManageFlag;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createTy;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 限制金额
     */
    @ApiModelProperty(value = "限制金额")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal limitAmount;
    /**
     * 允许共享单位支付额度
     */
    @ApiModelProperty(value = "允许共享单位支付额度")
    private String permitShare;
    /**
     * 允许转让额度
     */
    @ApiModelProperty(value = "允许转让额度")
    private String allowTransferAnother;
    /**
     * 允许转储到体检卡
     */
    @ApiModelProperty(value = "允许转储到体检卡")
    private String allowTransferCard;

    /**
     * 是否允许同步调整检客项目
     */
    @ApiModelProperty(value = "是否允许同步调整检客项目")
    private String allowChangeExaminerGroup;
    /**
     * 允许同步调整检客项目时的检客总检状态
     */
    @ApiModelProperty(value = "允许同步调整检客项目时的检客总检状态")
    private String allowChangeGroupSummaryStatus;

    /**
     * 限制剩余金额
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "限制剩余金额")
    private BigDecimal remainLimitAmount;
    /**
     * 限额发放策略
     */
    @ApiModelProperty(value = "限额发放策略")
    private String limitAmountIssueStrategy;

    /**
     * 姓名
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "姓名")
    private String customerName;

    @Excel(name = "限额分发周期", width = 15)
    @ApiModelProperty(value = "限额分发周期（自然年、跟随预约）")
    private String limitAmountPeriod;

    @Excel(name = "期末作废限额", width = 15)
    @ApiModelProperty(value = "期末作废限额")
    private String limitAmountExpired;

    @Excel(name = "允许同步调整限额账号金额", width = 15)
    @ApiModelProperty(value = "允许同步调整限额账号金额")
    private String allowSyncAdjustLimit;

    @Excel(name = "上传职业病平台", width = 15)
    @ApiModelProperty(value = "上传职业病平台")
    private String uploadPlateFlag;

    @Excel(name = "是否自动增加必检项目", width = 15)
    @ApiModelProperty(value = "是否自动增加必检项目")
    private String autoAddMustGroup;


    @TableField(exist = false)
    List<CompanyTeamItemGroup> itemGroupList;

    @TableField(exist = false)
    private CompanyReg companyReg;

    @TableField(exist = false)
    private List<CompanyTeamItemGroup> companyTeamItemGroupList;
    @TableField(exist = false)
    private String companyTeamItemGroupNames;

    @TableField(exist = false)
    private List<CustomerReg> customerRegList;
    @TableField(exist = false)
    private String customerLimitAmountId;

    public boolean contains(Integer age,String gender,String marriageStatus) {
       if(Objects.isNull(age)) {
           return false;
       }
        Boolean expression= age >= minAge && age <= maxAge;
        if (!StringUtils.equals(sexLimit,"不限")){
            expression= StringUtils.equals(sexLimit,gender) && expression;
        }
        if (!StringUtils.equalsAny(maritalStatus,"不限",null,"") && StringUtils.isNotBlank(marriageStatus)){
            expression= StringUtils.equals(maritalStatus,marriageStatus) && expression;
        }

        return expression;
    }
}
