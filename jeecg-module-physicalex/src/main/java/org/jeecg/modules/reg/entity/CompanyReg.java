package org.jeecg.modules.reg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.basicinfo.entity.Company;
import org.jeecg.modules.basicinfo.entity.ItemSuit;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 单位预约
 * @Author: jeecg-boot
 * @Date:   2024-02-19
 * @Version: V1.0
 */
@Data
@TableName("company_reg")
@ApiModel(value="company_reg对象", description="单位预约")
public class CompanyReg implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private String id;
	/**所在单位*/
    @Excel(name = "所在单位ID", width = 15)
    @ApiModelProperty(value = "所在单位ID")
    private String companyId;
    /**所在单位*/
    @Excel(name = "所在单位名称", width = 15)
    @ApiModelProperty(value = "所在单位名称")
    private String companyName;
	/**预约名称*/
    @Excel(name = "预约名称", width = 15)
    @ApiModelProperty(value = "预约名称")
    private String regName;
    /**助记码*/
    @ApiModelProperty(value = "助记码")
    private String helpChar;
	/**体检类别*/
    @Excel(name = "体检类别", width = 15, dictTable = "exam_category", dicText = "name", dicCode = "id")
    @Dict(dictTable = "exam_category", dicText = "name", dicCode = "id")
    @ApiModelProperty(value = "体检类别")
    private String examType;
	/**人员类别*/
    @Excel(name = "人员类别", width = 15, dicCode = "customer_exam_type")
    @Dict(dicCode = "customer_exam_type")
    @ApiModelProperty(value = "人员类别")
    private String personCategory;
	/**单位负责人*/
    @Excel(name = "单位负责人", width = 15)
    @ApiModelProperty(value = "单位负责人")
    private String linkMan;
	/**开始日期*/
    @Excel(name = "开始日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "开始日期")
    private Date startCheckDate;
	/**结束日期*/
    @Excel(name = "结束日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "结束日期")
    private Date endCheckDate;
	/**预约人数*/
    @Excel(name = "预约人数", width = 15)
    @ApiModelProperty(value = "预约人数")
    private Integer personCount;
	/**客服专员*/
    @Excel(name = "客服专员", width = 15, dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    @ApiModelProperty(value = "客服专员")
    private String serviceManager;
	/**封账状态*/
    @Excel(name = "封账状态", width = 15)
    @ApiModelProperty(value = "封账状态")
    private Integer checkoutStatus;
	/**锁定状态*/
    @Excel(name = "锁定状态", width = 15)
    @ApiModelProperty(value = "锁定状态")
    private Integer lockStatus;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**团报编号*/
    @Excel(name = "团报编号", width = 15)
    @ApiModelProperty(value = "团报编号")
    private String companyReportNo;
	/**团报日期*/
    @Excel(name = "团报日期", width = 15, format = "yyyy-MM-dd")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "团报日期")
    private Date companyReportDate;
	/**检查依据*/
    @Excel(name = "检查依据", width = 15)
    @ApiModelProperty(value = "检查依据")
    private String according;
	/**处理意见*/
    @Excel(name = "处理意见", width = 15)
    @ApiModelProperty(value = "处理意见")
    private String opinion;
	/**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**单位名称是否显示在报告中*/
    @ApiModelProperty(value = "单位名称是否显示在报告中")
    private String cnameDisplayInReport;
    /**是否打印导引单*/
    @ApiModelProperty(value = "是否打印导引单")
    private String printGuidance;
    /**是否打印申请单*/
    @ApiModelProperty(value = "是否打印申请单")
    private String printApply;
    /**添加时间*/
    @ApiModelProperty(value = "添加时间")
    private Date createTime;

    @TableField(exist = false)
    private List<ZyRiskFactor> riskFactorList;
    @TableField(exist = false)
    private List<CompanyTeamItemGroup> itemGroupList;
    @TableField(exist = false)
    private List<ItemSuit> itemSuit;
    @TableField(exist = false)
    private CustomerReg customerReg;
    @TableField(exist = false)
    private BigDecimal teamPrice;
    @TableField(exist = false)
    private List<CompanyTeam> teamList;

    /**职业病报告结果状态*/
    @TableField(exist = false)
    @ApiModelProperty(value = "职业病报告结果状态")
    private String occuReportResultStatus;

    /**职业病报告结果消息*/
    @TableField(exist = false)
    @ApiModelProperty(value = "职业病报告结果消息")
    private String occuReportResultMsg;

    /**职业病报告耗时*/
    @TableField(exist = false)
    @ApiModelProperty(value = "职业病报告耗时")
    private Long occuReportCostMs;

    /**职业病报告上传时间*/
    @TableField(exist = false)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "职业病报告上传时间")
    private Date occuReportUploadTime;

    @TableField(exist = false)
    @ApiModelProperty(value = "单位信息")
    private Company company;
}
