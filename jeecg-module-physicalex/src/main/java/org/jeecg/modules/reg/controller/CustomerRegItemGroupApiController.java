package org.jeecg.modules.reg.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.mobile.reg.service.ICustomerAccountService;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.service.ICustomerRegItemGroupService;
import org.jeecg.modules.reg.service.ICustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Map;

/**
 * @Description: 登记记录大项api
 * @Author: jeecg-boot
 * @Date: 2024-06-14
 * @Version: V1.0
 */
@Api(tags = "排队系统")
@RestController
@RequestMapping("/gds")
@Slf4j
public class CustomerRegItemGroupApiController extends JeecgController<Customer, ICustomerService> {
    @Autowired
    private ICustomerRegItemGroupService customerRegItemGroupService;


    @ApiOperation(value = "登记记录大项-弃检", notes = "登记记录大项-弃检")
    @PostMapping(value = "/abandon")
    public Result<?> queryPageList(@RequestBody JSONObject info) {
        String id = info.getString("id");
        String abandonReason = info.getString("reason");
        String operator = info.getString("operator");
        String abandonFlag = info.getString("abandonFlag");
        LambdaUpdateWrapper<CustomerRegItemGroup> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(CustomerRegItemGroup::getId, id).set(CustomerRegItemGroup::getAbandonFlag, abandonFlag).set(CustomerRegItemGroup::getUpdateName, operator).set(CustomerRegItemGroup::getUpdateTime, DateUtil.date());

        customerRegItemGroupService.update(lambdaUpdateWrapper);
        return Result.OK();
    }


}
