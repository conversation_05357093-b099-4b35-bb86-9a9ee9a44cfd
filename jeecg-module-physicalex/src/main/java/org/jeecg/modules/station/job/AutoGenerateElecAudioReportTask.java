package org.jeecg.modules.station.job;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.station.service.IElecAudioReportService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Component
public class AutoGenerateElecAudioReportTask implements Job {

    @Autowired
    private IElecAudioReportService reportService;

    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        if (lock.tryLock()) {
            try {
                reportService.generateElecAudioReportPdf();
            } catch (Exception e) {
                log.error("自动生成PDF报告任务失败", e);
            } finally {
                lock.unlock();
            }
        } else {
            log.warn("自动生成PDF报告任务正在运行");
        }
    }
}