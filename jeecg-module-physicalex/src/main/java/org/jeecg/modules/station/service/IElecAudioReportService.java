package org.jeecg.modules.station.service;

import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.station.bo.ElecAudioReport;

public interface IElecAudioReportService {
    ElecAudioReport getElecAudioReportData(String examNo);
    void generateElecAudioReportPdf();

     void generateElecAudioReportPdf4CustomerReg(CustomerRegItemGroup customerRegItemGroup, String url, String localFileServerDomain, boolean updateStatus);
}
