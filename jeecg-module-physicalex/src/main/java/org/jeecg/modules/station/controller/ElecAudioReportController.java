package org.jeecg.modules.station.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.station.bo.ElecAudioReport;
import org.jeecg.modules.station.service.IElecAudioReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags="报告数据获取")
@RestController
@RequestMapping("/station/report")
@Slf4j
public class ElecAudioReportController {

    @Autowired
    private IElecAudioReportService reportService;

    @ApiOperation(value = "生成电测听报告", notes = "生成电测听报告")
    @GetMapping(value = "/getElecAudioReportData")
    public Result<?> getElecAudioReportData(@RequestParam String examNo) {
        try {
            ElecAudioReport elecAudioReportData = reportService.getElecAudioReportData(examNo);
            return Result.OK(elecAudioReportData);
        } catch (Exception e) {
            return Result.error("查询失败！"+e.getMessage());
        }


    }
    @ApiOperation(value = "定时生成报告", notes = "定时生成报告")
    @GetMapping(value = "/generateElecAudioReportPdf")
    public Result<?> generateElecAudioReportPdf(@RequestParam String examNo) {
        try {
            reportService.generateElecAudioReportPdf();
            return Result.OK("生成成功");
        } catch (Exception e) {
            return Result.error("生成失败！"+e.getMessage());
        }


    }
}
