package org.jeecg.modules.station.bo;

import lombok.Data;

@Data
public class ElecAudioOriginData {
    private EarData leftEarAirData;
    private EarData leftEarBoneData;
    private EarData rightEarAirData;
    private EarData rightEarBoneData;
    private Double leftWhisperFrequencyAirAvg;
    private Double leftHighFrequencyAirAvg;
    private Double leftWhisperFrequencyBoneAvg;
    private Double leftHighFrequencyBoneAvg;
    private Double rightWhisperFrequencyAirAvg;
    private Double rightHighFrequencyAirAvg;
    private Double rightWhisperFrequencyBoneAvg;
    private Double rightHighFrequencyBoneAvg;
    private Double bothWhisperFrequencyAirAvg;
    private Double bothHighFrequencyAirAvg;
    private Double bothWhisperFrequencyBoneAvg;
    private Double bothHighFrequencyBoneAvg;
    @Data
   public static class EarData {
        private Integer hearingThreshold500Hz;
        private Integer hearingThreshold1000Hz;
        private Integer hearingThreshold2000Hz;
        private Integer hearingThreshold3000Hz;
        private Integer hearingThreshold4000Hz;
        private Integer hearingThreshold6000Hz;
    }
}
