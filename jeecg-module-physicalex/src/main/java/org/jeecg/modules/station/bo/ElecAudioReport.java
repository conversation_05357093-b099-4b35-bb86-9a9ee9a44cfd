package org.jeecg.modules.station.bo;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ElecAudioReport {

    private String examNo;

    private String name;

    private String gender;

    private Integer age;

    private String deviceName;

    private Date checkTime;

    private String reportNo;

    private String checkDoctorName;

    private String checkDoctorSignPic;

    private List<AudioCheckData> leftEarAirData;

    private List<AudioCheckData> rightEarAirData;

    private List<AudioCheckData> leftEarBoneData;

    private List<AudioCheckData> rightEarBoneData;

    private List<AudioCheckData> leftCorrectionBeforeData;

    private List<AudioCheckData> rightCorrectionBeforeData;

    private List<AudioCheckData> leftCorrectionAfterData;

    private List<AudioCheckData> rightCorrectionAfterData;

    private String leftWhisperFrequencyAirAvg;

    private String rightWhisperFrequencyAirAvg;

    private String bothHighFrequencyAirAvg;

     @Data
     public static class AudioCheckData {
        private String hearingThreshold;
         private String correctionBefore;
         private String correctionAfter;
        private String finalResult;
        private String boneResult;
        private String airResult;
    }
}
