package org.jeecg.modules.station.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.utils.*;
import org.jeecg.modules.ai.service.AIService;
import org.jeecg.modules.basicinfo.entity.*;
import org.jeecg.modules.basicinfo.mapper.*;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.jeecg.modules.reg.mapper.CustomerRegItemGroupMapper;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.jeecg.modules.station.bo.*;
import org.jeecg.modules.station.dto.AbnormalSummary;
import org.jeecg.modules.station.entity.CustomerRegCriticalItem;
import org.jeecg.modules.station.entity.CustomerRegDepartSummary;
import org.jeecg.modules.station.entity.CustomerRegItemResult;
import org.jeecg.modules.station.mapper.CustomerRegDepartSummaryMapper;
import org.jeecg.modules.station.mapper.CustomerRegItemResultMapper;
import org.jeecg.modules.station.service.ICustomerRegCriticalItemService;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.mapper.CustomerRegSummaryMapper;
import org.jeecg.modules.summary.service.SystemUserUtilService;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.mapper.SysDepartMapper;
import org.jeecg.modules.system.service.ISysUserService;
import org.mvel2.MVEL;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 科室小结
 * @Author: jeecg-boot
 * @Date: 2024-04-19
 * @Version: V1.0
 */
@Slf4j
@Service
public class CustomerRegDepartSummaryServiceImpl extends ServiceImpl<CustomerRegDepartSummaryMapper, CustomerRegDepartSummary> implements ICustomerRegDepartSummaryService {

    @Resource
    private DiagnosisComplexItemRuleMapper diagnosisComplexItemRuleMapper;
    @Resource
    private DiagnosisComplexMapper diagnosisComplexMapper;
    @Resource
    private ICustomerRegCriticalItemService customerRegCriticalItemService;
    @Resource
    private ItemInfoMapper itemInfoMapper;
    @Resource
    private ItemGroupMapper itemGroupMapper;
    @Resource
    private JdbcTemplate jdbcTemplate;
    @Resource
    private CustomerRegDepartSummaryMapper regDepartSummaryMapper;
    @Resource
    private SysDepartMapper sysDepartMapper;
    @Resource
    private CustomerRegItemResultMapper customerRegItemResultMapper;
    @Resource
    private ISysSettingService sysSettingService;
    @Resource
    private CustomerRegMapper customerRegMapper;
    @Resource
    private SystemUserUtilService systemUserUtilService;
    @Resource
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Resource
    private CustomerRegSummaryMapper customerRegSummaryMapper;
    @Resource
    private SystemUserUtilService sysUserUtilService;
    @Resource
    private ISysUserService sysUserService;
    @Resource
    private AbnormalSummarySettingMapper abnormalSummarySettingMapper;
    @Resource
    private AIService aiService;


    @Override
    public void removeDepartmentSummary(String id) throws Exception {
        CustomerRegDepartSummary departSummary = getById(id);
        if (departSummary == null) {
            throw new Exception("科室小结不存在！");
        }
        CustomerRegSummary summary = customerRegSummaryMapper.getByRegId(departSummary.getCustomerRegId());
        if (summary != null) {
            if (StringUtils.equals(summary.getStatus(), ExConstants.SUMMARY_STATUS_审核通过)) {
                throw new Exception("已有总检记录，不能清空小结!");
            }
        }
        removeById(id);
        //jdbcTemplate.update("update customer_reg_item_group set check_status=? where depart_summary_id=? and check_status=?", ExConstants.CHECK_STATUS_已检, id, ExConstants.CHECK_STATUS_已小结);
    }

    @Override
    public List<ComplexDiagnosticResult> getDiagnosticByComplex(String departmentId, CustomerReg reg, List<CustomerRegItemResult> itemResultList, String severityDegree) {

        List<ComplexDiagnosticResult> result = new ArrayList<>();

        List<DiagnosisComplexItemRule> ruleList = diagnosisComplexItemRuleMapper.selectByDepartmentId(departmentId, severityDegree, reg.getAge(), reg.getMarriageStatus(), reg.getGender());
        Map<String, Set<DiagnosisComplexItemRule>> matchedRuleMap = new HashMap<>();

        if (ruleList != null && !ruleList.isEmpty()) {
            for (CustomerRegItemResult itemResult : itemResultList) {
                for (DiagnosisComplexItemRule rule : ruleList) {
                    String valueType = itemResult.getValueType();
                    String itemId = itemResult.getItemId();

                    String ruleFieldType = rule.getFieldType();
                    String ruleItemId = rule.getItemId();
                    if (!StringUtils.equals(itemId, ruleItemId) || !StringUtils.equals(ruleFieldType, valueType)) {
                        continue;
                    }
                    String itemValue = itemResult.getValue();
                    if (StringUtils.isBlank(itemValue)) {
                        continue;
                    }

                    String ruleExpression = rule.getGroovyExpression();
                    if (StringUtils.isNotBlank(ruleExpression)) {
                        try {
                            boolean expresionResult = false;
                            expresionResult = GroovyUtil.getInstance().executeExpression(ruleExpression, itemValue);
                            if (expresionResult) {
                                rule.setMatched(true);
                                Set<DiagnosisComplexItemRule> matchedRuleList = matchedRuleMap.get(rule.getComplexId());
                                if (matchedRuleList == null) {
                                    matchedRuleList = new HashSet<>();
                                }
                                matchedRuleList.add(rule);
                                matchedRuleMap.put(rule.getComplexId(), matchedRuleList);
                            }
                        } catch (GroovyResultException e) {
                            log.error("匹配符合判断规则，计算groovy表达式异常，DiagnosisComplexItemRule：" + JSONObject.toJSONString(rule) + "\r\n" + "itemResult:" + JSONObject.toJSONString(itemResult) + "\r\n" + "customerReg:" + JSONObject.toJSONString(reg), e);
                        }
                        continue;
                    } else {
                        String ruleValue = rule.getFieldValue();
                        if (StringUtils.equals(ruleFieldType, ExConstants.ITEM_VALUE_TYPE_数值型) || StringUtils.equals(ruleFieldType, ExConstants.ITEM_VALUE_TYPE_计算型)) {
                            // 数值型或计算型
                            String operator = rule.getOperator();
                            //将operator的值转为实际的操作符
                            if (StringUtils.equals(operator, ExConstants.OPERATOR_大于)) {
                                operator = ">";
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_小于)) {
                                operator = "<";
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_等于)) {
                                operator = "==";
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_不等于)) {
                                operator = "!=";
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_大于等于)) {
                                operator = ">=";
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_小于等于)) {
                                operator = "<=";
                            } else {
                                operator = null;
                            }

                            if (operator == null) {
                                continue;
                            }

                            //拼接表达式
                            String expression = itemValue + operator + ruleValue;
                            // 在给定的上下文中评估表达式并获取结果
                            Boolean expressionResult = MVEL.evalToBoolean(expression, new HashMap<>());
                            if (expressionResult) {
                                rule.setMatched(true);
                                Set<DiagnosisComplexItemRule> matchedRuleList = matchedRuleMap.get(rule.getComplexId());
                                if (matchedRuleList == null) {
                                    matchedRuleList = new HashSet<>();
                                }
                                matchedRuleList.add(rule);
                                matchedRuleMap.put(rule.getComplexId(), matchedRuleList);
                            }
                        } else {

                            String operator = rule.getOperator();
                            if (StringUtils.equals(operator, ExConstants.OPERATOR_包含)) {
                                if (StringUtils.contains(itemValue, ruleValue)) {
                                    rule.setMatched(true);
                                    Set<DiagnosisComplexItemRule> matchedRuleList = matchedRuleMap.get(rule.getComplexId());
                                    if (matchedRuleList == null) {
                                        matchedRuleList = new HashSet<>();
                                    }
                                    matchedRuleList.add(rule);
                                    matchedRuleMap.put(rule.getComplexId(), matchedRuleList);
                                }
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_不包含)) {
                                if (!StringUtils.contains(itemValue, ruleValue)) {
                                    rule.setMatched(true);
                                    Set<DiagnosisComplexItemRule> matchedRuleList = matchedRuleMap.get(rule.getComplexId());
                                    if (matchedRuleList == null) {
                                        matchedRuleList = new HashSet<>();
                                    }
                                    matchedRuleList.add(rule);
                                    matchedRuleMap.put(rule.getComplexId(), matchedRuleList);
                                }
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_正则)) {
                                // 正则表达式
                                if (itemValue.matches(ruleValue)) {
                                    rule.setMatched(true);
                                    Set<DiagnosisComplexItemRule> matchedRuleList = matchedRuleMap.get(rule.getComplexId());
                                    if (matchedRuleList == null) {
                                        matchedRuleList = new HashSet<>();
                                    }
                                    matchedRuleList.add(rule);
                                    matchedRuleMap.put(rule.getComplexId(), matchedRuleList);
                                }
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_等于)) {
                                if (StringUtils.equals(itemValue, ruleValue)) {
                                    rule.setMatched(true);
                                    Set<DiagnosisComplexItemRule> matchedRuleList = matchedRuleMap.get(rule.getComplexId());
                                    if (matchedRuleList == null) {
                                        matchedRuleList = new HashSet<>();
                                    }
                                    matchedRuleList.add(rule);
                                    matchedRuleMap.put(rule.getComplexId(), matchedRuleList);
                                }
                            } else if (StringUtils.equals(operator, ExConstants.OPERATOR_不等于)) {
                                if (!StringUtils.equals(itemValue, ruleValue)) {
                                    rule.setMatched(true);
                                    Set<DiagnosisComplexItemRule> matchedRuleList = matchedRuleMap.get(rule.getComplexId());
                                    if (matchedRuleList == null) {
                                        matchedRuleList = new HashSet<>();
                                    }
                                    matchedRuleList.add(rule);
                                    matchedRuleMap.put(rule.getComplexId(), matchedRuleList);
                                }
                            }
                        }
                    }
                }
            }

            //获取符合条件的诊断
            for (Map.Entry<String, Set<DiagnosisComplexItemRule>> entry : matchedRuleMap.entrySet()) {
                Set<DiagnosisComplexItemRule> matchedRuleList = entry.getValue();
                String complexId = entry.getKey();
                DiagnosisComplex complex = diagnosisComplexMapper.selectById(complexId);

                String diagnostic = "";
                List<String> relatedItemIdList = new ArrayList<>();
                if (complex != null && complex.getEnableFlag() == 1 && complex.getRulesCount() == matchedRuleList.size()) {
                    String expression = complex.getFormula();
                    if (StringUtils.isBlank(expression)) {
                        continue;
                    }
                    Map<String, Object> variables = new HashMap<>();
                    for (DiagnosisComplexItemRule rule : matchedRuleList) {
                        variables.put(rule.getTitle(), rule.isMatched());
                        relatedItemIdList.add(rule.getItemId());
                    }
                    // 在给定的上下文中评估表达式并获取结果
                    Boolean expresionResult = MVEL.evalToBoolean(expression, variables);
                    if (expresionResult) {
                        diagnostic = complex.getName();
                    }
                    ComplexDiagnosticResult diagnosticResult = new ComplexDiagnosticResult();
                    diagnosticResult.setResult(diagnostic);
                    diagnosticResult.setRelatedItemIdList(relatedItemIdList);
                    diagnosticResult.setSeverityDegree(complex.getSeverityDegree());
                    diagnosticResult.setComplexId(complexId);
                    diagnosticResult.setBaseOn(complex.getBaseOn());
                    result.add(diagnosticResult);
                }
            }
        }

        return result;
    }

    @Override
    public DepartSummary generateSummary(String departmentId, CustomerReg reg, List<CustomerRegItemResult> itemResultList) {

        DepartSummaryAndCriticalList summaryAndCriticalList = genetateDepartSummaryAndCriticalItem(departmentId, reg, itemResultList);
        List<CustomerRegCriticalItem> regCriticalItemList = summaryAndCriticalList.getCriticalItemList();
        List<String> summaryList = summaryAndCriticalList.getSummaryList();
        List<String> pureSummaryList = summaryAndCriticalList.getPureSummaryList();

        SysSetting joinCharSetting = sysSettingService.getByCode("departSummaryJoinChar");
        String joinChar = joinCharSetting != null ? joinCharSetting.getValue() : "；";
        String summaryResult = StringUtils.join(summaryList, joinChar);

        SysSetting normalResultSetting = sysSettingService.getByCode("departSummaryNormalWord");
        String normalResult = normalResultSetting != null ? normalResultSetting.getValue() : "未见明显异常";
        summaryResult = StringUtils.isBlank(summaryResult) ? normalResult : summaryResult;
        DepartSummary departSummary = new DepartSummary();
        departSummary.setSummary(summaryResult);
        departSummary.setCriticalItems(regCriticalItemList);
        departSummary.setPureSummary(StringUtils.join(pureSummaryList, ";"));
        departSummary.setAbnormalFlag(summaryAndCriticalList.getSummaryAbnormalFlag());

        return departSummary;
    }


    @Override
    public List<String> generateAbnormalSummaryList(String customerRegId) {

        List<AbnormalSummary> list = generateAbnormalSummaryBeanList(customerRegId);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        return list.stream().map(AbnormalSummary::getSummaryText).collect(Collectors.toList());
    }


    @Override
    public List<AbnormalSummary> generateAbnormalSummaryBeanList(String customerRegId) {
        //1、获取登记记录的所有小项结果
        List<CustomerRegItemResult> resultList = customerRegItemResultMapper.listByRegId(customerRegId);
        //2、获取abnormalFlag等于1的小项
        resultList = resultList.stream().filter(itemResult -> StringUtils.equals(itemResult.getAbnormalFlag(), "1")).toList();
        //3、将过滤后的小项集合（resultList）按照itemGroupId进行分组
        Map<String, List<CustomerRegItemResult>> groupedByGroupIdResults = resultList.stream().collect(Collectors.groupingBy(CustomerRegItemResult::getItemGroupId));
        //4、获取登记记录的所有大项
        List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listWithItemGroupByReg(customerRegId, null, false);
        groupList.forEach(group -> {
            group.setResultList(groupedByGroupIdResults.get(group.getItemGroupId()));
            //如果resutList中有小项的abnormalFlag等于1，则将大项的abnormalFlag设置为1
            if (group.getResultList() != null && group.getResultList().stream().anyMatch(itemResult -> StringUtils.equals(itemResult.getAbnormalFlag(), "1"))) {
                group.setAbnormalFlag("1");
            }
        });

        //5、获取异常的大项集合：过滤abnormalFlag等于1的大项并且itemResutlList不为空
        List<CustomerRegItemGroup> abnormalGroupList = new ArrayList<>(groupList.stream().filter(group -> group.getResultList() != null && !group.getResultList().isEmpty() && StringUtils.equals(group.getAbnormalFlag(), "1")).toList());

        //6、整理异常汇总集合
        List<org.jeecg.modules.station.bo.AbnormalSummaryBean> abnormalAbnormalSummaryBeans = new ArrayList<>();
        //6.1、获取异常汇总设置
        abnormalSummarySettingMapper.selectList(new LambdaQueryWrapper<AbnormalSummarySetting>().eq(AbnormalSummarySetting::getEnableFlag, "1")).forEach(setting -> {
            String itemgroupIds = setting.getItemgroupIds();
            List<String> itemGroupIdList = Arrays.asList(itemgroupIds.split(","));
            String departmentId = setting.getDepartmentId();
            String sortRange = setting.getItemgroupSortRang();
            //判断sortRange有效性
            Integer start = null;
            Integer end = null;
            if (StringUtils.isNotBlank(sortRange)) {
                String[] sortRangeArr = sortRange.split("-");
                if (sortRangeArr.length != 2) {
                    return;
                }
                try {
                    start = Integer.parseInt(sortRangeArr[0]);
                    end = Integer.parseInt(sortRangeArr[1]);
                } catch (NumberFormatException ignored) {
                }
            }

            List<CustomerRegItemGroup> itemGroupList = new ArrayList<>();
            for (CustomerRegItemGroup group : abnormalGroupList) {
                boolean matchesDepartment = StringUtils.equals(group.getDepartmentId(), departmentId);
                boolean matchesItemGroup = itemGroupIdList.contains(group.getItemGroupId());
                boolean matchesSortRange = (start != null && end != null) && (group.getItemGroup().getSort() >= start && group.getItemGroup().getSort() <= end);

                if (matchesDepartment || matchesItemGroup || matchesSortRange) {
                    itemGroupList.add(group);
                }
            }
            //6.2 通过判断是否有与异常汇总设置匹配的大项，如果有则生成异常汇总
            if (!itemGroupList.isEmpty()) {
                org.jeecg.modules.station.bo.AbnormalSummaryBean groupSummary = new org.jeecg.modules.station.bo.AbnormalSummaryBean();
                groupSummary.setTitle(setting.getTitle());
                groupSummary.setOrder(setting.getSeq());
                groupSummary.setItemGroupList(itemGroupList);
                groupSummary.setItemGroupFormat(setting.getItemgroupFormate());
                groupSummary.setAbnormalSettingFlag(true);
                groupSummary.setItemOrGroup(setting.getItemOrGroup());
                groupSummary.setAbnormalJudgeExpression(setting.getJudgeExpression());
                groupSummary.setFormat(setting.getFormat());
                groupSummary.setDisgnoseSeparator(setting.getDisgnoseSeparator());
                abnormalAbnormalSummaryBeans.add(groupSummary);
                //从abnormalGroupList中移除已经加入到groupSummaryList中的大项
                abnormalGroupList.removeAll(itemGroupList);
            }
        });

        //6.3 将未匹配到异常汇总设置的大项，逐一生成异常汇总
        for (CustomerRegItemGroup group : abnormalGroupList) {
            org.jeecg.modules.station.bo.AbnormalSummaryBean abnormalSummaryBean = new org.jeecg.modules.station.bo.AbnormalSummaryBean();
            abnormalSummaryBean.setTitle(group.getItemGroupName());
            abnormalSummaryBean.setOrder(group.getItemGroup().getSort());
            abnormalSummaryBean.setItemGroupList(Collections.singletonList(group));
            abnormalSummaryBean.setItemGroupFormat("{{{summary}}}");
            abnormalSummaryBean.setFormat(group.getItemGroup().getNormalSummaryFormat());
            abnormalSummaryBean.setAbnormalSettingFlag(false);
            abnormalSummaryBean.setItemOrGroup("大项");
            abnormalSummaryBean.setDisgnoseSeparator("");
            abnormalAbnormalSummaryBeans.add(abnormalSummaryBean);
        }

        //6.4 对groupSummaryList按照sort进行排序
        abnormalAbnormalSummaryBeans.sort(Comparator.comparing(org.jeecg.modules.station.bo.AbnormalSummaryBean::getOrder));

        //7.1、获取异常汇总的格式
        String abnormalSummaryItemFormat = sysSettingService.getValueByCode("abnormalSummaryItemFormat");
        if (StringUtils.isBlank(abnormalSummaryItemFormat)) {
            abnormalSummaryItemFormat = "{{title}}:{{{text}}}";
        }

        // 提前收集所有 itemId
        Set<String> itemIds = resultList.stream().map(CustomerRegItemResult::getItemId).collect(Collectors.toSet());
        // 批量查询
        Map<String, ItemInfo> itemInfoMap = new HashMap<>();
        try {
            if (CollectionUtils.isNotEmpty(itemIds)) {
                itemInfoMap = itemInfoMapper.selectBatchIds(itemIds).stream().collect(Collectors.toMap(ItemInfo::getId, Function.identity()));
            }
        } catch (Exception ignored) {
        }

        //7.2遍历groupSummaryList，生成小结，小结的格式为：1、大项名称：小项名称（小项值）,小项名称（小项值）；2、大项名称：小项名称（小项值）,小项名称（小项值）；
        List<AbnormalSummary> summaryList = new ArrayList<>();
        for (AbnormalSummaryBean abnormalSummaryBean : abnormalAbnormalSummaryBeans) {
            String itemGroupFormate = abnormalSummaryBean.getItemGroupFormat();
            //根据abnormalSummaryBean中的itemGroupList生成小结：1、为每个大项生成小结。2、汇总大项的小结，生成AbnormalSummaryBean的summaryText。3、根据AbnormalSummaryBean创建SummaryBean并添加到summaryList中。
            List<CustomerRegItemGroup> itemGroupList = abnormalSummaryBean.getItemGroupList();
            Set<String> summaryOfGroupSet = new TreeSet<>();
            Set<String> totalSummaryOfItemSet = new TreeSet<>();
            if ("大项".equals(abnormalSummaryBean.getItemOrGroup())) {
                for (CustomerRegItemGroup itemGroup : itemGroupList) {
                    // 关联的小项列表
                    List<CustomerRegItemResult> relatedItemResults = new ArrayList<>();
                    //为每个大项生成小结
                    List<String> summaryOfItemList = new ArrayList<>();
                    List<CustomerRegItemResult> resultListOfGroup = itemGroup.getResultList();
                    if (resultListOfGroup != null && !resultListOfGroup.isEmpty()) {
                        //遍历大项的小项，生成小项的小结
                        for (CustomerRegItemResult itemResult : resultListOfGroup) {
                            relatedItemResults.add(itemResult);
                            ItemInfo itemInfo = itemInfoMap.get(itemResult.getItemId());
                            Integer summaryFlag;

                            String summaryFormat = null;
                            if (itemInfo != null) {
                                summaryFormat = itemInfo.getSummaryFormat();
                                summaryFlag = itemInfo.getSumableFlag();
                            } else {
                                summaryFormat = itemGroup.getItemGroup().getCombineSummaryFormat();
                                summaryFlag = itemGroup.getItemGroup().getItemSumableFlag();
                            }
                            summaryFlag = summaryFlag == null ? 1 : summaryFlag;
                            if (summaryFlag == 0) {
                                continue;
                            }

                            String itemName = itemResult.getItemName();
                            String value = itemResult.getValue();
                            String unit = itemResult.getUnit();
                            String conclusion = itemResult.getCheckConclusion();
                            String abnormalFlag = itemResult.getAbnormalFlag();
                            String abnormalFlagDesc = itemResult.getAbnormalFlagDesc();
                            String criticalFlag = itemResult.getCriticalFlag();
                            String criticalDegree = itemResult.getCriticalDegree();
                            Map<String, Object> map = new HashMap<>();
                            map.put("name", itemName);
                            map.put("value", value);
                            map.put("unit", unit);
                            map.put("abnormalFlag", abnormalFlag);
                            map.put("abnormalFlagDesc", abnormalFlagDesc);
                            map.put("criticalFlag", criticalFlag);
                            map.put("criticalDegree", criticalDegree);
                            if (StringUtils.isNotBlank(conclusion) && StringUtils.isNotEmpty(abnormalSummaryBean.getDisgnoseSeparator())) {
                                List<String> conclusionSplitList = DiagnosisSplitUtils.split(conclusion, abnormalSummaryBean.getDisgnoseSeparator());
                                Set<String> finalConclusionList = new LinkedHashSet<>();
                                for (String conclusion2 : conclusionSplitList) {
                                    String judgeExpression = abnormalSummaryBean.getAbnormalJudgeExpression();
                                    boolean needRemove = false;
                                    if (StringUtils.isNotBlank(judgeExpression)) {

                                        try {
                                            needRemove = GroovyUtil.getInstance().executeExpression(judgeExpression, conclusion2);
                                        } catch (GroovyResultException e) {
                                            log.error("过滤异常汇总中正常的描述，计算groovy表达式异常：" + e.getMessage());
                                        }
                                    } else {
                                        needRemove = StringUtils.containsAny(conclusion2, "未见异常", "未见明显异常");
                                    }
                                    if (!needRemove) {
                                        finalConclusionList.add(conclusion2);
                                    }
                                }

                                if (CollectionUtils.isNotEmpty(finalConclusionList)) {
                                    itemResult.setCheckConclusion(StringUtils.join(finalConclusionList,  " "));
                                    conclusion = StringUtils.join(finalConclusionList,  " ");
                                }
                            }

                            map.put("conclusion", conclusion);
                            String itemSummaryText = StringUtils.isNotBlank(summaryFormat) ? MustacheUtil.render(summaryFormat, map) : conclusion;

                            //使用智能标点处理器进行标点符号规范化
                            if (StringUtils.isNotBlank(itemSummaryText)) {
                                itemSummaryText = SmartPunctuationProcessor.normalize(itemSummaryText);
                                summaryOfItemList.add(itemSummaryText);
                                totalSummaryOfItemSet.add(itemSummaryText);
                            }
                        }

                        //将小项的小结汇总，生成大项的小结
                        String groupName = itemGroup.getItemGroupName();
                        String summaryOfGroup = StringUtils.join(summaryOfItemList, "，");
                        Map<String, Object> dataMap = new HashMap<>();
                        dataMap.put("name", groupName);
                        dataMap.put("summary", summaryOfGroup);
                        summaryOfGroupSet.add(MustacheUtil.render(itemGroupFormate, dataMap));
                    } else {
                        summaryOfGroupSet.add(itemGroup.getCheckConclusion());
                    }

                    AbnormalSummary abnormalSummary = new AbnormalSummary();
                    String title = abnormalSummaryBean.getTitle();
                    abnormalSummaryItemFormat = StringUtils.isNotBlank(abnormalSummaryBean.getFormat()) ? abnormalSummaryBean.getFormat() : abnormalSummaryItemFormat;
                    abnormalSummary.setFormat(abnormalSummaryItemFormat);
                    String abnormalSummaryText = StringUtils.join(summaryOfGroupSet, "，");
                    Map<String, Object> abnormalSummaryFormatMap = new HashMap<>();
                    abnormalSummaryFormatMap.put("title", title);
                    abnormalSummaryFormatMap.put("text", abnormalSummaryText);
                    //abnormalSummaryFormatMap.put("no", i);

                    String groupSummaryTextStr = MustacheUtil.render(abnormalSummaryItemFormat, abnormalSummaryFormatMap);
                    if (StringUtils.isNotBlank(groupSummaryTextStr)) {
                        groupSummaryTextStr = SmartPunctuationProcessor.normalize(groupSummaryTextStr);
                    }
                    abnormalSummary.setTitle(title);
                    abnormalSummary.setText(abnormalSummaryText);
                    abnormalSummary.setSummaryText(groupSummaryTextStr);
                    // 将关联的小项列表和大项列表添加到 AbnormalSummary 中
                    abnormalSummary.setRelatedItemResults(relatedItemResults);
                    abnormalSummary.setRelatedItemGroupList(Collections.singletonList(itemGroup));
                    // 将 totalSummaryOfItemSet 转为 List
                    List<String> totalSummaryOfItemListStr = new ArrayList<>(totalSummaryOfItemSet);
                    abnormalSummary.setItemSummaryTextList(totalSummaryOfItemListStr);
                    if (CollectionUtils.isNotEmpty(totalSummaryOfItemListStr)) {
                        summaryList.add(abnormalSummary);
                    }
                }
            } else if ("小项".equalsIgnoreCase(abnormalSummaryBean.getItemOrGroup())) {
                for (CustomerRegItemGroup itemGroup : itemGroupList) {
                    // 关联的小项列表
                    List<CustomerRegItemResult> relatedItemResults = new ArrayList<>();
                    List<CustomerRegItemResult> resultListOfGroup = itemGroup.getResultList();
                    if (resultListOfGroup != null && !resultListOfGroup.isEmpty()) {
                        for (CustomerRegItemResult itemResult : resultListOfGroup) {
                            relatedItemResults.add(itemResult);

                            ItemInfo itemInfo = itemInfoMap.get(itemResult.getItemId());
//                            ItemInfo itemInfo = itemInfoMapper.selectById(itemResult.getItemId());
                            Integer summaryFlag = 1;

                            String summaryFormat = null;
                            if (itemInfo != null) {
                                summaryFormat = itemInfo.getSummaryFormat();
                                summaryFlag = itemInfo.getSumableFlag();
                            } else {
                                summaryFormat = itemGroup.getItemGroup().getCombineSummaryFormat();
                                summaryFlag = itemGroup.getItemGroup().getItemSumableFlag();
                            }
                            summaryFlag = summaryFlag == null ? 1 : summaryFlag;
                            if (summaryFlag == 0) {
                                continue;
                            }

                            String itemName = itemResult.getItemName();
                            String value = itemResult.getValue();
                            String unit = itemResult.getUnit();
                            String conclusion = itemResult.getCheckConclusion();
                            String abnormalFlag = itemResult.getAbnormalFlag();
                            String abnormalFlagDesc = itemResult.getAbnormalFlagDesc();
                            String criticalFlag = itemResult.getCriticalFlag();
                            String criticalDegree = itemResult.getCriticalDegree();
                            Map<String, Object> map = new HashMap<>();
                            map.put("name", itemName);
                            map.put("value", value);
                            map.put("unit", unit);
                            map.put("abnormalFlag", abnormalFlag);
                            map.put("abnormalFlagDesc", abnormalFlagDesc);
                            map.put("criticalFlag", criticalFlag);
                            map.put("criticalDegree", criticalDegree);
                            if (StringUtils.isNotBlank(conclusion)) {
                                Set<String> finalConclusionList = new TreeSet<>();
                                if (StringUtils.isNotEmpty(abnormalSummaryBean.getDisgnoseSeparator())) {
                                    List<String> conclusionSplitList = DiagnosisSplitUtils.split(conclusion, abnormalSummaryBean.getDisgnoseSeparator());
                                    for (String splitConclusion : conclusionSplitList) {
                                        String judgeExpression = abnormalSummaryBean.getAbnormalJudgeExpression();
                                        boolean needRemove = false;
                                        if (StringUtils.isNotBlank(judgeExpression)) {
                                            try {
                                                needRemove = GroovyUtil.getInstance().executeExpression(judgeExpression, splitConclusion);
                                            } catch (GroovyResultException e) {
                                                log.error("过滤异常汇总中正常的描述，计算groovy表达式异常：" + e.getMessage());
                                            }
                                        } else {
                                            needRemove = StringUtils.containsAny(splitConclusion, "未见异常", "未见明显异常");
                                        }
                                        if (!needRemove) {
                                            finalConclusionList.add(splitConclusion);
                                        }
                                    }
                                } else {
                                    finalConclusionList.add(conclusion);
                                }

                                if (CollectionUtils.isNotEmpty(finalConclusionList)) {

                                    for (String conclusion2 : finalConclusionList) {
                                        map.put("conclusion", conclusion2);
                                        String itemSummaryText = StringUtils.isNotBlank(summaryFormat) ? MustacheUtil.render(summaryFormat, map) : conclusion;
                                        if (StringUtils.isNotBlank(itemSummaryText)) {
                                            // 使用智能标点处理器进行统一的标点符号规范化处理
                                            itemSummaryText = SmartPunctuationProcessor.normalize(itemSummaryText);

                                            // Create a separate SummaryBean for each item
                                            AbnormalSummary abnormalSummary = new AbnormalSummary();
                                            String title = itemGroup.getItemGroupName();
                                            Map<String, Object> itemSummaryFormatMap = new HashMap<>();

                                            abnormalSummaryItemFormat = StringUtils.isNotBlank(abnormalSummaryBean.getFormat()) ? abnormalSummaryBean.getFormat() : abnormalSummaryItemFormat;
                                            itemSummaryFormatMap.put("title", itemName);
                                            itemSummaryFormatMap.put("text", itemSummaryText);
                                            //itemSummaryFormatMap.put("no", i);

                                            String formattedItemSummaryText = MustacheUtil.render(abnormalSummaryItemFormat, itemSummaryFormatMap);
                                            //使用智能标点处理器处理开头的无意义标点符号
                                            if (formattedItemSummaryText != null) {
                                                formattedItemSummaryText = SmartPunctuationProcessor.cleanFormattedTextLeading(formattedItemSummaryText);
                                            }

                                            abnormalSummary.setFormat(abnormalSummaryItemFormat);
                                            abnormalSummary.setTitle(title);
                                            abnormalSummary.setText(itemSummaryText);
                                            abnormalSummary.setSummaryText(formattedItemSummaryText);
                                            abnormalSummary.setItemSummaryTextList(Collections.singletonList(itemSummaryText));
                                            // 将关联的小项列表和大项列表添加到 AbnormalSummary 中
                                            abnormalSummary.setRelatedItemResults(null);
                                            abnormalSummary.setRelatedItemGroupList(Collections.singletonList(itemGroup));
                                            //abnormalSummary.setSummary(new GroupSummaryBean(itemName, itemSummaryText));
                                            summaryList.add(abnormalSummary);
                                        }
                                    }
                                }
                            } else {
                                String itemSummaryText = StringUtils.isNotBlank(summaryFormat) ? MustacheUtil.render(summaryFormat, map) : conclusion;
                                if (StringUtils.isNotBlank(itemSummaryText)) {
                                    itemSummaryText = SmartPunctuationProcessor.normalize(itemSummaryText);

                                    // Create a separate SummaryBean for each item
                                    AbnormalSummary abnormalSummary = new AbnormalSummary();
                                    String title = itemGroup.getItemGroupName();
                                    Map<String, Object> itemSummaryFormatMap = new HashMap<>();

                                    abnormalSummaryItemFormat = StringUtils.isNotBlank(abnormalSummaryBean.getFormat()) ? abnormalSummaryBean.getFormat() : abnormalSummaryItemFormat;
                                    itemSummaryFormatMap.put("title", itemName);
                                    itemSummaryFormatMap.put("text", itemSummaryText);
                                    //itemSummaryFormatMap.put("no", i);

                                    String formattedItemSummaryText = MustacheUtil.render(abnormalSummaryItemFormat, itemSummaryFormatMap);
                                    //使用智能标点处理器处理开头的无意义标点符号
                                    if (formattedItemSummaryText != null) {
                                        formattedItemSummaryText = SmartPunctuationProcessor.cleanFormattedTextLeading(formattedItemSummaryText);
                                    }

                                    abnormalSummary.setFormat(abnormalSummaryItemFormat);
                                    abnormalSummary.setTitle(title);
                                    abnormalSummary.setText(itemSummaryText);
                                    abnormalSummary.setSummaryText(formattedItemSummaryText);
                                    abnormalSummary.setItemSummaryTextList(Collections.singletonList(itemSummaryText));
                                    // 将关联的小项列表和大项列表添加到 AbnormalSummary 中
                                    abnormalSummary.setRelatedItemResults(relatedItemResults);
                                    abnormalSummary.setRelatedItemGroupList(Collections.singletonList(itemGroup));
                                    //abnormalSummary.setSummary(new GroupSummaryBean(itemName, itemSummaryText));
                                    summaryList.add(abnormalSummary);
                                }
                            }
                        }
                    } else {
                        // Handle groups with no results
                        AbnormalSummary abnormalSummary = new AbnormalSummary();
                        String title = itemGroup.getItemGroupName();
                        Map<String, Object> itemSummaryFormatMap = new HashMap<>();
                        itemSummaryFormatMap.put("title", title);
                        itemSummaryFormatMap.put("text", itemGroup.getCheckConclusion());
                        //itemSummaryFormatMap.put("no", i);
                        abnormalSummaryItemFormat = StringUtils.isNotBlank(abnormalSummaryBean.getFormat()) ? abnormalSummaryBean.getFormat() : abnormalSummaryItemFormat;
                        String formattedItemSummaryText = MustacheUtil.render(abnormalSummaryItemFormat, itemSummaryFormatMap);

                        abnormalSummary.setFormat(abnormalSummaryItemFormat);
                        abnormalSummary.setTitle(title);
                        abnormalSummary.setText(itemGroup.getCheckConclusion());
                        abnormalSummary.setSummaryText(formattedItemSummaryText);
                        abnormalSummary.setItemSummaryTextList(Collections.singletonList(itemGroup.getCheckConclusion()));
                        // 将关联的小项列表和大项列表添加到 AbnormalSummary 中
                        abnormalSummary.setRelatedItemResults(new ArrayList<>());
                        abnormalSummary.setRelatedItemGroupList(Collections.singletonList(itemGroup));
                        summaryList.add(abnormalSummary);
                    }
                }
            }
        }

        String abnormalSummaryFormat = sysSettingService.getValueByCode("abnormalSummaryFormat");
        if (StringUtils.isBlank(abnormalSummaryFormat)) {
            abnormalSummaryFormat = "{{no}}、{{{text}}}";
        } else {
            //将No或者NO替换为小写no
            abnormalSummaryFormat = abnormalSummaryFormat.replace("No", "no").replace("NO", "no");
        }
        //处理summaryList,将summaryText重复的删除,需要保持顺序
        List<AbnormalSummary> finalSummaryList = new ArrayList<>();
        summaryList.forEach(abnormalSummary -> {
            if (finalSummaryList.stream().noneMatch(summary -> StringUtils.equals(summary.getSummaryText(), abnormalSummary.getSummaryText()))) {
                finalSummaryList.add(abnormalSummary);
            }
        });

        for (int i = 0; i < finalSummaryList.size(); i++) {
            AbnormalSummary abnormalSummary = finalSummaryList.get(i);
            Map<String, Object> map = new HashMap<>();
            String trimedText;
            try {
                // 直接使用SmartPunctuationProcessor处理文本，包含换行符和标点符号的规范化
                trimedText = SmartPunctuationProcessor.normalize(abnormalSummary.getSummaryText());
            } catch (Exception e) {
                log.error("清除标点符号异常", e);
                trimedText = abnormalSummary.getSummaryText();
            }
            map.put("title", abnormalSummary.getTitle());
            map.put("text", trimedText);
            map.put("no", i + 1);
            String abnormalSummaryText = MustacheUtil.render(abnormalSummaryFormat, map);
            abnormalSummary.setSummaryText(abnormalSummaryText);
        }

        return finalSummaryList;
    }

    @Override
    public DepartSummary generateAndSaveSummary(String departmentId, CustomerReg reg) {
        List<CustomerRegItemResult> itemResultList = customerRegItemResultMapper.listByRegIdAndDepartId(reg.getId(), Collections.singletonList(departmentId));
        DepartSummary departSummary = generateSummary(departmentId, reg, itemResultList);
        String summary = departSummary.getSummary();
        String pureSummary = departSummary.getPureSummary();
        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception ignored) {
        }

        //根据departmentId和customerRegId查询是否已经存在小结
        CustomerRegDepartSummary customerRegDepartSummary = getOneByDepartmentIdAndRegId(departmentId, reg.getId());
        SysDepart depart = sysDepartMapper.selectById(departmentId);
        if (customerRegDepartSummary != null) {
            if (!StringUtils.equals(customerRegDepartSummary.getUpdateByManual(), "1")) {
                customerRegDepartSummary.setCharacterSummary(summary);
                customerRegDepartSummary.setPureSummary(pureSummary);
                if (sysUser != null) {
                    customerRegDepartSummary.setUpdateBy(sysUser.getUsername());
                }
                customerRegDepartSummary.setUpdateTime(new Date());
                customerRegDepartSummary.setAbnormalFlag(departSummary.getAbnormalFlag());
                updateById(customerRegDepartSummary);
            }
        } else {

            customerRegDepartSummary = new CustomerRegDepartSummary();
            customerRegDepartSummary.setCharacterSummary(summary);
            customerRegDepartSummary.setPureSummary(pureSummary);
            customerRegDepartSummary.setDepartmentId(departmentId);
            String departmentSummaryDoctor = depart.getDepartSummaryDoctor();
            departmentSummaryDoctor = StringUtils.isNotBlank(departmentSummaryDoctor) ? sysUserUtilService.getUsernameByUserId(departmentSummaryDoctor) : sysUser != null ? sysUser.getUsername() : "未知";

            String doctorName = sysUserUtilService.getRealnameByUserId(departmentSummaryDoctor);
            customerRegDepartSummary.setCreateBy(departmentSummaryDoctor);
            customerRegDepartSummary.setCreatorName(doctorName);
            customerRegDepartSummary.setCreateTime(new Date());
            customerRegDepartSummary.setAbnormalFlag(departSummary.getAbnormalFlag());
            customerRegDepartSummary.setDepartmentName(depart.getDepartName());
            customerRegDepartSummary.setCustomerRegId(reg.getId());
            save(customerRegDepartSummary);
        }

        //获取itemResultList中的唯一itemGroupId
        Set<String> uniqueItemGroupIds = itemResultList.stream().map(CustomerRegItemResult::getItemGroupId).collect(Collectors.toSet());
        if (!uniqueItemGroupIds.isEmpty()) {
            updateCustomerRegItemGroup(uniqueItemGroupIds, reg.getId(), ExConstants.CHECK_STATUS_已检, customerRegDepartSummary.getId(), depart);
        }

        List<CustomerRegCriticalItem> regCriticalItemList = departSummary.getCriticalItems();
        customerRegCriticalItemService.saveAtDepartmentSummary(departmentId, reg.getId(), regCriticalItemList);
        return departSummary;
    }

    public void updateCustomerRegItemGroup(Set<String> uniqueItemGroupIds, String regId, String checkStatus, String departSummaryId, SysDepart sysDepart) {

        LoginUser sysUser = null;
        try {
            sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception ignored) {
        }
        String checkDoctor = sysUser != null ? sysUser.getUsername() : "未知";
        String checkDoctorName = sysUser != null ? sysUser.getRealname() : "未知";
        String departCheckDoctor = sysDepart.getDepartCheckDoctor();
        if (StringUtils.isNotBlank(departCheckDoctor)) {
            SysUser departCheckDoctorUser = sysUserService.getById(departCheckDoctor);
            checkDoctor = departCheckDoctorUser.getUsername();
            checkDoctorName = departCheckDoctorUser.getRealname();
        }
//        departCheckDoctor = StringUtils.isNotBlank(departCheckDoctor) ? departCheckDoctor : sysUser != null ? sysUser.getUsername() : "未知";
//        String checkDoctorName = sysUserUtilService.getRealnameByUserId(departCheckDoctor);
        String checkDoctorSignPic = systemUserUtilService.getSignPicByUsername(checkDoctor);
//
//        String finalDepartCheckDoctor = departCheckDoctor;
        for (String itemGroupId : uniqueItemGroupIds) {
//        uniqueItemGroupIds.forEach(itemGroupId -> {
            jdbcTemplate.update("update customer_reg_item_group set check_doctor_code=?,check_doctor_name=?,check_doctor_sign_pic=?,report_doctor_code=?,report_doctor_name=?,report_doctor_sign_pic=?,depart_summary_id=?,check_status=? where item_group_id=? and customer_reg_id=?", checkDoctor, checkDoctorName, checkDoctorSignPic, checkDoctor, checkDoctorName, checkDoctorSignPic, departSummaryId, checkStatus, itemGroupId, regId);
        }/*);*/

        //使用Jdbctemplate的batchUpdate

        /*UpdateWrapper<CustomerRegItemGroup> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("item_group_id", uniqueItemGroupIds);
        updateWrapper.isNull("check_doctor_code");
        updateWrapper.isNull("report_doctor_code");
        updateWrapper.isNull("check_doctor_name");
        updateWrapper.isNull("report_doctor_name");

        updateWrapper.set("check_status", checkStatus);
        updateWrapper.set("check_doctor_code", checkDoctorCode);
        updateWrapper.set("check_doctor_name", checkDoctorName);
        updateWrapper.set("check_doctor_sign_pic", checkDoctorSignPic);
        updateWrapper.set("report_doctor_code", checkDoctorCode);
        updateWrapper.set("report_doctor_name", checkDoctorName);
        updateWrapper.set("report_doctor_sign_pic", checkDoctorSignPic);
        updateWrapper.set("check_time", new Date());
        updateWrapper.set("depart_summary_id", departSummaryId);

        customerRegItemGroupMapper.update(null, updateWrapper);*/
    }

    @Override
    public CustomerRegDepartSummary getOneByDepartmentIdAndRegId(String departmentId, String regId) {
        LambdaQueryWrapper<CustomerRegDepartSummary> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CustomerRegDepartSummary::getDepartmentId, departmentId);
        queryWrapper.eq(CustomerRegDepartSummary::getCustomerRegId, regId);
        queryWrapper.last("limit 1");

        return getOne(queryWrapper);
    }

    @Override
    public void updateSummary(String departmentId, String customerRegId, String characterSummary, String updateByManual) throws Exception {

        CustomerRegDepartSummary departSummary = getOneByDepartmentIdAndRegId(departmentId, customerRegId);
        if (departSummary == null) {
            throw new Exception("未找到对应的科室小结");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        departSummary.setCharacterSummary(characterSummary);
        departSummary.setUpdateBy(sysUser.getUsername());
        departSummary.setUpdateTime(new Date());
        departSummary.setUpdateByManual(updateByManual);
        updateById(departSummary);
    }

    @Override
    public DepartSummaryAndCriticalList genetateDepartSummaryAndCriticalItem(String departmentId, CustomerReg reg, List<CustomerRegItemResult> itemResultList) {

        List<CustomerRegCriticalItem> regCriticalItemList = new ArrayList<>();
        List<String> summaryList = new ArrayList<>();
        List<String> pureSummaryList = new ArrayList<>();
        //List<ItemStandard> matchedStandardList = new ArrayList<>();
        //获取复合判断
        List<ComplexDiagnosticResult> diagnosticResultList = getDiagnosticByComplex(departmentId, reg, itemResultList, null);
        List<String> itemIdUsedByComplex = new ArrayList<>();
        if (diagnosticResultList != null && !diagnosticResultList.isEmpty()) {
            for (ComplexDiagnosticResult diagnosticResult : diagnosticResultList) {
                summaryList.add(diagnosticResult.getResult());
                pureSummaryList.add(diagnosticResult.getResult());
                itemIdUsedByComplex.addAll(diagnosticResult.getRelatedItemIdList());


                if (StringUtils.equals(diagnosticResult.getSeverityDegree(), ExConstants.SEVERITY_DEGREE_A类) || StringUtils.equals(diagnosticResult.getSeverityDegree(), ExConstants.SEVERITY_DEGREE_B类)) {
                    CustomerRegCriticalItem regCriticalItem = new CustomerRegCriticalItem();
                    regCriticalItem.setCustomerRegId(reg.getId());
                    regCriticalItem.setExamNo(reg.getExamNo());
                    regCriticalItem.setSeverityDegree(diagnosticResult.getSeverityDegree());
                    regCriticalItem.setTriggerType(ExConstants.TRIGGER_TYPE_复合判断);
                    regCriticalItem.setDiagnosisComplexId(diagnosticResult.getComplexId());
                    regCriticalItem.setSource(ExConstants.TRIGGER_SOURCE_科室小结);
                    regCriticalItem.setCreateTime(new Date());
                    regCriticalItem.setCreateBy("system");
                    regCriticalItem.setDepartmentId(departmentId);

                    regCriticalItem.setBaseOn(diagnosticResult.getBaseOn());


                    List<CustomerRegItemResult> relatedItemResultList = itemResultList.stream().filter(itemResult -> diagnosticResult.getRelatedItemIdList().contains(itemResult.getItemId())).collect(Collectors.toList());
                    regCriticalItem.setComplexItemResults(relatedItemResultList);
                    //拼接关联的小项名称和结果值+单位
                    StringBuilder itemNameAndValue = new StringBuilder();
                    for (CustomerRegItemResult itemResult : relatedItemResultList) {
                        itemNameAndValue.append(itemResult.getItemName()).append(":").append(itemResult.getValue()).append(itemResult.getUnit()).append(";");
                    }

                    regCriticalItem.setItemName(diagnosticResult.getResult());
                    regCriticalItem.setItemValue(itemNameAndValue.toString());

                    regCriticalItemList.add(regCriticalItem);
                }
            }
        }

        //生成大项结论
        Set<String> itemResultAbnormalFlagSet = new HashSet<>();
        List<ItemGroupSummary> itemGroupSummaryList = new ArrayList<>();
        //从itemResultList中排除itemIdUsedByComplex,即参与复合判断的小项不再参与组合小结的生成
        List<CustomerRegItemResult> filteredItemResultList = itemResultList.stream().filter(itemResult -> !itemIdUsedByComplex.contains(itemResult.getItemId())).toList();
        if (!filteredItemResultList.isEmpty()) {
            //将filteredItemResultList按照customerRegItemResult的itemGroupId分组
            Map<String, List<CustomerRegItemResult>> itemResultGroupByItemGroupId = filteredItemResultList.stream().collect(Collectors.groupingBy(CustomerRegItemResult::getItemGroupId));
            //生成组合小结

            for (Map.Entry<String, List<CustomerRegItemResult>> entry : itemResultGroupByItemGroupId.entrySet()) {
                String groupId = entry.getKey();
                List<CustomerRegItemResult> itemResultListByItemGroupId = entry.getValue();

                int i = 0;
                List<String> itemGroupSummaryStrList = new ArrayList<>();
                for (CustomerRegItemResult itemResult : itemResultListByItemGroupId) {

                    String value = itemResult.getValue();
                    String unit = itemResult.getUnit();
                    String conclusion = XmlEscapeUtils.unescapeXml(itemResult.getCheckConclusion());
                    //conclusion = StringUtils.isNotBlank(conclusion) ? conclusion : StringUtils.trimToEmpty(itemResult.getAbnormalFlagDesc());
                    //String conclusion = itemResult.getValue()+StringUtils.trimToEmpty(itemResult.getUnit());

                    String abnormalFlag = itemResult.getAbnormalFlag();
                    String abnormalFlagDesc = StringUtils.trimToEmpty(itemResult.getAbnormalFlagDesc());
                    String criticalFlag = itemResult.getCriticalFlag();
                    String criticalDegree = itemResult.getCriticalDegree();

                    //生成危急值
                    if (StringUtils.equals(criticalDegree, ExConstants.SEVERITY_DEGREE_A类) || StringUtils.equals(criticalDegree, ExConstants.SEVERITY_DEGREE_B类)) {
                        CustomerRegCriticalItem regCriticalItem = new CustomerRegCriticalItem();
                        regCriticalItem.setCustomerRegId(reg.getId());
                        regCriticalItem.setExamNo(reg.getExamNo());
                        regCriticalItem.setSeverityDegree(criticalDegree);
                        regCriticalItem.setTriggerType(ExConstants.TRIGGER_TYPE_项目参考值);
                        regCriticalItem.setDiagnosisComplexId(null);
                        regCriticalItem.setSource(ExConstants.TRIGGER_SOURCE_科室小结);
                        regCriticalItem.setCreateTime(new Date());
                        regCriticalItem.setCreateBy("system");
                        regCriticalItem.setDepartmentId(departmentId);
                        regCriticalItem.setItemValue(value);
                        regCriticalItem.setItemResultId(itemResult.getId());
                        regCriticalItem.setItemName(itemResult.getItemName());
                        regCriticalItem.setItemGroupId(itemResult.getItemGroupId());

                        regCriticalItemList.add(regCriticalItem);
                    }

                    ItemInfo itemInfo = itemInfoMapper.selectById(itemResult.getItemId());

                    int sumableFlag = 0;
                    Integer sumableNormalvalFlag = 0;
                    String summaryFormat = "";
                    if (itemInfo != null) {
                        sumableFlag = itemInfo.getSumableFlag() == null ? 0 : itemInfo.getSumableFlag();
                        sumableNormalvalFlag = itemInfo.getSumableNormalvalFlag() == null ? 0 : itemInfo.getSumableNormalvalFlag();
                        summaryFormat = itemInfo.getSummaryFormat();
                    } else {
                        ItemGroup itemGroup = itemGroupMapper.selectById(groupId);
                        if (itemGroup != null) {
                            sumableFlag = itemGroup.getItemSumableFlag() == null ? 0 : itemGroup.getItemSumableFlag();
                            sumableNormalvalFlag = itemGroup.getItemNormalvalSumableFlag();
                            sumableNormalvalFlag = sumableNormalvalFlag == null ? 0 : sumableNormalvalFlag;
                            summaryFormat = itemGroup.getCombineSummaryFormat();
                        }
                    }

                    if (sumableFlag == 0) {
                        continue;
                    }
                    if (abnormalFlag == null || (StringUtils.equals(abnormalFlag, "0")) && sumableNormalvalFlag == 0) {
                        continue;
                    }
                    itemResultAbnormalFlagSet.add(abnormalFlag);

                    if (StringUtils.isNotBlank(summaryFormat)) {
                        Map<String, Object> values = new HashMap<>();
                        values.put("name", itemResult.getItemName());
                        values.put("value", value);
                        values.put("unit", (StringUtils.isNotBlank(unit) ? unit : ""));
                        values.put("conclusion", StringUtils.stripToEmpty(conclusion));
                        values.put("abnormalFlagDesc", abnormalFlagDesc);
                        values.put("No", i + 1);
                        String result = MustacheUtil.render(summaryFormat, values);
                        itemGroupSummaryStrList.add(result);
                        pureSummaryList.add(itemResult.getItemName() + StringUtils.trimToEmpty(abnormalFlagDesc));
                    } else {
                        itemGroupSummaryStrList.add(itemResult.getItemName() + conclusion);
                        pureSummaryList.add(itemResult.getItemName() + conclusion);
                    }
                    i++;
                }

                if (!itemGroupSummaryStrList.isEmpty()) {
                    ItemGroupSummary itemGroupSummary = new ItemGroupSummary();
                    itemGroupSummary.setName(itemResultListByItemGroupId.get(0).getItemGroupName());
                    itemGroupSummary.setConclusion(StringUtils.join(itemGroupSummaryStrList, "；"));
                    itemGroupSummaryList.add(itemGroupSummary);
                }
            }
        }

        //根据组合小结生成科室小结
        SysDepart depart = sysDepartMapper.selectById(departmentId);
        if (depart != null) {
            String departSummaryFormat = depart.getSumFormat();
            String valueFormat = "";
            String numFormat = "";
            if (StringUtils.contains(departSummaryFormat, "{{No}}")) {
                String target = "{{";
                int firstIndex = departSummaryFormat.indexOf(target);
                int secondIndex = departSummaryFormat.indexOf(target, firstIndex + target.length());
                numFormat = departSummaryFormat.substring(0, secondIndex);

                //valueFormat 为去掉{{No}}的部分
                valueFormat = departSummaryFormat.substring(secondIndex);
            } else {
                valueFormat = departSummaryFormat;
            }
            if (StringUtils.isNotBlank(valueFormat)) {
                for (ItemGroupSummary itemGroupSummary : itemGroupSummaryList) {
                    Map<String, Object> values = new HashMap<>();
                    values.put("name", itemGroupSummary.getName());
                    values.put("conclusion", itemGroupSummary.getConclusion());
                    String result = MustacheUtil.render(valueFormat, values);
                    summaryList.add(result);
                    //pureSummaryList.add(itemGroupSummary.getConclusion());
                }
            } else {
                for (ItemGroupSummary itemGroupSummary : itemGroupSummaryList) {
                    summaryList.add(itemGroupSummary.getName() + "：" + itemGroupSummary.getConclusion());
                    //pureSummaryList.add(itemGroupSummary.getConclusion());
                }
            }

            if (StringUtils.isNotBlank(numFormat)) {
                //将summaryList中的每一项都加上序号
                for (int i = 0; i < summaryList.size(); i++) {
                    Map<String, Object> values = new HashMap<>();
                    values.put("No", i + 1);
                    String num = MustacheUtil.render(numFormat, values);
                    summaryList.set(i, num + summaryList.get(i));
                }
            }
        }

        DepartSummaryAndCriticalList departSummaryAndCriticalList = new DepartSummaryAndCriticalList();
        departSummaryAndCriticalList.setSummaryList(summaryList);
        departSummaryAndCriticalList.setCriticalItemList(regCriticalItemList);
        departSummaryAndCriticalList.setPureSummaryList(pureSummaryList);
        String summaryAbnormalFlag = itemResultAbnormalFlagSet.contains("1") ? "1" : "0";
        departSummaryAndCriticalList.setSummaryAbnormalFlag(summaryAbnormalFlag);
        //departSummaryAndCriticalListA.setMatchedStandardList(matchedStandardList);

        return departSummaryAndCriticalList;
    }

    @Override
    public List<CustomerRegCriticalItem> generateCriticalItem(String departmentId, CustomerReg reg, List<CustomerRegItemResult> itemResultList) {

        DepartSummaryAndCriticalList departSummaryAndCriticalListA = genetateDepartSummaryAndCriticalItem(departmentId, reg, itemResultList);

        return departSummaryAndCriticalListA.getCriticalItemList();
    }

    @Override
    public List<CustomerRegDepartSummary> listByCustomerReg(String customerRegId, String abnormalFlag) {
        //检查是否所有科室都已经有了科室小结
        List<String> allDepartIdList = jdbcTemplate.queryForList("select distinct(department_id) from customer_reg_item_group where abandon_flag=0 and (refund_fee_record_id is null or refund_fee_record_id = '') and add_minus_flag !=-1 and customer_reg_id=?", String.class, customerRegId);
       /* List<String> departIdInDepartSummaryList = jdbcTemplate.queryForList("select department_id from customer_reg_depart_summary where customer_reg_id=?", String.class, customerRegId);
        //获取未生成小结的科室
        List<String> notSummaryDepartIdList = allDepartIdList.stream().filter(departId -> !departIdInDepartSummaryList.contains(departId)).toList();
        CustomerReg reg = customerRegMapper.selectById(customerRegId);
        if (!notSummaryDepartIdList.isEmpty()) {
            notSummaryDepartIdList.forEach(departId -> {
                SysDepart sysDepart = sysDepartMapper.selectById(departId);
                if (sysDepart != null && StringUtils.equals(sysDepart.getAutoSummary(), "1")) {
                    generateAndSaveSummary(departId, reg);
                }
            });
        }*/

        CustomerReg reg = customerRegMapper.selectById(customerRegId);
        allDepartIdList.forEach(departId -> {
            SysDepart sysDepart = sysDepartMapper.selectById(departId);
            if (sysDepart != null && StringUtils.equals(sysDepart.getAutoSummary(), "1")) {
                generateAndSaveSummary(departId, reg);
            }
        });
        return regDepartSummaryMapper.listByCustomerReg(customerRegId, abnormalFlag);
    }

    @Override
    public boolean isSummaryAudited(String customerRegId) {
        String summaryStatus = null;
        try {
            summaryStatus = jdbcTemplate.queryForObject("select status from customer_reg_summary where customer_reg_id=? limit 1", String.class, customerRegId);
        } catch (Exception ignored) {
        }

        return StringUtils.equals(summaryStatus, ExConstants.SUMMARY_STATUS_审核通过);
        //return StringUtils.isNotBlank(summaryStatus);
    }

    @Override
    public void doAutoGenerateDepartSummaryJob() throws Exception {

        //1、找到未生成科室小结的检查单

        //3、判断其所有大项是否都已检查或放弃

        //4、生成科室小结，并将状态改为已小结
    }


    public static void main(String[] args) {
        String expression = "{{No}}{{name}}：{{conclusion}}";
        //获取第二个{{前的字符
        String target = "{{";
        int firstIndex = expression.indexOf(target);
        int secondIndex = expression.indexOf(target, firstIndex + target.length());
        String result = expression.substring(0, secondIndex);

        String valueFormat = expression.substring(secondIndex);
        System.out.println(result); // Outputs: ({{No}}
        System.out.println(valueFormat); // Outputs: {{name}}：{{conclusion}

    }
}
