package org.jeecg.modules.fee.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 体检限额操作记录
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Data
@TableName("limit_operation_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="limit_operation_record对象", description="体检限额操作记录")
public class LimitOperationRecord implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**登记记录ID*/
	@Excel(name = "登记记录ID", width = 15)
    @ApiModelProperty(value = "登记记录ID")
    private java.lang.String customerRegId;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**体检号*/
	@Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;
	/**金额*/
	@Excel(name = "交易金额", width = 15)
    @ApiModelProperty(value = "交易金额")
    private java.math.BigDecimal amount;
	/**操作类型*/
	@Excel(name = "操作类型", width = 15)
    @ApiModelProperty(value = "操作类型")
    private java.lang.String operation;
	/**目标ID*/
	@Excel(name = "目标ID", width = 15)
    @ApiModelProperty(value = "目标ID")
    private java.lang.String targetId;
	/**目标类型*/
	@Excel(name = "目标类型", width = 15)
    @ApiModelProperty(value = "目标类型")
    private java.lang.String targetType;
	/**受让人*/
	@Excel(name = "受让人", width = 15)
    @ApiModelProperty(value = "受让人")
    private java.lang.String targetName;
	/**受让体检号*/
	@Excel(name = "受让体检号", width = 15)
    @ApiModelProperty(value = "受让体检号")
    private java.lang.String targetExamNo;
	/**转入卡号*/
	@Excel(name = "转入卡号", width = 15)
    @ApiModelProperty(value = "转入卡号")
    private java.lang.String tagertCardNo;
	/**签字图片*/
	@Excel(name = "签字图片", width = 15)
    @ApiModelProperty(value = "签字图片")
    private java.lang.String signPic;
    /**限额凭证图片*/
    @Excel(name = "限额凭证图片", width = 15)
    @ApiModelProperty(value = "限额凭证图片")
    private java.lang.String limitPic;
    /**受让人*/
    @Excel(name = "转让人", width = 15)
    @ApiModelProperty(value = "转让人")
    private java.lang.String transferName;
    /**受让体检号*/
    @Excel(name = "转让体检号", width = 15)
    @ApiModelProperty(value = "转让体检号")
    private java.lang.String transferExamNo;
    /**
     * 业务id
     */
    private String bizId;

    private String limitId;

    private String transferLimitId;

    private String businessDesc;
}
