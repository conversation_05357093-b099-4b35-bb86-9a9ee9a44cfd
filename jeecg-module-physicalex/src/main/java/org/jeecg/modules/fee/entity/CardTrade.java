package org.jeecg.modules.fee.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: card_trade
 * @Author: jeecg-boot
 * @Date:   2024-07-14
 * @Version: V1.0
 */
@Data
@TableName("card_trade")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="card_trade对象", description="card_trade")
public class CardTrade implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**卡ID*/
	@Excel(name = "卡ID", width = 15)
    @ApiModelProperty(value = "卡ID")
    private java.lang.String cardId;
	/**卡号*/
	@Excel(name = "卡号", width = 15)
    @ApiModelProperty(value = "卡号")
    private java.lang.String cardNo;
	/**卡类别*/
	@Excel(name = "卡类别", width = 15)
    @ApiModelProperty(value = "卡类别")
    private java.lang.String category;
	/**交易类型：收入，支出*/
	@Excel(name = "交易类型：收入，支出", width = 15)
    @ApiModelProperty(value = "交易类型：收入，支出")
    private java.lang.String tradeType;
	/**交易金额*/
	@Excel(name = "交易金额", width = 15)
    @ApiModelProperty(value = "交易金额")
    private java.math.BigDecimal amount;
	/**实际金额*/
	@Excel(name = "实际金额", width = 15)
    @ApiModelProperty(value = "实际金额")
    private java.math.BigDecimal amountActual;
	/**折扣率*/
	@Excel(name = "折扣率", width = 15)
    @ApiModelProperty(value = "折扣率")
    private java.math.BigDecimal disRate;
	/**余额*/
	@Excel(name = "余额", width = 15)
    @ApiModelProperty(value = "余额")
    private java.math.BigDecimal blance;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private java.lang.String status;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**bizId*/
	@Excel(name = "bizId", width = 15)
    @ApiModelProperty(value = "bizId")
    private java.lang.String bizId;
    @TableField(exist = false)
    private String name;
    @TableField(exist = false)
    private String examNo;
}
