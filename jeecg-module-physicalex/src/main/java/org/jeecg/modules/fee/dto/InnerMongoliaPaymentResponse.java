package org.jeecg.modules.fee.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class InnerMongoliaPaymentResponse {
    
    @JsonProperty("msgCode")
    private String msgCode;
    private String msg;
    private String charset;
    @JsonProperty("signType")
    private String signType;
    private String sign;
    private Object data;
    
    @Data
    public static class UnifiedOrderData {
        @JsonProperty("payInfo")
        private String payInfo;
        @JsonProperty("insurancePayResult")
        private String insurancePayResult;
        @JsonProperty("payAppid")
        private String payAppid;
    }
    
    @Data
    public static class BarcodePayData {
        private String code;
        private String msg;
        @JsonProperty("payWayType")
        private Integer payWayType;
        @JsonProperty("polymerizationNo")
        private String polymerizationNo;
        @JsonProperty("tradeNo")
        private String tradeNo;
    }
    
    @Data
    public static class QueryData {
        private String code;
        private String msg;
        @JsonProperty("payWayType")
        private Integer payWayType;
        @JsonProperty("polymerizationNo")
        private String polymerizationNo;
        @JsonProperty("tradeNo")
        private String tradeNo;
    }
    
    @Data
    public static class RefundData {
        private String code;
        private String msg;
        @JsonProperty("outRefundNo")
        private String outRefundNo;
        @JsonProperty("tradeNo")
        private String tradeNo;
    }
    
    @Data
    public static class CancelData {
        private String code;
        private String msg;
        @JsonProperty("batchNo")
        private String batchNo;
    }
}