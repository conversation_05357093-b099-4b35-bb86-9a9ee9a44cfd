package org.jeecg.modules.fee.gateway.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Map;

@Data
public class PaymentRequest {
    
    private String orderNo;
    
    private BigDecimal amount;
    
    private String subject;
    
    private String body;
    
    private String wayCode;
    
    private String authCode;
    
    private String clientIp;
    
    private String notifyUrl;
    
    private String returnUrl;
    
    private String attach;
    
    private PaymentType paymentType;
    
    private PatientInfo patientInfo;
    
    private OperatorInfo operatorInfo;
    
    private Map<String, Object> extras;
    
    public enum PaymentType {
        ONLINE("在线支付"),
        QR_CODE("扫码支付"),
        BARCODE("条码支付"),
        H5("H5支付"),
        JSAPI("公众号支付"),
        MINI_PROGRAM("小程序支付");
        
        private final String description;
        
        PaymentType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    @Data
    public static class PatientInfo {
        private String patientName;
        private String idCard;
        private String phone;
        private String cardNo;
        private String clinicCode;
        private String inpatientNo;
        private String deptName;
        private String doctorName;
        private String markType;
        private String markNo;
    }
    
    @Data
    public static class OperatorInfo {
        private String operatorName;
        private String operatorCode;
        private String terminalId;
    }
}