package org.jeecg.modules.fee.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: team_customer_limit_amount
 * @Author: jeecg-boot
 * @Date:   2025-04-12
 * @Version: V1.0
 */
@Data
@TableName("team_customer_limit_amount")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="team_customer_limit_amount对象", description="team_customer_limit_amount")
public class TeamCustomerLimitAmount implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**档案id*/
	@Excel(name = "档案id", width = 15)
    @ApiModelProperty(value = "档案id")
    private java.lang.String customerId;
	/**分组id*/
	@Excel(name = "分组id", width = 15)
    @ApiModelProperty(value = "分组id")
    private java.lang.String teamId;
	/**分组名称*/
	@Excel(name = "分组名称", width = 15)
    @ApiModelProperty(value = "分组名称")
    private java.lang.String teamName;
	/**身份证号*/
	@Excel(name = "身份证号", width = 15)
    @ApiModelProperty(value = "身份证号")
    private java.lang.String idCard;
	/**姓名*/
	@Excel(name = "姓名", width = 15)
    @ApiModelProperty(value = "姓名")
    private java.lang.String name;
	/**单位预约id*/
	@Excel(name = "单位预约id", width = 15)
    @ApiModelProperty(value = "单位预约id")
    private java.lang.String companyRegId;
	/**单位预约名称*/
	@Excel(name = "单位预约名称", width = 15)
    @ApiModelProperty(value = "单位预约名称")
    private java.lang.String companyRegName;
	/**单位名称*/
	@Excel(name = "单位名称", width = 15)
    @ApiModelProperty(value = "单位名称")
    private java.lang.String companyName;
	/**金额*/
	@Excel(name = "金额", width = 15)
    @ApiModelProperty(value = "金额")
    private java.math.BigDecimal amount;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    @ApiModelProperty(value = "状态")
    private java.lang.String enableFlag;

    @TableField(exist = false)
    private BigDecimal teamLimitAmount;
    @TableField(exist = false)
    private List<LimitOperationRecord> limitRecordList;
}
