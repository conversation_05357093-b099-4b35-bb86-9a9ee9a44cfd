package org.jeecg.modules.fee.gateway.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class QueryResponse {
    
    private boolean success;
    
    private String code;
    
    private String message;
    
    private String orderNo;
    
    private String channelOrderNo;
    
    private String tradeNo;
    
    private BigDecimal amount;
    
    private PaymentResponse.PaymentStatus status;
    
    private String payWayType;
    
    private Date createTime;
    
    private Date successTime;
    
    private Object rawResponse;
    
    public static QueryResponse success(String orderNo, PaymentResponse.PaymentStatus status) {
        QueryResponse response = new QueryResponse();
        response.setSuccess(true);
        response.setCode("SUCCESS");
        response.setMessage("查询成功");
        response.setOrderNo(orderNo);
        response.setStatus(status);
        return response;
    }
    
    public static QueryResponse error(String code, String message) {
        QueryResponse response = new QueryResponse();
        response.setSuccess(false);
        response.setCode(code);
        response.setMessage(message);
        return response;
    }
}