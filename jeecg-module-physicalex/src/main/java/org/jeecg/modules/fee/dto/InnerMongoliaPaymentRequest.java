package org.jeecg.modules.fee.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class InnerMongoliaPaymentRequest {
    
    private String charset = "UTF-8";
    private String method;
    @JsonProperty("reqDate")
    private String reqDate;
    @JsonProperty("appId")
    private String appId;
    @JsonProperty("bizContent")
    private String bizContent;
    private String sign;
    @JsonProperty("signType")
    private String signType = "RSA";
    private String extras;
    private String version = "1.0";
    
    @Data
    public static class UnifiedOrderBizContent {
        @JsonProperty("orderNo")
        private String orderNo;
        @JsonProperty("orderType")
        private String orderType;
        @JsonProperty("payWayType")
        private String payWayType;
        private String amount;
        @JsonProperty("notifyUrl")
        private String notifyUrl;
        private String attach;
        @JsonProperty("spbillCreateIp")
        private String spbillCreateIp;
        @JsonProperty("hisBiz")
        private HisBiz hisBiz;
        @JsonProperty("insuranceParam")
        private Object insuranceParam;
    }
    
    @Data
    public static class BarcodePayBizContent {
        @JsonProperty("orderNo")
        private String orderNo;
        @JsonProperty("payWayType")
        private Integer payWayType;
        @JsonProperty("orderType")
        private Integer orderType;
        @JsonProperty("authCode")
        private String authCode;
        private String subject;
        @JsonProperty("totalAmount")
        private Double totalAmount;
        @JsonProperty("operatorName")
        private String operatorName;
        @JsonProperty("operatorCode")
        private String operatorCode;
        @JsonProperty("terminalId")
        private String terminalId;
        @JsonProperty("hisBiz")
        private HisBiz hisBiz;
    }
    
    @Data
    public static class QueryBizContent {
        @JsonProperty("orderNo")
        private String orderNo;
    }
    
    @Data
    public static class RefundBizContent {
        @JsonProperty("orderNo")
        private String orderNo;
        @JsonProperty("batchNo")
        private String batchNo;
        @JsonProperty("refundFee")
        private String refundFee;
        @JsonProperty("tradeDesc")
        private String tradeDesc;
        @JsonProperty("notifyUrl")
        private String notifyUrl;
        @JsonProperty("operatorName")
        private String operatorName;
        @JsonProperty("operatorCode")
        private String operatorCode;
    }
    
    @Data
    public static class CancelBizContent {
        @JsonProperty("orderNo")
        private String orderNo;
    }
    
    @Data
    public static class HisBiz {
        @JsonProperty("clinicCode")
        private String clinicCode;
        @JsonProperty("recipeNos")
        private String recipeNos;
        @JsonProperty("inpatientNo")
        private String inpatientNo;
        @JsonProperty("inpatientSeriNo")
        private String inpatientSeriNo;
        @JsonProperty("markType")
        private String markType;
        @JsonProperty("markNo")
        private String markNo;
        @JsonProperty("patientName")
        private String patientName;
        @JsonProperty("IDCard")
        private String IDCard;
        @JsonProperty("patientPhone")
        private String patientPhone;
        @JsonProperty("cardNo")
        private String cardNo;
        @JsonProperty("deptName")
        private String deptName;
        @JsonProperty("doctorName")
        private String doctorName;
    }
}