package org.jeecg.modules.fee.gateway.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class RefundResponse {
    
    private boolean success;
    
    private String code;
    
    private String message;
    
    private String orderNo;
    
    private String refundOrderNo;
    
    private String channelOrderNo;
    
    private String tradeNo;
    
    private BigDecimal refundAmount;
    
    private RefundStatus status;
    
    private Date createTime;
    
    private Date successTime;
    
    private Object rawResponse;
    
    public enum RefundStatus {
        CREATED("退款订单生成"),
        PROCESSING("退款中"),
        SUCCESS("退款成功"),
        FAILED("退款失败"),
        CLOSED("退款任务关闭");
        
        private final String description;
        
        RefundStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public static RefundResponse success(String orderNo, String refundOrderNo, BigDecimal refundAmount) {
        RefundResponse response = new RefundResponse();
        response.setSuccess(true);
        response.setCode("SUCCESS");
        response.setMessage("退款成功");
        response.setOrderNo(orderNo);
        response.setRefundOrderNo(refundOrderNo);
        response.setRefundAmount(refundAmount);
        response.setStatus(RefundStatus.SUCCESS);
        return response;
    }
    
    public static RefundResponse error(String code, String message) {
        RefundResponse response = new RefundResponse();
        response.setSuccess(false);
        response.setCode(code);
        response.setMessage(message);
        response.setStatus(RefundStatus.FAILED);
        return response;
    }
}