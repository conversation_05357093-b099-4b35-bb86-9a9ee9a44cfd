package org.jeecg.modules.fee.gateway.service;

import org.jeecg.modules.fee.gateway.dto.*;
import org.jeecg.modules.fee.gateway.enums.PaymentGatewayType;

public interface IPaymentGatewayService {
    
    PaymentResponse createPayment(PaymentRequest request);
    
    PaymentResponse createPayment(PaymentRequest request, PaymentGatewayType gatewayType);
    
    PaymentResponse createQrCodePayment(PaymentRequest request);
    
    PaymentResponse createQrCodePayment(PaymentRequest request, PaymentGatewayType gatewayType);
    
    PaymentResponse createBarcodePayment(PaymentRequest request);
    
    PaymentResponse createBarcodePayment(PaymentRequest request, PaymentGatewayType gatewayType);
    
    QueryResponse queryPayment(QueryRequest request);
    
    QueryResponse queryPayment(QueryRequest request, PaymentGatewayType gatewayType);
    
    RefundResponse refundPayment(RefundRequest request);
    
    RefundResponse refundPayment(RefundRequest request, PaymentGatewayType gatewayType);
    
    CancelResponse cancelPayment(CancelRequest request);
    
    CancelResponse cancelPayment(CancelRequest request, PaymentGatewayType gatewayType);
    
    boolean verifyNotifySign(String notifyData, PaymentGatewayType gatewayType);
    
    boolean isOrderCompleted(String orderNo);
    
    boolean isOrderCompleted(String orderNo, PaymentGatewayType gatewayType);
}