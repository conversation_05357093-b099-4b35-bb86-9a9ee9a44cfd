package org.jeecg.modules.fee.gateway.dto;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class RefundRequest {
    
    private String orderNo;
    
    private String refundOrderNo;
    
    private String batchNo;
    
    private BigDecimal refundAmount;
    
    private BigDecimal totalAmount;
    
    private String reason;
    
    private String notifyUrl;
    
    private PaymentRequest.OperatorInfo operatorInfo;
    
    public RefundRequest() {}
    
    public RefundRequest(String orderNo, String refundOrderNo, BigDecimal refundAmount) {
        this.orderNo = orderNo;
        this.refundOrderNo = refundOrderNo;
        this.refundAmount = refundAmount;
    }
}