package org.jeecg.modules.fee.gateway.exception;

public class PaymentGatewayException extends RuntimeException {
    
    private String code;
    
    public PaymentGatewayException(String message) {
        super(message);
        this.code = "GATEWAY_ERROR";
    }
    
    public PaymentGatewayException(String code, String message) {
        super(message);
        this.code = code;
    }
    
    public PaymentGatewayException(String message, Throwable cause) {
        super(message, cause);
        this.code = "GATEWAY_ERROR";
    }
    
    public PaymentGatewayException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
    
    public String getCode() {
        return code;
    }
}