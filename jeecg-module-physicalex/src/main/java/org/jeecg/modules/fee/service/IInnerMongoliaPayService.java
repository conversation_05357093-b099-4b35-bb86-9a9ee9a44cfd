package org.jeecg.modules.fee.service;

import org.jeecg.modules.fee.dto.InnerMongoliaPaymentRequest;
import org.jeecg.modules.fee.dto.InnerMongoliaPaymentResponse;

public interface IInnerMongoliaPayService {
    
    InnerMongoliaPaymentResponse createUnifiedOrder(InnerMongoliaPaymentRequest.UnifiedOrderBizContent bizContent);
    
    InnerMongoliaPaymentResponse createBarcodePayment(InnerMongoliaPaymentRequest.BarcodePayBizContent bizContent);
    
    InnerMongoliaPaymentResponse queryPayment(String orderNo);
    
    InnerMongoliaPaymentResponse refundPayment(InnerMongoliaPaymentRequest.RefundBizContent bizContent);
    
    InnerMongoliaPaymentResponse cancelPayment(String orderNo);
}