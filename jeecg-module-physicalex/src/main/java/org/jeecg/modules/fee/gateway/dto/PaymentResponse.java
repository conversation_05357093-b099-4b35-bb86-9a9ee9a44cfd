package org.jeecg.modules.fee.gateway.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class PaymentResponse {
    
    private boolean success;
    
    private String code;
    
    private String message;
    
    private String orderNo;
    
    private String channelOrderNo;
    
    private String tradeNo;
    
    private String payInfo;
    
    private String qrCode;
    
    private BigDecimal amount;
    
    private PaymentStatus status;
    
    private String payWayType;
    
    private Date createTime;
    
    private Date successTime;
    
    private Object rawResponse;
    
    public enum PaymentStatus {
        CREATED("订单生成"),
        PAYING("支付中"),
        SUCCESS("支付成功"),
        FAILED("支付失败"),
        CANCELLED("已撤销"),
        REFUNDED("已退款"),
        CLOSED("订单关闭");
        
        private final String description;
        
        PaymentStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public static PaymentResponse success(String orderNo, String channelOrderNo) {
        PaymentResponse response = new PaymentResponse();
        response.setSuccess(true);
        response.setCode("SUCCESS");
        response.setMessage("操作成功");
        response.setOrderNo(orderNo);
        response.setChannelOrderNo(channelOrderNo);
        response.setStatus(PaymentStatus.SUCCESS);
        return response;
    }
    
    public static PaymentResponse error(String code, String message) {
        PaymentResponse response = new PaymentResponse();
        response.setSuccess(false);
        response.setCode(code);
        response.setMessage(message);
        response.setStatus(PaymentStatus.FAILED);
        return response;
    }
}