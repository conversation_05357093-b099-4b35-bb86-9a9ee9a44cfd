package org.jeecg.modules.fee.util;

import lombok.extern.slf4j.Slf4j;
import javax.crypto.Cipher;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class InnerMongoliaSignUtil {
    
    private static final String ALGORITHM = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA1withRSA";
    
    public static String createSign(Map<String, Object> params, String privateKey) {
        try {
            String sortedParams = createLinkString(params);
            log.info("签名原始字符串: {}", sortedParams);
            
            return sign(sortedParams, privateKey);
        } catch (Exception e) {
            log.error("签名失败", e);
            throw new RuntimeException("签名失败", e);
        }
    }
    
    public static boolean verifySign(Map<String, Object> params, String publicKey, String sign) {
        try {
            String sortedParams = createLinkString(params);
            log.info("验签原始字符串: {}", sortedParams);
            
            return verify(sortedParams, publicKey, sign);
        } catch (Exception e) {
            log.error("验签失败", e);
            return false;
        }
    }
    
    private static String createLinkString(Map<String, Object> params) {
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        
        return keys.stream()
                .filter(key -> params.get(key) != null && !"".equals(params.get(key).toString()) && !"sign".equals(key))
                .map(key -> key + "=" + params.get(key))
                .collect(Collectors.joining("&"));
    }
    
    private static String sign(String content, String privateKey) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PrivateKey priKey = keyFactory.generatePrivate(keySpec);
        
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        
        byte[] signed = signature.sign();
        return Base64.getEncoder().encodeToString(signed);
    }
    
    private static boolean verify(String content, String publicKey, String sign) throws Exception {
        byte[] keyBytes = Base64.getDecoder().decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);
        
        Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
        signature.initVerify(pubKey);
        signature.update(content.getBytes(StandardCharsets.UTF_8));
        
        return signature.verify(Base64.getDecoder().decode(sign));
    }
}