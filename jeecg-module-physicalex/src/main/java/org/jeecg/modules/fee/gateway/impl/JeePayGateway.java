package org.jeecg.modules.fee.gateway.impl;

import com.jeequan.jeepay.Jeepay;
import com.jeequan.jeepay.exception.JeepayException;
import com.jeequan.jeepay.model.PayOrderCreateRequest;
import com.jeequan.jeepay.model.PayOrderCreateReqModel;
import com.jeequan.jeepay.model.PayOrderQueryRequest;
import com.jeequan.jeepay.model.RefundOrderCreateRequest;
import com.jeequan.jeepay.model.RefundOrderCreateReqModel;
import com.jeequan.jeepay.service.JeepayClient;
import com.jeequan.jeepay.util.JeepayKit;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.fee.bo.PayConfig;
import org.jeecg.modules.fee.gateway.IPaymentGateway;
import org.jeecg.modules.fee.gateway.dto.*;
import org.jeecg.modules.fee.gateway.exception.PaymentGatewayException;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Slf4j
@Component("jeePayGateway")
public class JeePayGateway implements IPaymentGateway {
    
    private PayConfig payConfig;
    private JeepayClient jeepayClient;
    
    public void init(PayConfig payConfig) {
        this.payConfig = payConfig;
        Jeepay.apiKey = payConfig.getApiKey();
        Jeepay.apiBase = payConfig.getGateWayUrl();
        this.jeepayClient = new JeepayClient(payConfig.getApiKey(), payConfig.getGateWayUrl());
    }
    
    @Override
    public String getGatewayName() {
        return "JeePay";
    }
    
    @Override
    public PaymentResponse createPayment(PaymentRequest request) {
        return switch (request.getPaymentType()) {
            case QR_CODE -> createQrCodePayment(request);
            case BARCODE -> createBarcodePayment(request);
            case JSAPI -> createJsApiPayment(request);
            case H5 -> createH5Payment(request);
            default -> createDefaultPayment(request);
        };
    }
    
    @Override
    public PaymentResponse createQrCodePayment(PaymentRequest request) {
        try {
            PayOrderCreateRequest payRequest = new PayOrderCreateRequest();
            PayOrderCreateReqModel model = buildPayOrderModel(request);
            
            model.setWayCode("ALI_QR");
            if (request.getWayCode() != null) {
                model.setWayCode(request.getWayCode());
            }
            
            payRequest.setBizModel(model);
            
            Map<String, Object> result = jeepayClient.execute(payRequest);
            
            if ("0".equals(result.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) result.get("data");
                PaymentResponse response = new PaymentResponse();
                response.setSuccess(true);
                response.setCode("SUCCESS");
                response.setMessage("创建成功");
                response.setOrderNo(request.getOrderNo());
                response.setChannelOrderNo((String) data.get("payOrderId"));
                response.setQrCode((String) data.get("codeUrl"));
                response.setPayInfo((String) data.get("payInfo"));
                response.setAmount(request.getAmount());
                response.setStatus(PaymentResponse.PaymentStatus.CREATED);
                response.setCreateTime(new Date());
                response.setRawResponse(result);
                return response;
            } else {
                return PaymentResponse.error((String) result.get("code"), (String) result.get("msg"));
            }
        } catch (Exception e) {
            log.error("JeePay创建扫码支付失败", e);
            throw new PaymentGatewayException("JEEPAY_CREATE_ERROR", "创建扫码支付失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public PaymentResponse createBarcodePayment(PaymentRequest request) {
        try {
            PayOrderCreateRequest payRequest = new PayOrderCreateRequest();
            PayOrderCreateReqModel model = buildPayOrderModel(request);
            
            model.setWayCode(request.getWayCode() != null ? request.getWayCode() : "ALI_BAR");
            model.setAuthCode(request.getAuthCode());
            
            payRequest.setBizModel(model);
            
            Map<String, Object> result = jeepayClient.execute(payRequest);
            
            if ("0".equals(result.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) result.get("data");
                Integer state = (Integer) data.get("state");
                
                PaymentResponse response = new PaymentResponse();
                response.setOrderNo(request.getOrderNo());
                response.setChannelOrderNo((String) data.get("payOrderId"));
                response.setAmount(request.getAmount());
                response.setCreateTime(new Date());
                response.setRawResponse(result);
                
                switch (state) {
                    case 0 -> {
                        response.setSuccess(false);
                        response.setCode("CREATED");
                        response.setMessage("订单生成");
                        response.setStatus(PaymentResponse.PaymentStatus.CREATED);
                    }
                    case 1 -> {
                        response.setSuccess(false);
                        response.setCode("PAYING");
                        response.setMessage("支付中");
                        response.setStatus(PaymentResponse.PaymentStatus.PAYING);
                    }
                    case 2 -> {
                        response.setSuccess(true);
                        response.setCode("SUCCESS");
                        response.setMessage("支付成功");
                        response.setStatus(PaymentResponse.PaymentStatus.SUCCESS);
                        response.setSuccessTime(new Date());
                        response.setTradeNo((String) data.get("channelOrderNo"));
                    }
                    case 3 -> {
                        response.setSuccess(false);
                        response.setCode("FAILED");
                        response.setMessage("支付失败");
                        response.setStatus(PaymentResponse.PaymentStatus.FAILED);
                    }
                    default -> {
                        response.setSuccess(false);
                        response.setCode("UNKNOWN");
                        response.setMessage("未知状态");
                        response.setStatus(PaymentResponse.PaymentStatus.FAILED);
                    }
                }
                return response;
            } else {
                return PaymentResponse.error((String) result.get("code"), (String) result.get("msg"));
            }
        } catch (Exception e) {
            log.error("JeePay创建条码支付失败", e);
            throw new PaymentGatewayException("JEEPAY_BARCODE_ERROR", "创建条码支付失败: " + e.getMessage(), e);
        }
    }
    
    private PaymentResponse createJsApiPayment(PaymentRequest request) {
        try {
            PayOrderCreateRequest payRequest = new PayOrderCreateRequest();
            PayOrderCreateReqModel model = buildPayOrderModel(request);
            
            model.setWayCode("WX_JSAPI");
            if (request.getExtras() != null && request.getExtras().containsKey("openid")) {
                model.setChannelExtra("{\"openid\":\"" + request.getExtras().get("openid") + "\"}");
            }
            
            payRequest.setBizModel(model);
            
            Map<String, Object> result = jeepayClient.execute(payRequest);
            
            if ("0".equals(result.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) result.get("data");
                PaymentResponse response = new PaymentResponse();
                response.setSuccess(true);
                response.setCode("SUCCESS");
                response.setMessage("创建成功");
                response.setOrderNo(request.getOrderNo());
                response.setChannelOrderNo((String) data.get("payOrderId"));
                response.setPayInfo((String) data.get("payInfo"));
                response.setAmount(request.getAmount());
                response.setStatus(PaymentResponse.PaymentStatus.CREATED);
                response.setCreateTime(new Date());
                response.setRawResponse(result);
                return response;
            } else {
                return PaymentResponse.error((String) result.get("code"), (String) result.get("msg"));
            }
        } catch (Exception e) {
            log.error("JeePay创建JSAPI支付失败", e);
            throw new PaymentGatewayException("JEEPAY_JSAPI_ERROR", "创建JSAPI支付失败: " + e.getMessage(), e);
        }
    }
    
    private PaymentResponse createH5Payment(PaymentRequest request) {
        try {
            PayOrderCreateRequest payRequest = new PayOrderCreateRequest();
            PayOrderCreateReqModel model = buildPayOrderModel(request);
            
            model.setWayCode("WX_H5");
            
            payRequest.setBizModel(model);
            
            Map<String, Object> result = jeepayClient.execute(payRequest);
            
            if ("0".equals(result.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) result.get("data");
                PaymentResponse response = new PaymentResponse();
                response.setSuccess(true);
                response.setCode("SUCCESS");
                response.setMessage("创建成功");
                response.setOrderNo(request.getOrderNo());
                response.setChannelOrderNo((String) data.get("payOrderId"));
                response.setPayInfo((String) data.get("payInfo"));
                response.setAmount(request.getAmount());
                response.setStatus(PaymentResponse.PaymentStatus.CREATED);
                response.setCreateTime(new Date());
                response.setRawResponse(result);
                return response;
            } else {
                return PaymentResponse.error((String) result.get("code"), (String) result.get("msg"));
            }
        } catch (Exception e) {
            log.error("JeePay创建H5支付失败", e);
            throw new PaymentGatewayException("JEEPAY_H5_ERROR", "创建H5支付失败: " + e.getMessage(), e);
        }
    }
    
    private PaymentResponse createDefaultPayment(PaymentRequest request) {
        return createQrCodePayment(request);
    }
    
    @Override
    public QueryResponse queryPayment(QueryRequest request) {
        try {
            PayOrderQueryRequest queryRequest = new PayOrderQueryRequest();
            queryRequest.setMchNo(payConfig.getMchNo());
            queryRequest.setPayOrderId(request.getChannelOrderNo());
            queryRequest.setMchOrderNo(request.getOrderNo());
            
            Map<String, Object> result = jeepayClient.execute(queryRequest);
            
            if ("0".equals(result.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) result.get("data");
                Integer state = (Integer) data.get("state");
                
                QueryResponse response = new QueryResponse();
                response.setOrderNo(request.getOrderNo());
                response.setChannelOrderNo((String) data.get("payOrderId"));
                response.setTradeNo((String) data.get("channelOrderNo"));
                response.setAmount(new BigDecimal(data.get("amount").toString()).divide(new BigDecimal("100")));
                response.setRawResponse(result);
                
                switch (state) {
                    case 0 -> {
                        response.setSuccess(true);
                        response.setCode("CREATED");
                        response.setMessage("订单生成");
                        response.setStatus(PaymentResponse.PaymentStatus.CREATED);
                    }
                    case 1 -> {
                        response.setSuccess(true);
                        response.setCode("PAYING");
                        response.setMessage("支付中");
                        response.setStatus(PaymentResponse.PaymentStatus.PAYING);
                    }
                    case 2 -> {
                        response.setSuccess(true);
                        response.setCode("SUCCESS");
                        response.setMessage("支付成功");
                        response.setStatus(PaymentResponse.PaymentStatus.SUCCESS);
                    }
                    case 3 -> {
                        response.setSuccess(true);
                        response.setCode("FAILED");
                        response.setMessage("支付失败");
                        response.setStatus(PaymentResponse.PaymentStatus.FAILED);
                    }
                    case 4 -> {
                        response.setSuccess(true);
                        response.setCode("CANCELLED");
                        response.setMessage("已撤销");
                        response.setStatus(PaymentResponse.PaymentStatus.CANCELLED);
                    }
                    case 5 -> {
                        response.setSuccess(true);
                        response.setCode("REFUNDED");
                        response.setMessage("已退款");
                        response.setStatus(PaymentResponse.PaymentStatus.REFUNDED);
                    }
                    case 6 -> {
                        response.setSuccess(true);
                        response.setCode("CLOSED");
                        response.setMessage("订单关闭");
                        response.setStatus(PaymentResponse.PaymentStatus.CLOSED);
                    }
                    default -> {
                        response.setSuccess(false);
                        response.setCode("UNKNOWN");
                        response.setMessage("未知状态");
                        response.setStatus(PaymentResponse.PaymentStatus.FAILED);
                    }
                }
                return response;
            } else {
                return QueryResponse.error((String) result.get("code"), (String) result.get("msg"));
            }
        } catch (Exception e) {
            log.error("JeePay查询支付失败", e);
            throw new PaymentGatewayException("JEEPAY_QUERY_ERROR", "查询支付失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public RefundResponse refundPayment(RefundRequest request) {
        try {
            RefundOrderCreateRequest refundRequest = new RefundOrderCreateRequest();
            RefundOrderCreateReqModel model = new RefundOrderCreateReqModel();
            
            model.setMchNo(payConfig.getMchNo());
            model.setAppId(payConfig.getAppId());
            model.setMchOrderNo(request.getOrderNo());
            model.setMchRefundNo(request.getRefundOrderNo());
            model.setRefundAmount(request.getRefundAmount().multiply(new BigDecimal("100")).longValue());
            model.setRefundReason(request.getReason());
            model.setCurrency("CNY");
            model.setNotifyUrl(request.getNotifyUrl());
            
            refundRequest.setBizModel(model);
            
            Map<String, Object> result = jeepayClient.execute(refundRequest);
            
            if ("0".equals(result.get("code"))) {
                Map<String, Object> data = (Map<String, Object>) result.get("data");
                RefundResponse response = new RefundResponse();
                response.setSuccess(true);
                response.setCode("SUCCESS");
                response.setMessage("退款申请成功");
                response.setOrderNo(request.getOrderNo());
                response.setRefundOrderNo(request.getRefundOrderNo());
                response.setChannelOrderNo((String) data.get("refundOrderId"));
                response.setRefundAmount(request.getRefundAmount());
                response.setStatus(RefundResponse.RefundStatus.SUCCESS);
                response.setCreateTime(new Date());
                response.setRawResponse(result);
                return response;
            } else {
                return RefundResponse.error((String) result.get("code"), (String) result.get("msg"));
            }
        } catch (Exception e) {
            log.error("JeePay申请退款失败", e);
            throw new PaymentGatewayException("JEEPAY_REFUND_ERROR", "申请退款失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public CancelResponse cancelPayment(CancelRequest request) {
        return CancelResponse.error("NOT_SUPPORT", "JeePay不支持订单撤销");
    }
    
    @Override
    public boolean verifyNotifySign(String notifyData) {
        try {
            return JeepayKit.verifySign(notifyData, payConfig.getApiKey());
        } catch (Exception e) {
            log.error("JeePay验证回调签名失败", e);
            return false;
        }
    }
    
    @Override
    public boolean isOrderCompleted(String orderNo) {
        try {
            QueryResponse response = queryPayment(new QueryRequest(orderNo));
            return response.isSuccess() && (
                response.getStatus() == PaymentResponse.PaymentStatus.SUCCESS ||
                response.getStatus() == PaymentResponse.PaymentStatus.REFUNDED
            );
        } catch (Exception e) {
            log.error("JeePay查询订单完成状态失败", e);
            return false;
        }
    }
    
    private PayOrderCreateReqModel buildPayOrderModel(PaymentRequest request) {
        PayOrderCreateReqModel model = new PayOrderCreateReqModel();
        model.setMchNo(payConfig.getMchNo());
        model.setAppId(payConfig.getAppId());
        model.setMchOrderNo(request.getOrderNo());
        model.setAmount(request.getAmount().multiply(new BigDecimal("100")).longValue());
        model.setCurrency("CNY");
        model.setSubject(request.getSubject());
        model.setBody(request.getBody());
        model.setClientIp(request.getClientIp() != null ? request.getClientIp() : payConfig.getGateWayPublicIp());
        model.setNotifyUrl(request.getNotifyUrl());
        model.setReturnUrl(request.getReturnUrl());
        
        if (request.getAttach() != null) {
            model.setExtParam(request.getAttach());
        }
        
        return model;
    }
}