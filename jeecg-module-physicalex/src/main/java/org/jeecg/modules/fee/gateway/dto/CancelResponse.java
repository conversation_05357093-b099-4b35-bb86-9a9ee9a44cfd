package org.jeecg.modules.fee.gateway.dto;

import lombok.Data;
import java.util.Date;

@Data
public class CancelResponse {
    
    private boolean success;
    
    private String code;
    
    private String message;
    
    private String orderNo;
    
    private String channelOrderNo;
    
    private String batchNo;
    
    private CancelStatus status;
    
    private Date cancelTime;
    
    private Object rawResponse;
    
    public enum CancelStatus {
        SUCCESS("撤销成功"),
        FAILED("撤销失败"),
        NOT_SUPPORT("不支持撤销");
        
        private final String description;
        
        CancelStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    public static CancelResponse success(String orderNo) {
        CancelResponse response = new CancelResponse();
        response.setSuccess(true);
        response.setCode("SUCCESS");
        response.setMessage("撤销成功");
        response.setOrderNo(orderNo);
        response.setStatus(CancelStatus.SUCCESS);
        response.setCancelTime(new Date());
        return response;
    }
    
    public static CancelResponse error(String code, String message) {
        CancelResponse response = new CancelResponse();
        response.setSuccess(false);
        response.setCode(code);
        response.setMessage(message);
        response.setStatus(CancelStatus.FAILED);
        return response;
    }
}