package org.jeecg.modules.fee.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.modules.reg.entity.Customer;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.entity.CustomerRegItemGroup;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 结账单
 * @Author: jeecg-boot
 * @Date:   2024-12-20
 * @Version: V1.0
 */
@Data
@TableName("customer_reg_bill")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="customer_reg_recipe对象", description="结账单")
public class CustomerRegBill implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键")
    private java.lang.String id;
    /**
     * 支付单号
     */
    @TableField
    @ApiModelProperty(value = "支付单号")
    private java.lang.String billNo;

	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建人*/
	@Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private java.lang.String creator;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private java.lang.String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private java.lang.String sysOrgCode;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private java.lang.String status;
	/**结账IP*/
	@Excel(name = "结账IP", width = 15)
    @ApiModelProperty(value = "结账IP")
    private java.lang.String ip;
	/**体检号*/
	@Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private java.lang.String examNo;
	/**顾客ID*/
	@Excel(name = "顾客ID", width = 15)
    @ApiModelProperty(value = "顾客ID")
    private java.lang.String customerId;
	/**体检登记ID*/
	@Excel(name = "体检登记ID", width = 15)
    @ApiModelProperty(value = "体检登记ID")
    private java.lang.String customerRegId;
    /**业务名称*/
    @Excel(name = "业务名称", width = 15)
    @ApiModelProperty(value = "业务名称")
    private String bizName;
    private BigDecimal amount;
    @TableLogic
    private String delFlag;
    /**赠送标志*/
    @Excel(name = "赠送标志", width = 15)
    @ApiModelProperty(value = "赠送标志")
    private String giveAwayFlag;
    /**赠送标志*/
    @Excel(name = "后付费标志", width = 15)
    @ApiModelProperty(value = "后付费标志")
    private String afterPayFlag;

    private String orderId;

    private String recipeNo;

    /**已付金额*/
    @Excel(name = "已付金额", width = 15)
    @ApiModelProperty(value = "已付金额")
    @TableField(exist = false)
    private BigDecimal paidAmount;
    /**未付金额*/
    @Excel(name = "未付金额", width = 15)
    @ApiModelProperty(value = "未付金额")
    @TableField(exist = false)
    private BigDecimal unpaidAmount;

    @TableField(exist = false)
    private List<String> customerRegItemGroupIds;
    @TableField(exist = false)
    private List<FeePayRecord> payRecords;
    @TableField(exist = false)
    private Customer customer;
    @TableField(exist = false)
    private CustomerReg customerReg;
    @TableField(exist = false)
    private List<String> refundItemGroupIds;
    @TableField(exist = false)
    private BigDecimal applyRefundAmount;
    @TableField(exist = false)
    private boolean refundDone;
    @TableField(exist = false)
    private List<CustomerRegItemGroup> customerRegItemGroups;

    @TableField(exist = false)
    private List<FeePayRecord> feePayRecords;

    @TableField(exist = false)
    private Boolean haveRefundFlag;

    // ========== 博思电子票据相关字段 ==========
    
    /**电子票据代码*/
    @Excel(name = "电子票据代码", width = 20)
    @ApiModelProperty(value = "电子票据代码")
    private String electronicBillBatchCode;

    /**电子票据号码*/
    @Excel(name = "电子票据号码", width = 20)
    @ApiModelProperty(value = "电子票据号码")
    private String electronicBillNo;

    /**电子校验码*/
    @Excel(name = "电子校验码", width = 20)
    @ApiModelProperty(value = "电子校验码")
    private String electronicBillRandom;

    /**电子票据状态*/
    @Excel(name = "电子票据状态", width = 15)
    @ApiModelProperty(value = "电子票据状态：0-未申请，1-申请中，2-已开具，3-申请失败")
    private String electronicBillStatus;

    /**电子票据二维码图片数据(BASE64)*/
    @ApiModelProperty(value = "电子票据二维码图片数据")
    private String electronicBillQrCode;

    /**电子票据H5页面URL*/
    @ApiModelProperty(value = "电子票据H5页面URL")
    private String electronicBillPictureUrl;

    /**电子票据开票时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "电子票据开票时间")
    private java.util.Date electronicBillIvcTime;

    /**电子票据开票错误信息*/
    @ApiModelProperty(value = "电子票据开票错误信息", notes = "开票失败时的错误信息")
    private String electronicBillError;

    /**是否已开红票*/
    @Excel(name = "是否已开红票", width = 15)
    @ApiModelProperty(value = "是否已开红票：0-未开红票，1-已开红票")
    private String isScarlet;

    /**电子票据申请时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "电子票据申请时间")
    private java.util.Date electronicBillApplyTime;

    /**是否需要电子票据*/
    @Excel(name = "是否需要电子票据", width = 15)
    @ApiModelProperty(value = "是否需要电子票据：1-需要，0-不需要")
    private Integer needElectronicBill;

    /**电子票据同步状态*/
    @ApiModelProperty(value = "电子票据同步状态：0-未同步，1-已同步，2-同步失败")
    private Integer electronicBillSyncStatus;

    /**电子票据最后同步时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "电子票据最后同步时间")
    private java.util.Date electronicBillLastSyncTime;

}
