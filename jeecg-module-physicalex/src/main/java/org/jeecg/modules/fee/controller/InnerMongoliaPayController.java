package org.jeecg.modules.fee.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.fee.dto.InnerMongoliaPaymentRequest;
import org.jeecg.modules.fee.dto.InnerMongoliaPaymentResponse;
import org.jeecg.modules.fee.service.IInnerMongoliaPayService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Api(tags = "内蒙古医院统一支付接口")
@RestController
@RequestMapping("/fee/innerMongolia")
@Slf4j
public class InnerMongoliaPayController {
    
    @Resource
    private IInnerMongoliaPayService innerMongoliaPayService;
    
    @ApiOperation(value = "扫码支付下单", notes = "用于微信公众号、小程序、支付宝生活号等")
    @PostMapping("/createQrCodeOrder")
    public Result<InnerMongoliaPaymentResponse> createQrCodeOrder(@RequestBody CreateQrCodeOrderRequest request) {
        try {
            InnerMongoliaPaymentRequest.UnifiedOrderBizContent bizContent = new InnerMongoliaPaymentRequest.UnifiedOrderBizContent();
            bizContent.setOrderNo(request.getOrderNo());
            bizContent.setOrderType(request.getOrderType());
            bizContent.setPayWayType(request.getPayWayType());
            bizContent.setAmount(request.getAmount().toString());
            bizContent.setNotifyUrl(request.getNotifyUrl());
            bizContent.setAttach(request.getAttach());
            bizContent.setSpbillCreateIp(request.getClientIp());
            
            InnerMongoliaPaymentRequest.HisBiz hisBiz = new InnerMongoliaPaymentRequest.HisBiz();
            hisBiz.setClinicCode(request.getClinicCode());
            hisBiz.setRecipeNos(request.getRecipeNos());
            hisBiz.setInpatientNo(request.getInpatientNo());
            hisBiz.setInpatientSeriNo(request.getInpatientSeriNo());
            hisBiz.setMarkType(request.getMarkType());
            hisBiz.setMarkNo(request.getMarkNo());
            hisBiz.setPatientName(request.getPatientName());
            hisBiz.setIDCard(request.getIdCard());
            hisBiz.setPatientPhone(request.getPatientPhone());
            bizContent.setHisBiz(hisBiz);
            bizContent.setInsuranceParam(new Object());
            
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.createUnifiedOrder(bizContent);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("创建扫码支付订单失败", e);
            return Result.error("创建支付订单失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "条码支付", notes = "用于窗口扫描客户付款码支付")
    @PostMapping("/createBarcodePayment")
    public Result<InnerMongoliaPaymentResponse> createBarcodePayment(@RequestBody CreateBarcodePaymentRequest request) {
        try {
            InnerMongoliaPaymentRequest.BarcodePayBizContent bizContent = new InnerMongoliaPaymentRequest.BarcodePayBizContent();
            bizContent.setOrderNo(request.getOrderNo());
            bizContent.setPayWayType(request.getPayWayType());
            bizContent.setOrderType(request.getOrderType());
            bizContent.setAuthCode(request.getAuthCode());
            bizContent.setSubject(request.getSubject());
            bizContent.setTotalAmount(request.getTotalAmount().doubleValue());
            bizContent.setOperatorName(request.getOperatorName());
            bizContent.setOperatorCode(request.getOperatorCode());
            bizContent.setTerminalId(request.getTerminalId());
            
            InnerMongoliaPaymentRequest.HisBiz hisBiz = new InnerMongoliaPaymentRequest.HisBiz();
            hisBiz.setClinicCode(request.getClinicCode());
            hisBiz.setRecipeNos(request.getRecipeNos());
            hisBiz.setInpatientNo(request.getInpatientNo());
            hisBiz.setInpatientSeriNo(request.getInpatientSeriNo());
            hisBiz.setMarkType(request.getMarkType());
            hisBiz.setPatientName(request.getPatientName());
            hisBiz.setIDCard(request.getIdCard());
            hisBiz.setCardNo(request.getCardNo());
            hisBiz.setPatientPhone(request.getPatientPhone());
            hisBiz.setDeptName(request.getDeptName());
            hisBiz.setDoctorName(request.getDoctorName());
            bizContent.setHisBiz(hisBiz);
            
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.createBarcodePayment(bizContent);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("创建条码支付失败", e);
            return Result.error("创建条码支付失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "支付查询", notes = "查询支付订单状态")
    @GetMapping("/queryPayment/{orderNo}")
    public Result<InnerMongoliaPaymentResponse> queryPayment(@PathVariable String orderNo) {
        try {
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.queryPayment(orderNo);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("查询支付订单失败", e);
            return Result.error("查询支付订单失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "申请退款", notes = "申请退款")
    @PostMapping("/refundPayment")
    public Result<InnerMongoliaPaymentResponse> refundPayment(@RequestBody RefundRequest request) {
        try {
            InnerMongoliaPaymentRequest.RefundBizContent bizContent = new InnerMongoliaPaymentRequest.RefundBizContent();
            bizContent.setOrderNo(request.getOrderNo());
            bizContent.setBatchNo(request.getBatchNo());
            bizContent.setRefundFee(request.getRefundFee().toString());
            bizContent.setTradeDesc(request.getTradeDesc());
            bizContent.setNotifyUrl(request.getNotifyUrl());
            bizContent.setOperatorName(request.getOperatorName());
            bizContent.setOperatorCode(request.getOperatorCode());
            
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.refundPayment(bizContent);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("申请退款失败", e);
            return Result.error("申请退款失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "撤销支付", notes = "撤销支付(注意：撤销会退费)")
    @PostMapping("/cancelPayment/{orderNo}")
    public Result<InnerMongoliaPaymentResponse> cancelPayment(@PathVariable String orderNo) {
        try {
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.cancelPayment(orderNo);
            return Result.ok(response);
        } catch (Exception e) {
            log.error("撤销支付失败", e);
            return Result.error("撤销支付失败: " + e.getMessage());
        }
    }
    
    public static class CreateQrCodeOrderRequest {
        private String orderNo;
        private String orderType;
        private String payWayType;
        private BigDecimal amount;
        private String notifyUrl;
        private String attach;
        private String clientIp;
        private String clinicCode;
        private String recipeNos;
        private String inpatientNo;
        private String inpatientSeriNo;
        private String markType;
        private String markNo;
        private String patientName;
        private String idCard;
        private String patientPhone;
        
        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
        public String getOrderType() { return orderType; }
        public void setOrderType(String orderType) { this.orderType = orderType; }
        public String getPayWayType() { return payWayType; }
        public void setPayWayType(String payWayType) { this.payWayType = payWayType; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public String getNotifyUrl() { return notifyUrl; }
        public void setNotifyUrl(String notifyUrl) { this.notifyUrl = notifyUrl; }
        public String getAttach() { return attach; }
        public void setAttach(String attach) { this.attach = attach; }
        public String getClientIp() { return clientIp; }
        public void setClientIp(String clientIp) { this.clientIp = clientIp; }
        public String getClinicCode() { return clinicCode; }
        public void setClinicCode(String clinicCode) { this.clinicCode = clinicCode; }
        public String getRecipeNos() { return recipeNos; }
        public void setRecipeNos(String recipeNos) { this.recipeNos = recipeNos; }
        public String getInpatientNo() { return inpatientNo; }
        public void setInpatientNo(String inpatientNo) { this.inpatientNo = inpatientNo; }
        public String getInpatientSeriNo() { return inpatientSeriNo; }
        public void setInpatientSeriNo(String inpatientSeriNo) { this.inpatientSeriNo = inpatientSeriNo; }
        public String getMarkType() { return markType; }
        public void setMarkType(String markType) { this.markType = markType; }
        public String getMarkNo() { return markNo; }
        public void setMarkNo(String markNo) { this.markNo = markNo; }
        public String getPatientName() { return patientName; }
        public void setPatientName(String patientName) { this.patientName = patientName; }
        public String getIdCard() { return idCard; }
        public void setIdCard(String idCard) { this.idCard = idCard; }
        public String getPatientPhone() { return patientPhone; }
        public void setPatientPhone(String patientPhone) { this.patientPhone = patientPhone; }
    }
    
    public static class CreateBarcodePaymentRequest {
        private String orderNo;
        private Integer payWayType;
        private Integer orderType;
        private String authCode;
        private String subject;
        private BigDecimal totalAmount;
        private String operatorName;
        private String operatorCode;
        private String terminalId;
        private String clinicCode;
        private String recipeNos;
        private String inpatientNo;
        private String inpatientSeriNo;
        private String markType;
        private String patientName;
        private String idCard;
        private String cardNo;
        private String patientPhone;
        private String deptName;
        private String doctorName;
        
        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
        public Integer getPayWayType() { return payWayType; }
        public void setPayWayType(Integer payWayType) { this.payWayType = payWayType; }
        public Integer getOrderType() { return orderType; }
        public void setOrderType(Integer orderType) { this.orderType = orderType; }
        public String getAuthCode() { return authCode; }
        public void setAuthCode(String authCode) { this.authCode = authCode; }
        public String getSubject() { return subject; }
        public void setSubject(String subject) { this.subject = subject; }
        public BigDecimal getTotalAmount() { return totalAmount; }
        public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }
        public String getOperatorName() { return operatorName; }
        public void setOperatorName(String operatorName) { this.operatorName = operatorName; }
        public String getOperatorCode() { return operatorCode; }
        public void setOperatorCode(String operatorCode) { this.operatorCode = operatorCode; }
        public String getTerminalId() { return terminalId; }
        public void setTerminalId(String terminalId) { this.terminalId = terminalId; }
        public String getClinicCode() { return clinicCode; }
        public void setClinicCode(String clinicCode) { this.clinicCode = clinicCode; }
        public String getRecipeNos() { return recipeNos; }
        public void setRecipeNos(String recipeNos) { this.recipeNos = recipeNos; }
        public String getInpatientNo() { return inpatientNo; }
        public void setInpatientNo(String inpatientNo) { this.inpatientNo = inpatientNo; }
        public String getInpatientSeriNo() { return inpatientSeriNo; }
        public void setInpatientSeriNo(String inpatientSeriNo) { this.inpatientSeriNo = inpatientSeriNo; }
        public String getMarkType() { return markType; }
        public void setMarkType(String markType) { this.markType = markType; }
        public String getPatientName() { return patientName; }
        public void setPatientName(String patientName) { this.patientName = patientName; }
        public String getIdCard() { return idCard; }
        public void setIdCard(String idCard) { this.idCard = idCard; }
        public String getCardNo() { return cardNo; }
        public void setCardNo(String cardNo) { this.cardNo = cardNo; }
        public String getPatientPhone() { return patientPhone; }
        public void setPatientPhone(String patientPhone) { this.patientPhone = patientPhone; }
        public String getDeptName() { return deptName; }
        public void setDeptName(String deptName) { this.deptName = deptName; }
        public String getDoctorName() { return doctorName; }
        public void setDoctorName(String doctorName) { this.doctorName = doctorName; }
    }
    
    public static class RefundRequest {
        private String orderNo;
        private String batchNo;
        private BigDecimal refundFee;
        private String tradeDesc;
        private String notifyUrl;
        private String operatorName;
        private String operatorCode;
        
        public String getOrderNo() { return orderNo; }
        public void setOrderNo(String orderNo) { this.orderNo = orderNo; }
        public String getBatchNo() { return batchNo; }
        public void setBatchNo(String batchNo) { this.batchNo = batchNo; }
        public BigDecimal getRefundFee() { return refundFee; }
        public void setRefundFee(BigDecimal refundFee) { this.refundFee = refundFee; }
        public String getTradeDesc() { return tradeDesc; }
        public void setTradeDesc(String tradeDesc) { this.tradeDesc = tradeDesc; }
        public String getNotifyUrl() { return notifyUrl; }
        public void setNotifyUrl(String notifyUrl) { this.notifyUrl = notifyUrl; }
        public String getOperatorName() { return operatorName; }
        public void setOperatorName(String operatorName) { this.operatorName = operatorName; }
        public String getOperatorCode() { return operatorCode; }
        public void setOperatorCode(String operatorCode) { this.operatorCode = operatorCode; }
    }
}