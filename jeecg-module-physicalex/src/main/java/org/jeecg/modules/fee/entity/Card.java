package org.jeecg.modules.fee.entity;

import java.io.FileDescriptor;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: card
 * @Author: jeecg-boot
 * @Date:   2024-07-14
 * @Version: V1.0
 */
@Data
@TableName("card")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="card对象", description="card")
public class Card implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**设置ID*/
	@Excel(name = "设置ID", width = 15)
    @ApiModelProperty(value = "设置ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String goodsId;
    /**订单ID*/
    @Excel(name = "订单ID", width = 15)
    @ApiModelProperty(value = "订单ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String orderId;
	/**卡号*/
	@Excel(name = "卡号", width = 15)
    @ApiModelProperty(value = "卡号")
    private java.lang.String cardNo;
	/**卡类别：单位凭证卡、单位储值卡、个人储值卡*/
	@Excel(name = "卡类别：单位凭证卡、单位储值卡、个人储值卡", width = 15)
    @ApiModelProperty(value = "卡类别：单位凭证卡、单位储值卡、个人储值卡")
    private java.lang.String category;
	/**发布时间*/
	@Excel(name = "发布时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发布时间")
    private java.util.Date publishTime;
	/**生效时间*/
	@Excel(name = "生效时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "生效时间")
    private java.util.Date effectiveTime;
	/**折扣率*/
	@Excel(name = "折扣率", width = 15)
    @ApiModelProperty(value = "折扣率")
    private java.math.BigDecimal discountRate;
	/**面额*/
	@Excel(name = "面额", width = 15)
    @ApiModelProperty(value = "面额")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.math.BigDecimal denomination;
	/**状态*/
	@Excel(name = "状态", width = 15)
    @ApiModelProperty(value = "状态")
    private java.lang.String status;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private java.lang.String remark;
	/**用户ID*/
	@Excel(name = "用户ID", width = 15)
    @ApiModelProperty(value = "用户ID")
    private java.lang.String customerId;
	/**用户姓名*/
	@Excel(name = "用户姓名", width = 15)
    @ApiModelProperty(value = "用户姓名")
    private java.lang.String customerName;
	/**余额*/
	@Excel(name = "余额", width = 15)
    @ApiModelProperty(value = "余额")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.math.BigDecimal balance;
    /**实收金额*/
    @Excel(name = "实收金额", width = 15)
    @ApiModelProperty(value = "实收金额")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.math.BigDecimal amount;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**结算状态*/
	@Excel(name = "结算状态", width = 15)
    @ApiModelProperty(value = "结算状态")
    private java.lang.String settleStatus;
	/**开卡人*/
	@Excel(name = "开卡人", width = 15)
    @ApiModelProperty(value = "开卡人")
    private java.lang.String creator;
	/**公司ID*/
	@Excel(name = "公司ID", width = 15)
    @ApiModelProperty(value = "公司ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String companyId;
	/**公司名称*/
	@Excel(name = "公司名称", width = 15)
    @ApiModelProperty(value = "公司名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String companyName;
	/**单位登记ID*/
	@Excel(name = "单位登记ID", width = 15)
    @ApiModelProperty(value = "单位登记ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private java.lang.String companyRegId;
	/**卡来源：旧卡、新卡*/
	@Excel(name = "卡来源：旧卡、新卡", width = 15)
    @ApiModelProperty(value = "卡来源：旧卡、新卡")
    private java.lang.String source;
    /**卡密码*/
    @Excel(name = "卡密码", width = 15)
    @ApiModelProperty(value = "卡密码")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private java.lang.String pwd;
    /**二维码内容*/
    @Excel(name = "二维码内容", width = 15)
    @ApiModelProperty(value = "二维码内容")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private java.lang.String qrContent;
    /**二维码文件*/
    @Excel(name = "二维码文件", width = 15)
    @ApiModelProperty(value = "二维码文件")
    private java.lang.String qrFile;
    /**发行时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "发行时间")
    private Date releaseTime;
    /**发行人*/
    @ApiModelProperty(value = "发行人")
    private String releaseBy;
    /**售出时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "售出时间")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date saleTime;
    /**售出人*/
    @ApiModelProperty(value = "售出人")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String saleBy;
    /**写入时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "写入时间")
    private Date produceTime;
    /**写入人*/
    @ApiModelProperty(value = "写入人")
    private String produceBy;
    /**锁定时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "锁定时间")
    private Date lockTime;
    /**锁定人*/
    @ApiModelProperty(value = "锁定人")
    private String lockBy;
    /**锁定原因*/
    @ApiModelProperty(value = "锁定原因")
    private String lockReason;
    /**作废时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "作废时间")
    private Date invalidTime;
    /**作废人*/
    @ApiModelProperty(value = "作废人")
    private String invalidBy;
    /**卡递增号*/
    @ApiModelProperty(value = "卡递增号")
    private Integer cardNumber;
    /**批次号*/
    @ApiModelProperty(value = "批次号")
    private String batchNumber;
    /**乐观锁版本号*/
    @Version
    @ApiModelProperty(value = "乐观锁版本号")
    private Integer version;
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String handoverId;

    /**重置原因*/
    @ApiModelProperty(value = "重置原因")
    private String resetReason;
    /**重置时间*/
    @ApiModelProperty(value = "重置时间")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date resetTime;
    /**重置操作人*/
    @ApiModelProperty(value = "重置操作人")
    private String resetBy;

    @TableField(exist = false)
    private String prefix;

    @TableField(exist = false)
    private Integer totalCount;
    @TableField(exist = false)
    private BigDecimal currentBalance;
}
