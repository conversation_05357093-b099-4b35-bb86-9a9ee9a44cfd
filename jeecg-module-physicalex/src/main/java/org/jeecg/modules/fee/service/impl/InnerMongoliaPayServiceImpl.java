package org.jeecg.modules.fee.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.fee.dto.InnerMongoliaPaymentRequest;
import org.jeecg.modules.fee.dto.InnerMongoliaPaymentResponse;
import org.jeecg.modules.fee.service.IInnerMongoliaPayService;
import org.jeecg.modules.fee.util.InnerMongoliaSignUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class InnerMongoliaPayServiceImpl implements IInnerMongoliaPayService {
    
    @Resource
    private RestTemplate restTemplate;
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Value("${inner.mongolia.pay.gateway.url:http://test-cn.your-api-server.com/realpay/gateway}")
    private String gatewayUrl;
    
    @Value("${inner.mongolia.pay.app.id}")
    private String appId;
    
    @Value("${inner.mongolia.pay.private.key}")
    private String privateKey;
    
    @Value("${inner.mongolia.pay.public.key}")
    private String publicKey;
    
    @Override
    public InnerMongoliaPaymentResponse createUnifiedOrder(InnerMongoliaPaymentRequest.UnifiedOrderBizContent bizContent) {
        InnerMongoliaPaymentRequest request = buildBaseRequest("rop.trade.union.create");
        
        try {
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            return sendRequest(request);
        } catch (JsonProcessingException e) {
            log.error("序列化bizContent失败", e);
            throw new RuntimeException("创建统一下单请求失败", e);
        }
    }
    
    @Override
    public InnerMongoliaPaymentResponse createBarcodePayment(InnerMongoliaPaymentRequest.BarcodePayBizContent bizContent) {
        InnerMongoliaPaymentRequest request = buildBaseRequest("rop.trade.pay");
        
        try {
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            return sendRequest(request);
        } catch (JsonProcessingException e) {
            log.error("序列化bizContent失败", e);
            throw new RuntimeException("创建条码支付请求失败", e);
        }
    }
    
    @Override
    public InnerMongoliaPaymentResponse queryPayment(String orderNo) {
        InnerMongoliaPaymentRequest request = buildBaseRequest("rop.trade.query");
        
        InnerMongoliaPaymentRequest.QueryBizContent bizContent = new InnerMongoliaPaymentRequest.QueryBizContent();
        bizContent.setOrderNo(orderNo);
        
        try {
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            return sendRequest(request);
        } catch (JsonProcessingException e) {
            log.error("序列化bizContent失败", e);
            throw new RuntimeException("创建支付查询请求失败", e);
        }
    }
    
    @Override
    public InnerMongoliaPaymentResponse refundPayment(InnerMongoliaPaymentRequest.RefundBizContent bizContent) {
        InnerMongoliaPaymentRequest request = buildBaseRequest("rop.trade.refund");
        
        try {
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            return sendRequest(request);
        } catch (JsonProcessingException e) {
            log.error("序列化bizContent失败", e);
            throw new RuntimeException("创建退款请求失败", e);
        }
    }
    
    @Override
    public InnerMongoliaPaymentResponse cancelPayment(String orderNo) {
        InnerMongoliaPaymentRequest request = buildBaseRequest("rop.trade.cancel");
        
        InnerMongoliaPaymentRequest.CancelBizContent bizContent = new InnerMongoliaPaymentRequest.CancelBizContent();
        bizContent.setOrderNo(orderNo);
        
        try {
            request.setBizContent(objectMapper.writeValueAsString(bizContent));
            return sendRequest(request);
        } catch (JsonProcessingException e) {
            log.error("序列化bizContent失败", e);
            throw new RuntimeException("创建撤销请求失败", e);
        }
    }
    
    private InnerMongoliaPaymentRequest buildBaseRequest(String method) {
        InnerMongoliaPaymentRequest request = new InnerMongoliaPaymentRequest();
        request.setMethod(method);
        request.setAppId(appId);
        request.setReqDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        request.setCharset("UTF-8");
        request.setSignType("RSA");
        request.setVersion("1.0");
        return request;
    }
    
    private InnerMongoliaPaymentResponse sendRequest(InnerMongoliaPaymentRequest request) {
        try {
            Map<String, Object> signParams = buildSignParams(request);
            String sign = InnerMongoliaSignUtil.createSign(signParams, privateKey);
            request.setSign(sign);
            
            log.info("发送内蒙古支付请求: method={}, orderNo={}", 
                request.getMethod(), extractOrderNoFromBizContent(request.getBizContent()));
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<InnerMongoliaPaymentRequest> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<InnerMongoliaPaymentResponse> response = restTemplate.postForEntity(
                gatewayUrl, entity, InnerMongoliaPaymentResponse.class);
            
            InnerMongoliaPaymentResponse result = response.getBody();
            if (result != null && verifyResponse(result)) {
                log.info("内蒙古支付请求成功: msgCode={}", result.getMsgCode());
                return result;
            } else {
                log.error("内蒙古支付响应验签失败");
                throw new RuntimeException("支付响应验签失败");
            }
        } catch (Exception e) {
            log.error("发送内蒙古支付请求失败", e);
            throw new RuntimeException("支付请求失败", e);
        }
    }
    
    private Map<String, Object> buildSignParams(InnerMongoliaPaymentRequest request) {
        Map<String, Object> params = new HashMap<>();
        params.put("appId", request.getAppId());
        params.put("method", request.getMethod());
        params.put("reqDate", request.getReqDate());
        params.put("charset", request.getCharset());
        params.put("bizContent", request.getBizContent());
        params.put("signType", request.getSignType());
        params.put("extras", request.getExtras());
        params.put("version", request.getVersion());
        return params;
    }
    
    private boolean verifyResponse(InnerMongoliaPaymentResponse response) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("msgCode", response.getMsgCode());
            params.put("msg", response.getMsg());
            params.put("charset", response.getCharset());
            params.put("signType", response.getSignType());
            params.put("data", objectMapper.writeValueAsString(response.getData()));
            
            return InnerMongoliaSignUtil.verifySign(params, publicKey, response.getSign());
        } catch (Exception e) {
            log.error("验证响应签名失败", e);
            return false;
        }
    }
    
    private String extractOrderNoFromBizContent(String bizContent) {
        try {
            Map<String, Object> bizMap = objectMapper.readValue(bizContent, Map.class);
            return (String) bizMap.get("orderNo");
        } catch (Exception e) {
            return "unknown";
        }
    }
}