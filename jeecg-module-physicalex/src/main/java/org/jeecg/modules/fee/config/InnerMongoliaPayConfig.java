package org.jeecg.modules.fee.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "inner.mongolia.pay")
public class InnerMongoliaPayConfig {
    
    private Gateway gateway = new Gateway();
    private App app = new App();
    private PrivateKey privateKey = new PrivateKey();
    private PublicKey publicKey = new PublicKey();
    private Notify notify = new Notify();
    
    @Data
    public static class Gateway {
        private String url = "http://test-cn.your-api-server.com/realpay/gateway";
    }
    
    @Data
    public static class App {
        private String id;
    }
    
    @Data
    public static class PrivateKey {
        private String key;
    }
    
    @Data
    public static class PublicKey {
        private String key;
    }
    
    @Data
    public static class Notify {
        private String url;
    }
}