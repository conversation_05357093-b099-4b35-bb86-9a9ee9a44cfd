package org.jeecg.modules.fee.gateway.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.fee.config.InnerMongoliaPayConfig;
import org.jeecg.modules.fee.dto.InnerMongoliaPaymentRequest;
import org.jeecg.modules.fee.dto.InnerMongoliaPaymentResponse;
import org.jeecg.modules.fee.gateway.IPaymentGateway;
import org.jeecg.modules.fee.gateway.dto.*;
import org.jeecg.modules.fee.gateway.exception.PaymentGatewayException;
import org.jeecg.modules.fee.service.IInnerMongoliaPayService;
import org.jeecg.modules.fee.util.InnerMongoliaSignUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Slf4j
@Component("innerMongoliaPayGateway")
public class InnerMongoliaPayGateway implements IPaymentGateway {
    
    @Resource
    private IInnerMongoliaPayService innerMongoliaPayService;
    
    @Resource
    private InnerMongoliaPayConfig payConfig;
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Override
    public String getGatewayName() {
        return "InnerMongolia";
    }
    
    @Override
    public PaymentResponse createPayment(PaymentRequest request) {
        return switch (request.getPaymentType()) {
            case QR_CODE -> createQrCodePayment(request);
            case BARCODE -> createBarcodePayment(request);
            case JSAPI -> createQrCodePayment(request);
            case H5 -> createQrCodePayment(request);
            case MINI_PROGRAM -> createQrCodePayment(request);
            default -> createQrCodePayment(request);
        };
    }
    
    @Override
    public PaymentResponse createQrCodePayment(PaymentRequest request) {
        try {
            InnerMongoliaPaymentRequest.UnifiedOrderBizContent bizContent = buildUnifiedOrderBizContent(request);
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.createUnifiedOrder(bizContent);
            
            return convertToPaymentResponse(response, request);
        } catch (Exception e) {
            log.error("内蒙古扫码支付创建失败", e);
            throw new PaymentGatewayException("INNER_MONGOLIA_QR_ERROR", "创建扫码支付失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public PaymentResponse createBarcodePayment(PaymentRequest request) {
        try {
            InnerMongoliaPaymentRequest.BarcodePayBizContent bizContent = buildBarcodePayBizContent(request);
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.createBarcodePayment(bizContent);
            
            return convertToBarcodePaymentResponse(response, request);
        } catch (Exception e) {
            log.error("内蒙古条码支付创建失败", e);
            throw new PaymentGatewayException("INNER_MONGOLIA_BARCODE_ERROR", "创建条码支付失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public QueryResponse queryPayment(QueryRequest request) {
        try {
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.queryPayment(request.getOrderNo());
            
            return convertToQueryResponse(response, request);
        } catch (Exception e) {
            log.error("内蒙古支付查询失败", e);
            throw new PaymentGatewayException("INNER_MONGOLIA_QUERY_ERROR", "查询支付失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public RefundResponse refundPayment(RefundRequest request) {
        try {
            InnerMongoliaPaymentRequest.RefundBizContent bizContent = buildRefundBizContent(request);
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.refundPayment(bizContent);
            
            return convertToRefundResponse(response, request);
        } catch (Exception e) {
            log.error("内蒙古退款申请失败", e);
            throw new PaymentGatewayException("INNER_MONGOLIA_REFUND_ERROR", "申请退款失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public CancelResponse cancelPayment(CancelRequest request) {
        try {
            InnerMongoliaPaymentResponse response = innerMongoliaPayService.cancelPayment(request.getOrderNo());
            
            return convertToCancelResponse(response, request);
        } catch (Exception e) {
            log.error("内蒙古支付撤销失败", e);
            throw new PaymentGatewayException("INNER_MONGOLIA_CANCEL_ERROR", "撤销支付失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean verifyNotifySign(String notifyData) {
        try {
            Map<String, Object> params = objectMapper.readValue(notifyData, Map.class);
            String sign = (String) params.get("sign");
            
            if (sign == null) {
                return false;
            }
            
            return InnerMongoliaSignUtil.verifySign(params, payConfig.getPublicKey().getKey(), sign);
        } catch (Exception e) {
            log.error("内蒙古支付回调验签失败", e);
            return false;
        }
    }
    
    @Override
    public boolean isOrderCompleted(String orderNo) {
        try {
            QueryResponse response = queryPayment(new QueryRequest(orderNo));
            return response.isSuccess() && (
                response.getStatus() == PaymentResponse.PaymentStatus.SUCCESS ||
                response.getStatus() == PaymentResponse.PaymentStatus.REFUNDED
            );
        } catch (Exception e) {
            log.error("内蒙古支付查询订单完成状态失败", e);
            return false;
        }
    }
    
    private InnerMongoliaPaymentRequest.UnifiedOrderBizContent buildUnifiedOrderBizContent(PaymentRequest request) {
        InnerMongoliaPaymentRequest.UnifiedOrderBizContent bizContent = 
            new InnerMongoliaPaymentRequest.UnifiedOrderBizContent();
        
        bizContent.setOrderNo(request.getOrderNo());
        bizContent.setOrderType(determineOrderType(request));
        bizContent.setPayWayType(determinePayWayType(request));
        bizContent.setAmount(request.getAmount().toString());
        bizContent.setNotifyUrl(request.getNotifyUrl());
        bizContent.setAttach(request.getAttach());
        bizContent.setSpbillCreateIp(request.getClientIp());
        
        if (request.getPatientInfo() != null) {
            InnerMongoliaPaymentRequest.HisBiz hisBiz = new InnerMongoliaPaymentRequest.HisBiz();
            hisBiz.setClinicCode(request.getPatientInfo().getClinicCode());
            hisBiz.setInpatientNo(request.getPatientInfo().getInpatientNo());
            hisBiz.setMarkType(request.getPatientInfo().getMarkType());
            hisBiz.setMarkNo(request.getPatientInfo().getMarkNo());
            hisBiz.setPatientName(request.getPatientInfo().getPatientName());
            hisBiz.setIDCard(request.getPatientInfo().getIdCard());
            hisBiz.setPatientPhone(request.getPatientInfo().getPhone());
            bizContent.setHisBiz(hisBiz);
        }
        
        bizContent.setInsuranceParam(new Object());
        
        return bizContent;
    }
    
    private InnerMongoliaPaymentRequest.BarcodePayBizContent buildBarcodePayBizContent(PaymentRequest request) {
        InnerMongoliaPaymentRequest.BarcodePayBizContent bizContent = 
            new InnerMongoliaPaymentRequest.BarcodePayBizContent();
        
        bizContent.setOrderNo(request.getOrderNo());
        bizContent.setPayWayType(determinePayWayTypeInt(request));
        bizContent.setOrderType(determineOrderTypeInt(request));
        bizContent.setAuthCode(request.getAuthCode());
        bizContent.setSubject(request.getSubject());
        bizContent.setTotalAmount(request.getAmount().doubleValue());
        
        if (request.getOperatorInfo() != null) {
            bizContent.setOperatorName(request.getOperatorInfo().getOperatorName());
            bizContent.setOperatorCode(request.getOperatorInfo().getOperatorCode());
            bizContent.setTerminalId(request.getOperatorInfo().getTerminalId());
        }
        
        if (request.getPatientInfo() != null) {
            InnerMongoliaPaymentRequest.HisBiz hisBiz = new InnerMongoliaPaymentRequest.HisBiz();
            hisBiz.setClinicCode(request.getPatientInfo().getClinicCode());
            hisBiz.setInpatientNo(request.getPatientInfo().getInpatientNo());
            hisBiz.setMarkType(request.getPatientInfo().getMarkType());
            hisBiz.setPatientName(request.getPatientInfo().getPatientName());
            hisBiz.setIDCard(request.getPatientInfo().getIdCard());
            hisBiz.setCardNo(request.getPatientInfo().getCardNo());
            hisBiz.setPatientPhone(request.getPatientInfo().getPhone());
            hisBiz.setDeptName(request.getPatientInfo().getDeptName());
            hisBiz.setDoctorName(request.getPatientInfo().getDoctorName());
            bizContent.setHisBiz(hisBiz);
        }
        
        return bizContent;
    }
    
    private InnerMongoliaPaymentRequest.RefundBizContent buildRefundBizContent(RefundRequest request) {
        InnerMongoliaPaymentRequest.RefundBizContent bizContent = 
            new InnerMongoliaPaymentRequest.RefundBizContent();
        
        bizContent.setOrderNo(request.getOrderNo());
        bizContent.setBatchNo(request.getBatchNo());
        bizContent.setRefundFee(request.getRefundAmount().toString());
        bizContent.setTradeDesc(request.getReason());
        bizContent.setNotifyUrl(request.getNotifyUrl());
        
        if (request.getOperatorInfo() != null) {
            bizContent.setOperatorName(request.getOperatorInfo().getOperatorName());
            bizContent.setOperatorCode(request.getOperatorInfo().getOperatorCode());
        }
        
        return bizContent;
    }
    
    private PaymentResponse convertToPaymentResponse(InnerMongoliaPaymentResponse response, PaymentRequest request) {
        PaymentResponse paymentResponse = new PaymentResponse();
        
        if ("0".equals(response.getMsgCode())) {
            InnerMongoliaPaymentResponse.UnifiedOrderData data = 
                objectMapper.convertValue(response.getData(), InnerMongoliaPaymentResponse.UnifiedOrderData.class);
            
            paymentResponse.setSuccess(true);
            paymentResponse.setCode("SUCCESS");
            paymentResponse.setMessage("创建成功");
            paymentResponse.setOrderNo(request.getOrderNo());
            paymentResponse.setPayInfo(data.getPayInfo());
            paymentResponse.setAmount(request.getAmount());
            paymentResponse.setStatus(PaymentResponse.PaymentStatus.CREATED);
            paymentResponse.setCreateTime(new Date());
        } else {
            paymentResponse.setSuccess(false);
            paymentResponse.setCode(response.getMsgCode());
            paymentResponse.setMessage(response.getMsg());
            paymentResponse.setStatus(PaymentResponse.PaymentStatus.FAILED);
        }
        
        paymentResponse.setRawResponse(response);
        return paymentResponse;
    }
    
    private PaymentResponse convertToBarcodePaymentResponse(InnerMongoliaPaymentResponse response, 
                                                           PaymentRequest request) {
        PaymentResponse paymentResponse = new PaymentResponse();
        
        if ("0".equals(response.getMsgCode())) {
            InnerMongoliaPaymentResponse.BarcodePayData data = 
                objectMapper.convertValue(response.getData(), InnerMongoliaPaymentResponse.BarcodePayData.class);
            
            if ("SUCCESS".equals(data.getCode())) {
                paymentResponse.setSuccess(true);
                paymentResponse.setCode("SUCCESS");
                paymentResponse.setMessage("支付成功");
                paymentResponse.setStatus(PaymentResponse.PaymentStatus.SUCCESS);
                paymentResponse.setSuccessTime(new Date());
                paymentResponse.setTradeNo(data.getTradeNo());
            } else {
                paymentResponse.setSuccess(false);
                paymentResponse.setCode(data.getCode());
                paymentResponse.setMessage(data.getMsg());
                paymentResponse.setStatus(PaymentResponse.PaymentStatus.FAILED);
            }
            
            paymentResponse.setOrderNo(request.getOrderNo());
            paymentResponse.setChannelOrderNo(data.getPolymerizationNo());
            paymentResponse.setAmount(request.getAmount());
            paymentResponse.setPayWayType(data.getPayWayType().toString());
        } else {
            paymentResponse.setSuccess(false);
            paymentResponse.setCode(response.getMsgCode());
            paymentResponse.setMessage(response.getMsg());
            paymentResponse.setStatus(PaymentResponse.PaymentStatus.FAILED);
        }
        
        paymentResponse.setCreateTime(new Date());
        paymentResponse.setRawResponse(response);
        return paymentResponse;
    }
    
    private QueryResponse convertToQueryResponse(InnerMongoliaPaymentResponse response, QueryRequest request) {
        QueryResponse queryResponse = new QueryResponse();
        
        if ("0".equals(response.getMsgCode())) {
            InnerMongoliaPaymentResponse.QueryData data = 
                objectMapper.convertValue(response.getData(), InnerMongoliaPaymentResponse.QueryData.class);
            
            queryResponse.setSuccess(true);
            queryResponse.setCode(data.getCode());
            queryResponse.setMessage(data.getMsg());
            queryResponse.setOrderNo(request.getOrderNo());
            queryResponse.setChannelOrderNo(data.getPolymerizationNo());
            queryResponse.setTradeNo(data.getTradeNo());
            queryResponse.setPayWayType(data.getPayWayType().toString());
            
            if ("SUCCESS".equals(data.getCode())) {
                queryResponse.setStatus(PaymentResponse.PaymentStatus.SUCCESS);
            } else {
                queryResponse.setStatus(PaymentResponse.PaymentStatus.FAILED);
            }
        } else {
            queryResponse.setSuccess(false);
            queryResponse.setCode(response.getMsgCode());
            queryResponse.setMessage(response.getMsg());
        }
        
        queryResponse.setRawResponse(response);
        return queryResponse;
    }
    
    private RefundResponse convertToRefundResponse(InnerMongoliaPaymentResponse response, RefundRequest request) {
        RefundResponse refundResponse = new RefundResponse();
        
        if ("0".equals(response.getMsgCode())) {
            InnerMongoliaPaymentResponse.RefundData data = 
                objectMapper.convertValue(response.getData(), InnerMongoliaPaymentResponse.RefundData.class);
            
            if ("SUCCESS".equals(data.getCode()) || "ACCEPTSUCCESS".equals(data.getCode())) {
                refundResponse.setSuccess(true);
                refundResponse.setCode("SUCCESS");
                refundResponse.setMessage("退款成功");
                refundResponse.setStatus(RefundResponse.RefundStatus.SUCCESS);
                refundResponse.setSuccessTime(new Date());
            } else {
                refundResponse.setSuccess(false);
                refundResponse.setCode(data.getCode());
                refundResponse.setMessage(data.getMsg());
                refundResponse.setStatus(RefundResponse.RefundStatus.FAILED);
            }
            
            refundResponse.setOrderNo(request.getOrderNo());
            refundResponse.setRefundOrderNo(request.getRefundOrderNo());
            refundResponse.setChannelOrderNo(data.getOutRefundNo());
            refundResponse.setTradeNo(data.getTradeNo());
            refundResponse.setRefundAmount(request.getRefundAmount());
        } else {
            refundResponse.setSuccess(false);
            refundResponse.setCode(response.getMsgCode());
            refundResponse.setMessage(response.getMsg());
            refundResponse.setStatus(RefundResponse.RefundStatus.FAILED);
        }
        
        refundResponse.setCreateTime(new Date());
        refundResponse.setRawResponse(response);
        return refundResponse;
    }
    
    private CancelResponse convertToCancelResponse(InnerMongoliaPaymentResponse response, CancelRequest request) {
        CancelResponse cancelResponse = new CancelResponse();
        
        if ("0".equals(response.getMsgCode())) {
            InnerMongoliaPaymentResponse.CancelData data = 
                objectMapper.convertValue(response.getData(), InnerMongoliaPaymentResponse.CancelData.class);
            
            if ("SUCCESS".equals(data.getCode())) {
                cancelResponse.setSuccess(true);
                cancelResponse.setCode("SUCCESS");
                cancelResponse.setMessage("撤销成功");
                cancelResponse.setStatus(CancelResponse.CancelStatus.SUCCESS);
                cancelResponse.setCancelTime(new Date());
            } else {
                cancelResponse.setSuccess(false);
                cancelResponse.setCode(data.getCode());
                cancelResponse.setMessage(data.getMsg());
                cancelResponse.setStatus(CancelResponse.CancelStatus.FAILED);
            }
            
            cancelResponse.setOrderNo(request.getOrderNo());
            cancelResponse.setBatchNo(data.getBatchNo());
        } else {
            cancelResponse.setSuccess(false);
            cancelResponse.setCode(response.getMsgCode());
            cancelResponse.setMessage(response.getMsg());
            cancelResponse.setStatus(CancelResponse.CancelStatus.FAILED);
        }
        
        cancelResponse.setRawResponse(response);
        return cancelResponse;
    }
    
    private String determineOrderType(PaymentRequest request) {
        if (request.getPatientInfo() != null) {
            if (request.getPatientInfo().getInpatientNo() != null) {
                return "1";
            } else if (request.getPatientInfo().getClinicCode() != null) {
                return "2";
            }
        }
        return "4";
    }
    
    private Integer determineOrderTypeInt(PaymentRequest request) {
        return Integer.parseInt(determineOrderType(request));
    }
    
    private String determinePayWayType(PaymentRequest request) {
        if (request.getWayCode() != null) {
            return switch (request.getWayCode()) {
                case "wxpay", "WX_JSAPI", "WX_H5" -> "32";
                case "alipay", "ALI_QR", "ALI_BAR" -> "22";
                default -> "32";
            };
        }
        return "32";
    }
    
    private Integer determinePayWayTypeInt(PaymentRequest request) {
        return Integer.parseInt(determinePayWayType(request));
    }
}