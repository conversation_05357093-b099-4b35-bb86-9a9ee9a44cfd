package org.jeecg.modules.fee.gateway.config;

import lombok.Data;
import org.jeecg.modules.fee.gateway.enums.PaymentGatewayType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "payment.gateway")
public class PaymentGatewayConfig {
    
    private String defaultProvider = PaymentGatewayType.JEEPAY.getCode();
    
    private boolean fallbackEnabled = true;
    
    private String fallbackProvider = PaymentGatewayType.JEEPAY.getCode();
    
    public PaymentGatewayType getDefaultProviderType() {
        return PaymentGatewayType.fromCode(defaultProvider);
    }
    
    public PaymentGatewayType getFallbackProviderType() {
        return PaymentGatewayType.fromCode(fallbackProvider);
    }
}