package org.jeecg.modules.fee.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.fee.util.InnerMongoliaSignUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Api(tags = "内蒙古支付回调接口")
@RestController
@RequestMapping("/fee/innerMongolia/notify")
@Slf4j
public class InnerMongoliaPayNotifyController {
    
    @Resource
    private IFeePayRecordService feePayRecordService;
    
    @Resource
    private ObjectMapper objectMapper;
    
    @Value("${inner.mongolia.pay.public.key}")
    private String publicKey;
    
    @ApiOperation(value = "支付成功回调", notes = "处理内蒙古支付平台的支付成功回调")
    @PostMapping("/paySuccess")
    public String paySuccessNotify(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            log.info("收到内蒙古支付成功回调: {}", params);
            
            if (!verifyNotifySign(params)) {
                log.error("内蒙古支付回调验签失败");
                return "FAIL";
            }
            
            String orderNo = (String) params.get("orderNo");
            String tradeNo = (String) params.get("tradeNo");
            String totalAmount = (String) params.get("totalAmount");
            String payWayType = (String) params.get("payWayType");
            
            if (orderNo == null || tradeNo == null) {
                log.error("内蒙古支付回调参数不完整: orderNo={}, tradeNo={}", orderNo, tradeNo);
                return "FAIL";
            }
            
            try {
                log.info("处理内蒙古支付成功回调: orderNo={}, tradeNo={}, amount={}", 
                    orderNo, tradeNo, totalAmount);
                return "SUCCESS";
            } catch (Exception e) {
                log.error("处理内蒙古支付回调失败: orderNo=" + orderNo, e);
                return "FAIL";
            }
            
        } catch (Exception e) {
            log.error("内蒙古支付回调处理异常", e);
            return "FAIL";
        }
    }
    
    @ApiOperation(value = "退款成功回调", notes = "处理内蒙古支付平台的退款成功回调")
    @PostMapping("/refundSuccess")
    public String refundSuccessNotify(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        try {
            log.info("收到内蒙古退款成功回调: {}", params);
            
            if (!verifyNotifySign(params)) {
                log.error("内蒙古退款回调验签失败");
                return "FAIL";
            }
            
            String orderNo = (String) params.get("orderNo");
            String outRefundNo = (String) params.get("outRefundNo");
            String refundFee = (String) params.get("refundFee");
            String batchNo = (String) params.get("batchNo");
            
            if (orderNo == null || outRefundNo == null) {
                log.error("内蒙古退款回调参数不完整: orderNo={}, outRefundNo={}", orderNo, outRefundNo);
                return "FAIL";
            }
            
            try {
                log.info("处理内蒙古退款成功回调: orderNo={}, outRefundNo={}, refundFee={}", 
                    orderNo, outRefundNo, refundFee);
                return "SUCCESS";
            } catch (Exception e) {
                log.error("处理内蒙古退款回调失败: orderNo=" + orderNo, e);
                return "FAIL";
            }
            
        } catch (Exception e) {
            log.error("内蒙古退款回调处理异常", e);
            return "FAIL";
        }
    }
    
    private boolean verifyNotifySign(Map<String, Object> params) {
        try {
            String sign = (String) params.get("sign");
            if (sign == null) {
                log.error("内蒙古回调缺少签名");
                return false;
            }
            
            Map<String, Object> signParams = new HashMap<>(params);
            signParams.remove("sign");
            
            return InnerMongoliaSignUtil.verifySign(signParams, publicKey, sign);
        } catch (Exception e) {
            log.error("验证内蒙古回调签名失败", e);
            return false;
        }
    }
}