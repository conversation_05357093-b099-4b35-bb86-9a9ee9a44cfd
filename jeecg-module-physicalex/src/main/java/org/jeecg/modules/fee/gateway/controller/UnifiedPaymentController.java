package org.jeecg.modules.fee.gateway.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.fee.gateway.dto.*;
import org.jeecg.modules.fee.gateway.enums.PaymentGatewayType;
import org.jeecg.modules.fee.gateway.service.IPaymentGatewayService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

@Api(tags = "统一支付网关接口")
@RestController
@RequestMapping("/fee/gateway")
@Slf4j
public class UnifiedPaymentController {
    
    @Resource
    private IPaymentGatewayService paymentGatewayService;
    
    @ApiOperation(value = "创建扫码支付", notes = "创建扫码支付订单，支持微信、支付宝等")
    @PostMapping("/createQrCodePayment")
    public Result<PaymentResponse> createQrCodePayment(@RequestBody CreateQrCodePaymentRequest request,
                                                       HttpServletRequest httpRequest) {
        try {
            PaymentRequest paymentRequest = buildPaymentRequest(request, httpRequest);
            paymentRequest.setPaymentType(PaymentRequest.PaymentType.QR_CODE);
            
            PaymentGatewayType gatewayType = determineGatewayType(request.getGatewayType());
            PaymentResponse response = paymentGatewayService.createQrCodePayment(paymentRequest, gatewayType);
            
            return Result.ok(response);
        } catch (Exception e) {
            log.error("创建扫码支付失败", e);
            return Result.error("创建扫码支付失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "创建条码支付", notes = "扫描客户付款码进行支付")
    @PostMapping("/createBarcodePayment")
    public Result<PaymentResponse> createBarcodePayment(@RequestBody CreateBarcodePaymentRequest request,
                                                        HttpServletRequest httpRequest) {
        try {
            PaymentRequest paymentRequest = buildPaymentRequest(request, httpRequest);
            paymentRequest.setPaymentType(PaymentRequest.PaymentType.BARCODE);
            paymentRequest.setAuthCode(request.getAuthCode());
            
            PaymentGatewayType gatewayType = determineGatewayType(request.getGatewayType());
            PaymentResponse response = paymentGatewayService.createBarcodePayment(paymentRequest, gatewayType);
            
            return Result.ok(response);
        } catch (Exception e) {
            log.error("创建条码支付失败", e);
            return Result.error("创建条码支付失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "查询支付订单", notes = "查询支付订单状态")
    @GetMapping("/queryPayment/{orderNo}")
    public Result<QueryResponse> queryPayment(@PathVariable String orderNo,
                                              @RequestParam(required = false) String gatewayType) {
        try {
            QueryRequest queryRequest = new QueryRequest(orderNo);
            PaymentGatewayType gateway = determineGatewayType(gatewayType);
            
            QueryResponse response = paymentGatewayService.queryPayment(queryRequest, gateway);
            
            return Result.ok(response);
        } catch (Exception e) {
            log.error("查询支付订单失败", e);
            return Result.error("查询支付订单失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "申请退款", notes = "申请退款")
    @PostMapping("/refundPayment")
    public Result<RefundResponse> refundPayment(@RequestBody RefundPaymentRequest request) {
        try {
            RefundRequest refundRequest = new RefundRequest();
            refundRequest.setOrderNo(request.getOrderNo());
            refundRequest.setRefundOrderNo(request.getRefundOrderNo());
            refundRequest.setBatchNo(request.getBatchNo());
            refundRequest.setRefundAmount(request.getRefundAmount());
            refundRequest.setReason(request.getReason());
            refundRequest.setNotifyUrl(request.getNotifyUrl());
            
            if (request.getOperatorName() != null || request.getOperatorCode() != null) {
                PaymentRequest.OperatorInfo operatorInfo = new PaymentRequest.OperatorInfo();
                operatorInfo.setOperatorName(request.getOperatorName());
                operatorInfo.setOperatorCode(request.getOperatorCode());
                refundRequest.setOperatorInfo(operatorInfo);
            }
            
            PaymentGatewayType gatewayType = determineGatewayType(request.getGatewayType());
            RefundResponse response = paymentGatewayService.refundPayment(refundRequest, gatewayType);
            
            return Result.ok(response);
        } catch (Exception e) {
            log.error("申请退款失败", e);
            return Result.error("申请退款失败: " + e.getMessage());
        }
    }
    
    @ApiOperation(value = "撤销支付", notes = "撤销支付订单")
    @PostMapping("/cancelPayment/{orderNo}")
    public Result<CancelResponse> cancelPayment(@PathVariable String orderNo,
                                                @RequestParam(required = false) String gatewayType,
                                                @RequestParam(required = false) String reason) {
        try {
            CancelRequest cancelRequest = new CancelRequest(orderNo, reason);
            PaymentGatewayType gateway = determineGatewayType(gatewayType);
            
            CancelResponse response = paymentGatewayService.cancelPayment(cancelRequest, gateway);
            
            return Result.ok(response);
        } catch (Exception e) {
            log.error("撤销支付失败", e);
            return Result.error("撤销支付失败: " + e.getMessage());
        }
    }
    
    private PaymentRequest buildPaymentRequest(BasePaymentRequest request, HttpServletRequest httpRequest) {
        PaymentRequest paymentRequest = new PaymentRequest();
        paymentRequest.setOrderNo(request.getOrderNo());
        paymentRequest.setAmount(request.getAmount());
        paymentRequest.setSubject(request.getSubject());
        paymentRequest.setBody(request.getBody());
        paymentRequest.setWayCode(request.getWayCode());
        paymentRequest.setClientIp(getClientIp(httpRequest));
        paymentRequest.setNotifyUrl(request.getNotifyUrl());
        paymentRequest.setReturnUrl(request.getReturnUrl());
        paymentRequest.setAttach(request.getAttach());
        
        if (request.getPatientName() != null || request.getIdCard() != null) {
            PaymentRequest.PatientInfo patientInfo = new PaymentRequest.PatientInfo();
            patientInfo.setPatientName(request.getPatientName());
            patientInfo.setIdCard(request.getIdCard());
            patientInfo.setPhone(request.getPatientPhone());
            patientInfo.setCardNo(request.getCardNo());
            patientInfo.setClinicCode(request.getClinicCode());
            patientInfo.setInpatientNo(request.getInpatientNo());
            patientInfo.setDeptName(request.getDeptName());
            patientInfo.setDoctorName(request.getDoctorName());
            patientInfo.setMarkType(request.getMarkType());
            patientInfo.setMarkNo(request.getMarkNo());
            paymentRequest.setPatientInfo(patientInfo);
        }
        
        if (request.getOperatorName() != null || request.getOperatorCode() != null) {
            PaymentRequest.OperatorInfo operatorInfo = new PaymentRequest.OperatorInfo();
            operatorInfo.setOperatorName(request.getOperatorName());
            operatorInfo.setOperatorCode(request.getOperatorCode());
            operatorInfo.setTerminalId(request.getTerminalId());
            paymentRequest.setOperatorInfo(operatorInfo);
        }
        
        return paymentRequest;
    }
    
    private PaymentGatewayType determineGatewayType(String gatewayType) {
        if (gatewayType != null) {
            return PaymentGatewayType.fromCode(gatewayType);
        }
        return null; // 使用默认网关
    }
    
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
    
    @Data
    public static class BasePaymentRequest {
        private String orderNo;
        private BigDecimal amount;
        private String subject;
        private String body;
        private String wayCode;
        private String notifyUrl;
        private String returnUrl;
        private String attach;
        private String gatewayType;
        
        private String patientName;
        private String idCard;
        private String patientPhone;
        private String cardNo;
        private String clinicCode;
        private String inpatientNo;
        private String deptName;
        private String doctorName;
        private String markType;
        private String markNo;
        
        private String operatorName;
        private String operatorCode;
        private String terminalId;
    }
    
    @Data
    public static class CreateQrCodePaymentRequest extends BasePaymentRequest {
    }
    
    @Data
    public static class CreateBarcodePaymentRequest extends BasePaymentRequest {
        private String authCode;
    }
    
    @Data
    public static class RefundPaymentRequest {
        private String orderNo;
        private String refundOrderNo;
        private String batchNo;
        private BigDecimal refundAmount;
        private String reason;
        private String notifyUrl;
        private String operatorName;
        private String operatorCode;
        private String gatewayType;
    }
}