package org.jeecg.modules.fee.gateway.enums;

public enum PaymentGatewayType {
    
    JEEPAY("jeepay", "JeePay支付网关", "jeePayGateway"),
    
    INNER_MONGOLIA("innerMongolia", "内蒙古医院统一支付", "innerMongoliaPayGateway");
    
    private final String code;
    private final String description;
    private final String beanName;
    
    PaymentGatewayType(String code, String description, String beanName) {
        this.code = code;
        this.description = description;
        this.beanName = beanName;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public String getBeanName() {
        return beanName;
    }
    
    public static PaymentGatewayType fromCode(String code) {
        for (PaymentGatewayType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的支付网关类型: " + code);
    }
}