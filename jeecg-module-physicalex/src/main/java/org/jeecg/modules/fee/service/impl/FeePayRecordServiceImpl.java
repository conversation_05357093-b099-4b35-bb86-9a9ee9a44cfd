package org.jeecg.modules.fee.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.jeequan.jeepay.JeepayClient;
import com.jeequan.jeepay.model.PayOrderCreateReqModel;
import com.jeequan.jeepay.request.PayOrderCreateRequest;
import com.jeequan.jeepay.response.PayOrderCreateResponse;
import com.mzlion.easyokhttp.HttpClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.Validate;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExApiConstants;
import org.jeecg.excommons.ExConstants;
import org.jeecg.excommons.jms.JmsMessageSender;
import org.jeecg.modules.basicinfo.service.ISequencesService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.fee.bo.*;
import org.jeecg.modules.fee.entity.*;
import org.jeecg.modules.fee.mapper.*;
import org.jeecg.modules.fee.service.IFeePayRecordService;
import org.jeecg.modules.reg.entity.*;
import org.jeecg.modules.reg.mapper.*;
import org.jeecg.modules.appointment.mapper.CustomerOrderMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @Description: 收费记录
 * @Author: jeecg-boot
 * @Date: 2024-06-25
 * @Version: V1.0
 */
@Slf4j
@Service
public class FeePayRecordServiceImpl extends ServiceImpl<FeePayRecordMapper, FeePayRecord> implements IFeePayRecordService {
    @Autowired
    private FeePayRecordMapper feePayRecordMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private JmsMessageSender jmsMessageSender;
    @Autowired
    private ISysSettingService sysSettingService;
    @Autowired
    private CustomerRegItemGroupMapper customerRegItemGroupMapper;
    @Autowired
    private CompanyRegMapper companyRegMapper;
    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private FeeRefundRecordMapper feeRefundRecordMapper;
    @Autowired
    private IdentifierGenerator identifierGenerator;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private CardTradeMapper cardTradeMapper;
    @Autowired
    private CardMapper cardMapper;
    @Autowired
    private CustomerOrderMapper customerOrderMapper;
    @Autowired
    private ISequencesService sequencesService;
    @Autowired
    private LimitOperationRecordMapper limitOperationRecordMapper;
    @Autowired
    private TeamCustomerLimitAmountMapper teamCustomerLimitAmountMapper;
    @Autowired
    private org.jeecg.modules.electronicbill.service.IElectronicBillAutoWriteoffService electronicBillAutoWriteoffService;
    @Autowired
    private CompanyTeamMapper companyTeamMapper;


    @Override
    public Page<CustomerReg> pageCustomer(Page<CustomerReg> page, String examNo, String idCard, String name, String regDateStart, String regDateEnd, String regStatus, String payStatus) {

        feePayRecordMapper.pageCustomer(page, examNo, idCard, name, regDateStart, regDateEnd, regStatus, payStatus);
        page.getRecords().forEach(item -> {
            BigDecimal totalPrice = feePayRecordMapper.getTotalPriceOfReg(item.getId(), ExConstants.PAYER_TYPE_个人支付);
            totalPrice = totalPrice != null ? totalPrice : BigDecimal.valueOf(0);

            BigDecimal payedAmount = feePayRecordMapper.getPayedAmountOfReg(item.getId(), ExConstants.PAYER_TYPE_个人支付);
            //payedAmount单位是分，需要转为元
            payedAmount = payedAmount != null ? payedAmount : BigDecimal.valueOf(0);

            BigDecimal remainAmount = totalPrice.subtract(payedAmount);
            item.setRemainAmount(remainAmount);
            item.setTotalPrice(totalPrice);
            item.setPayedAmount(payedAmount);

            item.setPaymentState(statPayStatus4Reg(item.getId()));
        });

        return page;
    }

    private String statPayStatus4Reg(String regId) {
        List<String> statusList = jdbcTemplate.queryForList("select distinct pay_status from customer_reg_item_group where customer_reg_id = ?", String.class, regId);
        //如果只有一个状态，直接返回
        if (statusList.size() == 1) {
            return statusList.get(0);
        }
        //如果有多个状态，判断是否有已缴费状态，有则返回已缴费，否则返回未缴费
        if (statusList.contains(ExConstants.PAY_STATUS_PAYED)) {
            return ExConstants.PAY_STATUS_PART;
        }

        return "待支付";
    }

//    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Transactional
    @Override
    public void giveAway(FeePayRecord feePayRecord) throws Exception {

        validatePaymentInfo(feePayRecord);

        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        if (customerReg == null) {
            throw new Exception("登记信息为空！");
        }
        if (StringUtils.equals(customerReg.getStatus(), ExConstants.REG_STATUS_WAIT)) {
            throw new Exception("未登记不可收费！");
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //1、支付记录
        feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
        feePayRecord.setPayChannel(ExConstants.PAY_CHANNEL_赠送);
//        feePayRecord.setAmount(BigDecimal.valueOf(0));
        feePayRecord.setAmount(feePayRecord.getAmount());
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setSuccessTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecordMapper.insert(feePayRecord);
    }
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @Override
    public void firstCheckAfterPay(FeePayRecord feePayRecord) throws Exception {

        validatePaymentInfo(feePayRecord);

        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        if (customerReg == null) {
            throw new Exception("登记信息为空！");
        }
        if (StringUtils.equals(customerReg.getStatus(), ExConstants.REG_STATUS_WAIT)) {
            throw new Exception("未登记不可收费！");
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //1、支付记录
        feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
        feePayRecord.setPayChannel(ExConstants.PAY_CHANNEL_先检后付);
        feePayRecord.setAmount(BigDecimal.valueOf(0));
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setSuccessTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecordMapper.insert(feePayRecord);
    }

    @Transactional
    @Override
    public BigDecimal payByCompany(FeePayRecord feePayRecord) throws Exception {
        validatePaymentInfo(feePayRecord);
        BigDecimal unpaidAmount = BigDecimal.ZERO;

        LoginUser loginUser = null;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
        }
        //1、支付记录
        feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
        feePayRecord.setPayChannel(ExConstants.PAY_CHANNEL_单位支付);
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setSuccessTime(new Date());
        if (loginUser != null) {
            feePayRecord.setCreator(loginUser.getRealname());
            feePayRecord.setCreateBy(loginUser.getUsername());
        }

        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        //只考虑本人团检，没有额度的情况
        if (StringUtils.isNotBlank(feePayRecord.getTeamId())&&StringUtils.isBlank(feePayRecord.getOriginCustomerLimitAmountId())){
            //查询分组信息
            CompanyTeam companyTeam = companyTeamMapper.selectById(feePayRecord.getTeamId());
            Validate.notNull(companyTeam, "未查询到团检分组信息");
            //没有分组额度为null或者为0都不会建额度账号，均当做无额度处理
            if(Objects.isNull(companyTeam.getLimitAmount())||BigDecimal.ZERO.compareTo(companyTeam.getLimitAmount())==0){
                save(feePayRecord);
                return unpaidAmount;
            }
        }


        //处理额度相关操作
        List<FeePayRecord> list = deductCompanyLimitAmount(feePayRecord, loginUser, unpaidAmount);
        if (CollectionUtils.isNotEmpty(list)) {
            saveOrUpdateBatch(list);
        }
//        feePayRecordMapper.insert(feePayRecord);
      return unpaidAmount;
    }
    private List<FeePayRecord> deductCompanyLimitAmount(FeePayRecord feePayRecord, LoginUser loginUser,BigDecimal unpaidAmount) throws Exception{
        final BigDecimal payAmount = feePayRecord.getAmount();
        final String customerId = feePayRecord.getCustomerId();
        final String teamId = feePayRecord.getTeamId();
        final String operator = Objects.nonNull(loginUser) ? loginUser.getUsername() : "";
        List<FeePayRecord> list= Lists.newArrayList();
        // 1. 纯本人额度扣减
        if (StringUtils.isBlank(feePayRecord.getOriginCustomerLimitAmountId())) {
            TeamCustomerLimitAmount selfLimit = teamCustomerLimitAmountMapper.getLatestLimit(teamId,customerId,null);
            if (selfLimit == null){
                feePayRecord.setAmount(BigDecimal.ZERO);
//                feePayRecord.setState(ExConstants.PAY_STATE_支付中);
                throw new Exception("未查询到本人可用额度");
            }
            feePayRecord.setBizInfo(selfLimit.getId());
            feePayRecord.setRemark("本人额度");
            save(feePayRecord);
            BigDecimal remaining = deductLimit(selfLimit, payAmount, feePayRecord, operator, "本人额度支付","支付本人项目");

            // 额度不足时更新状态
            if (remaining.compareTo(BigDecimal.ZERO) > 0) {
//                feePayRecord.setState(ExConstants.PAY_STATE_支付中);
                feePayRecord.setAmount(selfLimit.getAmount());
                unpaidAmount = remaining;
            }
            list.add(feePayRecord);
            return list;
        }
        // 2. 混合额度（本人+亲友）
        else if (StringUtils.isNotBlank(teamId)) {
            BigDecimal remaining = payAmount;
            FeePayRecord selfPayRecord=new FeePayRecord();
            // 先扣本人额度
            TeamCustomerLimitAmount selfLimit = teamCustomerLimitAmountMapper.getLatestLimit(teamId,customerId, null);
            if (selfLimit != null) {
                BeanUtils.copyProperties( feePayRecord,selfPayRecord);
                selfPayRecord.setBizInfo(selfLimit.getId());
                selfPayRecord.setRemark("本人额度");
                save(selfPayRecord);
                remaining = deductLimit(selfLimit, remaining, selfPayRecord, operator, "本人额度支付","支付本人项目");
                list.add(selfPayRecord);
            }

            // 本人额度不足再扣亲友额度
            if (remaining.compareTo(BigDecimal.ZERO) > 0) {
                if (selfLimit != null) {
                    selfPayRecord.setAmount(selfLimit.getAmount());
                }
                TeamCustomerLimitAmount shareLimit = teamCustomerLimitAmountMapper.selectById(feePayRecord.getOriginCustomerLimitAmountId());
                if (shareLimit != null) {
                    FeePayRecord sharePayRecord=new FeePayRecord();
                    BeanUtils.copyProperties( feePayRecord,sharePayRecord);
                    sharePayRecord.setBizInfo(shareLimit.getId());
                    sharePayRecord.setAmount(remaining);
                    sharePayRecord.setRemark("亲友额度");
                    save(sharePayRecord);
                    BigDecimal afterDeduct = deductLimit(shareLimit, remaining, sharePayRecord, operator, "亲友额度支付","支付亲友项目");
                    list.add(sharePayRecord);
                    // 亲友额度仍不足
                    if (afterDeduct.compareTo(BigDecimal.ZERO) > 0) {
//                        selfPayRecord.setState(ExConstants.PAY_STATE_支付中);
//                        sharePayRecord.setState(ExConstants.PAY_STATE_支付中);
                        sharePayRecord.setAmount(shareLimit.getAmount());
                        unpaidAmount = afterDeduct;
                    }
                } else {
                    unpaidAmount = remaining;
//                    selfPayRecord.setState(ExConstants.PAY_STATE_支付中);
                }
            }

            return list;
        }
        // 3. 纯亲友额度
        else {
            TeamCustomerLimitAmount shareLimit = teamCustomerLimitAmountMapper.selectById(feePayRecord.getOriginCustomerLimitAmountId());
            if (shareLimit == null) {
                feePayRecord.setAmount(BigDecimal.ZERO);
//                feePayRecord.setState(ExConstants.PAY_STATE_支付中);
                throw new Exception("未查询到亲友可用额度");
            }
            feePayRecord.setBizInfo(shareLimit.getId());
            feePayRecord.setRemark("亲友额度");
            save(feePayRecord);
            BigDecimal remaining = deductLimit(shareLimit, payAmount, feePayRecord, operator, "亲友额度支付","支付亲友项目");
            if (remaining.compareTo(BigDecimal.ZERO) > 0) {
//                feePayRecord.setState(ExConstants.PAY_STATE_支付中);
                feePayRecord.setAmount(shareLimit.getAmount());
                unpaidAmount = remaining;
            }

            list.add(feePayRecord);
            return list;
        }
    }



    // 执行额度扣减
    private BigDecimal deductLimit(TeamCustomerLimitAmount limitRecord, BigDecimal deductAmount, FeePayRecord feePayRecord, String operator, String operationType, String businessDesc) {
        BigDecimal currentAmount = limitRecord.getAmount();

        // 全额扣减
        if (currentAmount.compareTo(deductAmount) >= 0) {
            updateLimitAmount(limitRecord.getId(), currentAmount.subtract(deductAmount));
            addOperationRecord(limitRecord.getId(),feePayRecord, deductAmount.negate(), operator, operationType,businessDesc);
            return BigDecimal.ZERO;
        }else {
            // 部分扣减
            updateLimitAmount(limitRecord.getId(), BigDecimal.ZERO);
            addOperationRecord(limitRecord.getId(),feePayRecord, currentAmount.negate(), operator, operationType,businessDesc);
            return deductAmount.subtract(currentAmount);
        }
    }

    // 更新额度金额
    private void updateLimitAmount(String id, BigDecimal newAmount) {
        teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, newAmount).eq(TeamCustomerLimitAmount::getId, id)
        );
    }

    // 创建操作记录
    private void addOperationRecord(String limitId,FeePayRecord feePayRecord, BigDecimal amount, String operator, String operationType, String businessDesc) {
        LimitOperationRecord record = new LimitOperationRecord();
        record.setCustomerRegId(feePayRecord.getCustomerRegId());
        record.setName(feePayRecord.getName());
        record.setExamNo(feePayRecord.getExamNo());
        record.setAmount(amount);
        record.setOperation(operationType);
        record.setBusinessDesc(businessDesc);
        record.setCreateTime(new Date());
        record.setCreateBy(operator);
        record.setBizId(feePayRecord.getId());
        record.setLimitId(limitId);
        limitOperationRecordMapper.insert(record);
    }
    /*private void dealCompanyLimitAmount(FeePayRecord feePayRecord, LoginUser loginUser) {
        //扣减额度，本人额度
        if (StringUtils.isBlank(feePayRecord.getOriginCustomerLimitAmountId())) {
            TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getCustomerId, feePayRecord.getCustomerId()).eq(TeamCustomerLimitAmount::getTeamId, feePayRecord.getTeamId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
            if (Objects.nonNull(customerLimitAmount)) {
                BigDecimal limitAmountAmount = customerLimitAmount.getAmount();
                if (limitAmountAmount.compareTo(feePayRecord.getAmount()) >= 0) {
                    teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, customerLimitAmount.getAmount().subtract(feePayRecord.getAmount())).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                    //额度操作记录
                    LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                    limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                    limitOperationRecord.setName(feePayRecord.getName());
                    limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                    limitOperationRecord.setAmount(feePayRecord.getAmount().negate());
                    limitOperationRecord.setOperation("额度支付");
                    limitOperationRecord.setCreateTime(new Date());
                    limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                    limitOperationRecord.setBizId(feePayRecord.getId());
                    limitOperationRecordMapper.insert(limitOperationRecord);
                } else {
                    teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, BigDecimal.ZERO).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                    //额度操作记录
                    LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                    limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                    limitOperationRecord.setName(feePayRecord.getName());
                    limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                    limitOperationRecord.setAmount(limitAmountAmount.negate());
                    limitOperationRecord.setOperation("额度支付");
                    limitOperationRecord.setCreateTime(new Date());
                    limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                    limitOperationRecord.setBizId(feePayRecord.getId());
                    limitOperationRecordMapper.insert(limitOperationRecord);

                    feePayRecord.setState(ExConstants.PAY_STATE_支付中);
                }
            }
        }else{
            //既有本人额度，又有亲友额度
            if (StringUtils.isNotBlank(feePayRecord.getTeamId())){
                //先扣本人额度
                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getCustomerId, feePayRecord.getCustomerId()).eq(TeamCustomerLimitAmount::getTeamId, feePayRecord.getTeamId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
                if (Objects.nonNull(customerLimitAmount)) {
                    BigDecimal limitAmountAmount = customerLimitAmount.getAmount();
                    if (limitAmountAmount.compareTo(feePayRecord.getAmount())>=0) {
                        teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, customerLimitAmount.getAmount().subtract(feePayRecord.getAmount())).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                        //额度操作记录
                        LimitOperationRecord selfLimitOperationRecord = new LimitOperationRecord();
                        selfLimitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                        selfLimitOperationRecord.setName(feePayRecord.getName());
                        selfLimitOperationRecord.setExamNo(feePayRecord.getExamNo());
                        selfLimitOperationRecord.setAmount(feePayRecord.getAmount().negate());
                        selfLimitOperationRecord.setOperation("额度支付");
                        selfLimitOperationRecord.setCreateTime(new Date());
                        selfLimitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                        selfLimitOperationRecord.setBizId(feePayRecord.getId());
                        limitOperationRecordMapper.insert(selfLimitOperationRecord);
                    }else{
                        teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, BigDecimal.ZERO).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                        //额度操作记录
                        LimitOperationRecord selfLimitOperationRecord = new LimitOperationRecord();
                        selfLimitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                        selfLimitOperationRecord.setName(feePayRecord.getName());
                        selfLimitOperationRecord.setExamNo(feePayRecord.getExamNo());
                        selfLimitOperationRecord.setAmount(limitAmountAmount.negate());
                        selfLimitOperationRecord.setOperation("额度支付");
                        selfLimitOperationRecord.setCreateTime(new Date());
                        selfLimitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                        selfLimitOperationRecord.setBizId(feePayRecord.getId());
                        limitOperationRecordMapper.insert(selfLimitOperationRecord);
                         //本人不够扣再扣亲友额度
                        TeamCustomerLimitAmount shareLimitAmount = teamCustomerLimitAmountMapper.selectById(feePayRecord.getOriginCustomerLimitAmountId());
                        BigDecimal remainingAmount = feePayRecord.getAmount().subtract(customerLimitAmount.getAmount());
                        if (Objects.nonNull(shareLimitAmount)) {
                            BigDecimal shareAmount = shareLimitAmount.getAmount();
                            if (shareAmount.compareTo(remainingAmount)>=0) {
                                teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, shareLimitAmount.getAmount().subtract(remainingAmount)).eq(TeamCustomerLimitAmount::getId, shareLimitAmount.getId()));
                                //额度操作记录
                                LimitOperationRecord shareLimitOperationRecord = new LimitOperationRecord();
                                shareLimitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                                shareLimitOperationRecord.setName(feePayRecord.getName());
                                shareLimitOperationRecord.setExamNo(feePayRecord.getExamNo());
                                shareLimitOperationRecord.setAmount(remainingAmount.negate());
                                shareLimitOperationRecord.setOperation("额度支付");
                                shareLimitOperationRecord.setCreateTime(new Date());
                                shareLimitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                shareLimitOperationRecord.setBizId(feePayRecord.getId());
                                limitOperationRecordMapper.insert(shareLimitOperationRecord);
                            }else {
                                teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, BigDecimal.ZERO).eq(TeamCustomerLimitAmount::getId, shareLimitAmount.getId()));
                                //额度操作记录
                                LimitOperationRecord shareLimitOperationRecord = new LimitOperationRecord();
                                shareLimitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                                shareLimitOperationRecord.setName(feePayRecord.getName());
                                shareLimitOperationRecord.setExamNo(feePayRecord.getExamNo());
                                shareLimitOperationRecord.setAmount(shareAmount.negate());
                                shareLimitOperationRecord.setOperation("额度支付");
                                shareLimitOperationRecord.setCreateTime(new Date());
                                shareLimitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                                shareLimitOperationRecord.setBizId(feePayRecord.getId());
                                limitOperationRecordMapper.insert(shareLimitOperationRecord);
                                feePayRecord.setState(ExConstants.PAY_STATE_支付中);
                            }
                        }else{
                            feePayRecord.setState(ExConstants.PAY_STATE_支付中);
                        }

                    }
                }


            }else {
                //只有亲友额度
                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectById(feePayRecord.getOriginCustomerLimitAmountId());
                if (Objects.nonNull(customerLimitAmount)) {
                    BigDecimal shareAmount = customerLimitAmount.getAmount();
                    if (shareAmount.compareTo(feePayRecord.getAmount()) >= 0) {
                        teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, customerLimitAmount.getAmount().subtract(feePayRecord.getAmount())).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                        //额度操作记录
                        LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                        limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                        limitOperationRecord.setName(feePayRecord.getName());
                        limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                        limitOperationRecord.setAmount(feePayRecord.getAmount().negate());
                        limitOperationRecord.setOperation("额度支付");
                        limitOperationRecord.setCreateTime(new Date());
                        limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                        limitOperationRecord.setBizId(feePayRecord.getId());
                        limitOperationRecordMapper.insert(limitOperationRecord);
                    } else {
                        teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, BigDecimal.ZERO).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                        //额度操作记录
                        LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                        limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                        limitOperationRecord.setName(feePayRecord.getName());
                        limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                        limitOperationRecord.setAmount(shareAmount.negate());
                        limitOperationRecord.setOperation("额度支付");
                        limitOperationRecord.setCreateTime(new Date());
                        limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                        limitOperationRecord.setBizId(feePayRecord.getId());
                        limitOperationRecordMapper.insert(limitOperationRecord);
                        feePayRecord.setState(ExConstants.PAY_STATE_支付中);
                    }
                }
            }
        }
    }*/

    @Transactional
    @Override
    public void invalidatePayByCompany(FeePayRecord feePayRecord) throws Exception {
        validatePaymentInfo(feePayRecord);

        LoginUser loginUser = null;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
        }
/*        //1、支付记录
        feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
        feePayRecord.setPayChannel(ExConstants.PAY_CHANNEL_单位支付);
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setSuccessTime(new Date());
        if (loginUser != null) {
            feePayRecord.setCreator(loginUser.getRealname());
            feePayRecord.setCreateBy(loginUser.getUsername());
        }

        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecordMapper.insert(feePayRecord);*/
        //扣减额度
        if (StringUtils.isBlank(feePayRecord.getOriginCustomerLimitAmountId())) {
            TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getLatestLimit(feePayRecord.getTeamId(),feePayRecord.getCustomerId(), null);
//            TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectOne(new LambdaQueryWrapper<TeamCustomerLimitAmount>().eq(TeamCustomerLimitAmount::getCustomerId, feePayRecord.getCustomerId()).eq(TeamCustomerLimitAmount::getTeamId, feePayRecord.getTeamId()).orderByDesc(TeamCustomerLimitAmount::getCreateTime).last("limit 1"));
            if (Objects.nonNull(customerLimitAmount)) {
                teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, customerLimitAmount.getAmount().subtract(feePayRecord.getAmount())).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                //额度操作记录
                LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                limitOperationRecord.setName(feePayRecord.getName());
                limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                limitOperationRecord.setAmount(feePayRecord.getAmount().negate());
                limitOperationRecord.setOperation("作废退款单，重新扣除额度");
                limitOperationRecord.setBusinessDesc("作废退款单，重新扣除额度");
                limitOperationRecord.setCreateTime(new Date());
                limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                limitOperationRecord.setBizId(feePayRecord.getId());
                limitOperationRecordMapper.insert(limitOperationRecord);
            }
        }else{
            TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectById(feePayRecord.getOriginCustomerLimitAmountId());
            if (Objects.nonNull(customerLimitAmount)) {
                teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, customerLimitAmount.getAmount().subtract(feePayRecord.getAmount())).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                //额度操作记录
                LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                limitOperationRecord.setName(feePayRecord.getName());
                limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                limitOperationRecord.setAmount(feePayRecord.getAmount().negate());
                limitOperationRecord.setOperation("作废退款单，重新扣除额度");
                limitOperationRecord.setBusinessDesc("作废退款单，重新扣除额度");
                limitOperationRecord.setCreateTime(new Date());
                limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                limitOperationRecord.setBizId(feePayRecord.getId());
                limitOperationRecordMapper.insert(limitOperationRecord);
            }
        }

    }

    @Override
    public BatchResult<CustomerReg> sendFee2HisBatch(List<CustomerReg> list, String clientIp) throws Exception {
        BatchResult<CustomerReg> batchResult = new BatchResult<>();
        List<BatchResult.FailureResult<CustomerReg>> failureResults = new ArrayList<>();
        List<CustomerReg> successResults = new ArrayList<>();
        for (CustomerReg customerReg : list) {
            try {
                //判断是否为登记状态
                if (!ExConstants.REG_STATUS_REGED.equals(customerReg.getStatus())) {
                    failureResults.add(new BatchResult.FailureResult<>(customerReg, "未登记！"));
                    continue;
                }
                //获取CustomerRegItemGroup列表
                List<CustomerRegItemGroup> customerRegItemGroupList = customerRegItemGroupMapper.listByRegAndDepart(customerReg.getId(), null);
                if (CollectionUtils.isEmpty(customerRegItemGroupList)) {
                    failureResults.add(new BatchResult.FailureResult<>(customerReg, "没有体检项目！"));
                    continue;
                }
                //过滤出未支付的项目
                List<CustomerRegItemGroup> unPayList = customerRegItemGroupList.stream().filter(item -> ExConstants.PAY_STATUS_WAIT.equals(item.getPayStatus())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(unPayList)) {
                    failureResults.add(new BatchResult.FailureResult<>(customerReg, "没有未支付的项目！"));
                    continue;
                }
                //发送费用到HIS
                //组装FeePayRecord
                FeePayRecord feePayRecord = new FeePayRecord();
                feePayRecord.setBizId(customerReg.getId());
                feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
                feePayRecord.setCurrency("CNY");
                feePayRecord.setClientIp(clientIp);
                feePayRecord.setCustomerRegId(customerReg.getId());
                feePayRecord.setExamNo(customerReg.getExamNo());
                feePayRecord.setPayChannel(ExConstants.PAY_CHANNEL_门诊);
                BigDecimal totalAmount = unPayList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
                feePayRecord.setAmount(totalAmount);
                feePayRecord.setCustomerRegItemGroupIds(unPayList.stream().map(CustomerRegItemGroup::getId).toList());
                try {
                    payByHis(feePayRecord);
                } catch (Exception e) {
                    failureResults.add(new BatchResult.FailureResult<>(customerReg, e.getMessage()));
                    continue;
                }

                successResults.add(customerReg);
            } catch (Exception e) {
                log.error("发送费用到HIS失败", e);
                failureResults.add(new BatchResult.FailureResult<>(customerReg, e.getMessage()));
            }
        }
        batchResult.setSuccessResults(successResults);
        batchResult.setFailureResults(failureResults);
        batchResult.setTotalProcessed(list.size());
        return batchResult;
    }

    @Override
    public CustomerReg getCustomerRegWithFee(String customerRegId) {
        CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
        if (customerReg == null) {
            return null;
        }
        BigDecimal totalPrice = feePayRecordMapper.getTotalPriceOfReg(customerReg.getId(), ExConstants.PAYER_TYPE_个人支付);
        totalPrice = totalPrice != null ? totalPrice : BigDecimal.valueOf(0);

        BigDecimal payedAmount = feePayRecordMapper.getPayedAmountOfReg(customerReg.getId(), ExConstants.PAYER_TYPE_个人支付);
        //payedAmount单位是分，需要转为元
        payedAmount = payedAmount != null ? payedAmount : BigDecimal.valueOf(0);

        BigDecimal remainAmount = totalPrice.subtract(payedAmount);
        customerReg.setRemainAmount(remainAmount);
        customerReg.setTotalPrice(totalPrice);
        customerReg.setPayedAmount(payedAmount);

        return customerReg;
    }

    @Override
    public void validatePaymentInfo(FeePayRecord feePayRecord) throws Exception {
        if (feePayRecord == null) {
            throw new Exception("支付信息为空！");
        }
        if (feePayRecord.getAmount() == null || feePayRecord.getAmount().compareTo(BigDecimal.valueOf(0)) < 0) {
            throw new Exception("支付金额为空或小于0！");
        }
        if (StringUtils.isBlank(feePayRecord.getPayChannel())) {
            throw new Exception("支付类型为空！");
        }
    }


    @Transactional
    @Override
    public void payByCard(FeePayRecord feePayRecord) throws Exception {

        validatePaymentInfo(feePayRecord);

        if (StringUtils.isBlank(feePayRecord.getCardNo())) {
            throw new Exception("卡号为空！");
        }
        if (StringUtils.isBlank(feePayRecord.getPwd())) {
            throw new Exception("密码为空！");
        }
        Card card = cardMapper.getByCardNo(feePayRecord.getCardNo());
        if (card == null) {
            throw new Exception("卡号不存在！");
        }
        if (!StringUtils.equals(card.getPwd(), feePayRecord.getPwd())) {
            throw new Exception("密码错误！");
        }

        if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已锁定)) {
            throw new Exception("卡片已锁定！");
        }
        if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已作废)) {
            throw new Exception("卡片已作废！");
        }
        if (!StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已激活)) {
            throw new Exception("卡片状态异常！");
        }
        //判断卡内余额是否足够
        BigDecimal balance = cardTradeMapper.getBalance(card.getCardNo());
        if (balance.compareTo(feePayRecord.getAmount()) < 0) {
            throw new Exception("卡内余额不足！");
        }
        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        if (customerReg == null) {
            throw new Exception("登记信息为空！");
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //1、支付记录
        CardTrade cardTrade = new CardTrade();
        cardTrade.setCardId(card.getId());
        cardTrade.setCardNo(feePayRecord.getCardNo());
        cardTrade.setAmount(feePayRecord.getAmount().negate());
        cardTrade.setTradeType(ExConstants.CARD_TRADE_TYPE_出);
        cardTrade.setCategory(ExConstants.PAY_BIZ_TYPE_体检费);
        cardTrade.setCreateTime(new Date());
        cardTrade.setBizId(feePayRecord.getBizId());
        cardTrade.setStatus(ExConstants.CARD_TRADE_STATUS_成功);
        cardTrade.setCreateBy(loginUser.getUsername());
        cardTrade.setBlance(balance.subtract(feePayRecord.getAmount()));
        cardTradeMapper.insert(cardTrade);
        //更新卡内余额
        LambdaUpdateWrapper<Card> cardLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        cardLambdaUpdateWrapper.set(Card::getBalance, balance.subtract(feePayRecord.getAmount())).eq(Card::getId, card.getId());
        cardMapper.update(null, cardLambdaUpdateWrapper);

        feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setSuccessTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecord.setChannelOrderNo(cardTrade.getId());
        feePayRecord.setBizInfo(card.getCardNo());
        feePayRecordMapper.insert(feePayRecord);
    }

    @Transactional
    @Override
    public void invalidatePayByCard(FeePayRecord feePayRecord) throws Exception {

        validatePaymentInfo(feePayRecord);

        if (StringUtils.isBlank(feePayRecord.getCardNo())) {
            throw new Exception("卡号为空！");
        }
        if (StringUtils.isBlank(feePayRecord.getPwd())) {
            throw new Exception("密码为空！");
        }
        Card card = cardMapper.getByCardNo(feePayRecord.getCardNo());
        if (card == null) {
            throw new Exception("卡号不存在！");
        }
        if (!StringUtils.equals(card.getPwd(), feePayRecord.getPwd())) {
            throw new Exception("密码错误！");
        }

        if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已锁定)) {
            throw new Exception("卡片已锁定！");
        }
        if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已作废)) {
            throw new Exception("卡片已作废！");
        }
        if (!StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已激活)) {
            throw new Exception("卡片状态异常！");
        }
        //判断卡内余额是否足够
        BigDecimal balance = cardTradeMapper.getBalance(card.getCardNo());
        if (balance.compareTo(feePayRecord.getAmount()) < 0) {
            throw new Exception("卡内余额不足！");
        }
        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        if (customerReg == null) {
            throw new Exception("登记信息为空！");
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //1、支付记录
        CardTrade cardTrade = new CardTrade();
        cardTrade.setCardId(card.getId());
        cardTrade.setCardNo(feePayRecord.getCardNo());
        cardTrade.setAmount(feePayRecord.getAmount().negate());
        cardTrade.setTradeType(ExConstants.CARD_TRADE_TYPE_出);
        cardTrade.setCategory(ExConstants.PAY_BIZ_TYPE_体检费);
        cardTrade.setCreateTime(new Date());
        cardTrade.setBizId(feePayRecord.getBizId());
        cardTrade.setStatus(ExConstants.CARD_TRADE_STATUS_成功);
        cardTrade.setCreateBy(loginUser.getUsername());
        cardTrade.setBlance(balance.subtract(feePayRecord.getAmount()));
        cardTrade.setRemark("作废退款单，重新扣除卡的金额");
        cardTradeMapper.insert(cardTrade);
        //更新卡内余额
        LambdaUpdateWrapper<Card> cardLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        cardLambdaUpdateWrapper.set(Card::getBalance, balance.subtract(feePayRecord.getAmount())).eq(Card::getId, card.getId());
        cardMapper.update(null, cardLambdaUpdateWrapper);

    }

    @Transactional
    @Override
    public void refundCard(FeePayRecord feePayRecord) throws Exception {
        if (feePayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("退款金额小于0，无法退款！");
        }
        String customerRegId = feePayRecord.getBizId();
        //1、进行退款
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //退费
        FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
        feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
        feeRefundRecord.setBillNo(feePayRecord.getBillNo());
        feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
        feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
        feeRefundRecord.setPayRecordId(feePayRecord.getId());
        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款成功);
        feeRefundRecord.setRefundAmount(feePayRecord.getApplyRefundAmount());
        feeRefundRecord.setCreateBy(loginUser.getUsername());
        feeRefundRecord.setCreatedTime(new Date());
        feeRefundRecord.setSuccessTime(new Date());
        feeRefundRecord.setCreator(loginUser.getRealname());
        feeRefundRecord.setCustomerRegId(customerRegId);
        feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
        feeRefundRecord.setExamNo(feePayRecord.getExamNo());
        feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
        feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setName(feePayRecord.getName());
        feeRefundRecord.setCurrency("cny");
        feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setClientIp(feePayRecord.getClientIp());
        feeRefundRecordMapper.insert(feeRefundRecord);

        //记录卡交易
        String originCardTradeId = feePayRecord.getChannelOrderNo();
        CardTrade originCardTrade = cardTradeMapper.selectById(originCardTradeId);
        Card card = cardMapper.getByCardNo(originCardTrade.getCardNo());
        if (StringUtils.equals(card.getStatus(), ExConstants.CRAD_STATUS_已激活)) {
            CardTrade cardTrade = new CardTrade();
            cardTrade.setCardId(card.getId());
            cardTrade.setCardNo(originCardTrade.getCardNo());
            cardTrade.setAmount(feePayRecord.getApplyRefundAmount());
            cardTrade.setTradeType(ExConstants.CARD_TRADE_TYPE_入);
            cardTrade.setCategory(ExConstants.PAY_BIZ_TYPE_体检费);
            cardTrade.setCreateTime(new Date());
            cardTrade.setStatus(ExConstants.CARD_TRADE_STATUS_成功);
            cardTrade.setBizId(feeRefundRecord.getId());
            BigDecimal balance = cardTradeMapper.getBalance(originCardTrade.getCardNo());
            cardTrade.setBlance(balance.add(cardTrade.getAmount()));
            cardTradeMapper.insert(cardTrade);
            //更新卡内余额
            LambdaUpdateWrapper<Card> cardLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            cardLambdaUpdateWrapper.set(Card::getBalance, balance.add(cardTrade.getAmount())).eq(Card::getCardNo, cardTrade.getCardNo());
            cardMapper.update(null, cardLambdaUpdateWrapper);

        }

        jdbcTemplate.update("update fee_pay_record set refund_amount=ifnull(refund_amount,0)+?,refund_times = ifnull(refund_times,0)+1,refund_state=? where id=?", feePayRecord.getApplyRefundAmount(), ExConstants.REFUND_STATE_退款成功, feePayRecord.getId());

        // 自动冲红电子票据
        try {
            electronicBillAutoWriteoffService.autoWriteoffAfterRefund(
                feePayRecord.getBillId(),
                "现金退款",
                loginUser.getRealname()
            );
        } catch (Exception e) {
            log.warn("现金退款后自动冲红电子票据失败：{}", e.getMessage());
        }

        feePayRecord.setRefundDone(true);
    }

    @Transactional
    @Override
    public void refund4Company(FeePayRecord feePayRecord) throws Exception {
        if (feePayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("退款金额小于0，无法退款！");
        }
        String customerRegId = feePayRecord.getBizId();
        //1、进行退款
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //退费
        FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
        feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
        feeRefundRecord.setBillNo(feePayRecord.getBillNo());
        feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
        feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
        feeRefundRecord.setPayRecordId(feePayRecord.getId());
        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款成功);
        feeRefundRecord.setRefundAmount(feePayRecord.getApplyRefundAmount());
        feeRefundRecord.setCreateBy(loginUser.getUsername());
        feeRefundRecord.setCreatedTime(new Date());
        feeRefundRecord.setSuccessTime(new Date());
        feeRefundRecord.setCreator(loginUser.getRealname());
        feeRefundRecord.setCustomerRegId(customerRegId);
        feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
        feeRefundRecord.setExamNo(feePayRecord.getExamNo());
        feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
        feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setName(feePayRecord.getName());
        feeRefundRecord.setCurrency("cny");
        feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setClientIp(feePayRecord.getClientIp());
        feeRefundRecordMapper.insert(feeRefundRecord);

        jdbcTemplate.update("update fee_pay_record set refund_amount=ifnull(refund_amount,0)+?,refund_times = ifnull(refund_times,0)+1,refund_state=? where id=?", feePayRecord.getApplyRefundAmount(), ExConstants.REFUND_STATE_退款成功, feePayRecord.getId());

        // 自动冲红电子票据
        try {
            electronicBillAutoWriteoffService.autoWriteoffAfterRefund(
                feePayRecord.getBillId(),
                "公司退款",
                loginUser.getRealname()
            );
        } catch (Exception e) {
            log.warn("公司退款后自动冲红电子票据失败：{}", e.getMessage());
        }

        //登记记录增加额度
        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        if (StringUtils.isNotBlank(feePayRecord.getBizInfo())){
            TeamCustomerLimitAmount teamCustomerLimitAmount = teamCustomerLimitAmountMapper.selectById(feePayRecord.getBizInfo());
            if (Objects.nonNull(teamCustomerLimitAmount)) {
                teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, feePayRecord.getApplyRefundAmount().add(teamCustomerLimitAmount.getAmount())).eq(TeamCustomerLimitAmount::getId, teamCustomerLimitAmount.getId()));
                //添加额度操作记录
                LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                limitOperationRecord.setName(feePayRecord.getName());
                limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                limitOperationRecord.setAmount(feePayRecord.getApplyRefundAmount());
                limitOperationRecord.setLimitId(teamCustomerLimitAmount.getId());
                limitOperationRecord.setBizId(feeRefundRecord.getId());
                limitOperationRecord.setOperation(feePayRecord.getRemark()+"退费");
                String businessDesc = switch(feePayRecord.getRemark()) {
                    case "本人额度" -> "本人项目退费";
                    case "亲友额度" -> "亲友项目退费";
                    default -> "退费";
                };
                limitOperationRecord.setBusinessDesc(businessDesc);
                limitOperationRecord.setCreateTime(new Date());
                limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                limitOperationRecordMapper.insert(limitOperationRecord);
            }
            //处理6月9日之前的情况，后续可注掉此内容
        }else {
            if (StringUtils.isBlank(customerReg.getOriginCustomerLimitAmountId())) {
                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.getCustomerLimitAmountByRegId(feePayRecord.getCustomerRegId());
                if (Objects.nonNull(customerLimitAmount)) {
                    teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, feePayRecord.getApplyRefundAmount().add(customerLimitAmount.getAmount())).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                    //添加额度操作记录
                    LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                    limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                    limitOperationRecord.setName(feePayRecord.getName());
                    limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                    limitOperationRecord.setAmount(feePayRecord.getApplyRefundAmount());
                    limitOperationRecord.setLimitId(customerLimitAmount.getId());
                    limitOperationRecord.setBizId(feeRefundRecord.getId());
                    limitOperationRecord.setOperation("本人额度退费");
                    limitOperationRecord.setBusinessDesc("本人项目退费");
                    limitOperationRecord.setCreateTime(new Date());
                    limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                    limitOperationRecordMapper.insert(limitOperationRecord);
                }
            } else {
                TeamCustomerLimitAmount customerLimitAmount = teamCustomerLimitAmountMapper.selectById(customerReg.getOriginCustomerLimitAmountId());
                if (Objects.nonNull(customerLimitAmount)) {
                    teamCustomerLimitAmountMapper.update(null, new LambdaUpdateWrapper<TeamCustomerLimitAmount>().set(TeamCustomerLimitAmount::getAmount, feePayRecord.getApplyRefundAmount().add(customerLimitAmount.getAmount())).eq(TeamCustomerLimitAmount::getId, customerLimitAmount.getId()));
                    //添加额度操作记录
                    LimitOperationRecord limitOperationRecord = new LimitOperationRecord();
                    limitOperationRecord.setCustomerRegId(feePayRecord.getCustomerRegId());
                    limitOperationRecord.setName(feePayRecord.getName());
                    limitOperationRecord.setExamNo(feePayRecord.getExamNo());
                    limitOperationRecord.setAmount(feePayRecord.getApplyRefundAmount());
                    limitOperationRecord.setLimitId(customerLimitAmount.getId());
                    limitOperationRecord.setBizId(feeRefundRecord.getId());
                    limitOperationRecord.setOperation("亲友额度退费");
                    limitOperationRecord.setBusinessDesc("亲友项目退费");
                    limitOperationRecord.setCreateTime(new Date());
                    limitOperationRecord.setCreateBy(Objects.nonNull(loginUser) ? loginUser.getUsername() : "");
                    limitOperationRecordMapper.insert(limitOperationRecord);
                }
            }
        }
        feePayRecord.setRefundDone(true);
    }


    @Override
    public List<FeePayRecord> getFeePayRecord4Refund(List<String> idList) {
        if (idList == null || idList.isEmpty()) {
            return null;
        }
        LambdaUpdateWrapper<FeePayRecord> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(FeePayRecord::getId, idList);
        List<FeePayRecord> feePayRecords = feePayRecordMapper.selectList(lambdaUpdateWrapper);
        if (feePayRecords == null || feePayRecords.isEmpty()) {
            return null;
        }
        feePayRecords.forEach(item -> {
            if (StringUtils.equals(item.getPayChannel(), ExConstants.PAY_CHANNEL_组合支付)) {
                LambdaUpdateWrapper<FeePayRecord> subLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                subLambdaUpdateWrapper.eq(FeePayRecord::getPid, item.getId());
                List<FeePayRecord> subPayRecords = feePayRecordMapper.selectList(subLambdaUpdateWrapper);
                item.setSubPayRecords(subPayRecords);
            }
        });

        return feePayRecords;
    }


    @Transactional
    @Override
    public void payOffline(FeePayRecord feePayRecord) throws Exception {
        validatePaymentInfo(feePayRecord);

        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        List<String> customerRegItemGroupIds = feePayRecord.getCustomerRegItemGroupIds();

        feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setSuccessTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecordMapper.insert(feePayRecord);
    }


    @Transactional
    @Override
    public void payReceipt(FeePayRecord feePayRecord) throws Exception {
        validatePaymentInfo(feePayRecord);
        if (StringUtils.isBlank(feePayRecord.getReceiptPic())) {
            throw new Exception("未上传支付凭证！");
        }

        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setSuccessTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecordMapper.insert(feePayRecord);
    }


    @Transactional
    @Override
    public void payByHis(FeePayRecord feePayRecord) throws Exception {
        validatePaymentInfo(feePayRecord);

        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        if (customerReg == null) {
            throw new Exception("登记信息为空！");
        }
        Customer customer = customerMapper.selectById(customerReg.getCustomerId());
        if (customer == null || StringUtils.isBlank(customer.getHisPid())) {
            throw new Exception("未在HIS成功建档！");
        }
        if (StringUtils.isBlank(customerReg.getHisPid())) {
            throw new Exception("未在HIS成功挂号！");
        }

        List<String> customerRegItemGroupIds = feePayRecord.getCustomerRegItemGroupIds();
        BigDecimal totalAmount = customerRegItemGroupMapper.getTotalAmount(customerRegItemGroupIds);

        String payStatus = ExConstants.PAY_STATE_支付中;
        if (totalAmount.compareTo(BigDecimal.valueOf(0)) == 0) {
            payStatus = ExConstants.PAY_STATE_支付成功;
        }
        feePayRecord.setState(payStatus);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecordMapper.insert(feePayRecord);
        //批量插入customer_reg_item_group_fee_record
        //jdbcTemplate.batchUpdate("insert into customer_reg_item_group_fee_record (customer_reg_item_group_id,fee_record_id,fee_type) values (?,?,?)", customerRegItemGroupIds.stream().map(id -> new Object[]{id, feePayRecord.getId(), "收"}).collect(Collectors.toList()));

        if (totalAmount.compareTo(BigDecimal.valueOf(0)) > 0) {
            String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
            hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
            log.info("HIS收费接口发送：" + JSONObject.toJSONString(feePayRecord));
            String finalUrl = hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH;
            String resultStr = HttpClient.textBody(finalUrl).json(JSON.toJSONString(feePayRecord)).execute().asString();

            //String resultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH, JSONObject.toJSONString(feePayRecord));
            //String resultStr = HttpClient.textBody(hipInterfaceUrl + "/datasync/interface/sendReceiptInfo").json(JSONObject.toJSONString(feePayRecord)).execute().asString();
            log.info("HIS收费接口返回：" + resultStr);
            JSONObject result = JSONObject.parseObject(resultStr);

            if (result != null && result.getBoolean("success")) {
                JSONObject data = result.getJSONObject("data");
                String applyNo = data.getString("applyNo");
                if (StringUtils.isBlank(applyNo)) {
                    throw new Exception(StringUtils.isNotBlank(result.getString("message")) ? result.getString("message") : "HIS接口未返回收费号！");
                }
                jdbcTemplate.update("update  fee_pay_record set his_apply_no=? where id=?", applyNo, feePayRecord.getId());

                UpdateWrapper<CustomerRegItemGroup> updateWrapper = new UpdateWrapper<>();
                updateWrapper.in("id", customerRegItemGroupIds);
                updateWrapper.set("fee_record_id", feePayRecord.getId());
                updateWrapper.set("pay_status", ExConstants.PAY_STATE_支付中);
                customerRegItemGroupMapper.update(null, updateWrapper);
            } else {
                log.error("HIS收费接口失败，FeePayRecord：" + JSONObject.toJSONString(feePayRecord));
                throw new Exception("HIS收费接口调用端失败");
            }
        } else {
            UpdateWrapper<CustomerRegItemGroup> updateWrapper = new UpdateWrapper<>();
            updateWrapper.in("id", customerRegItemGroupIds);
            updateWrapper.set("fee_record_id", feePayRecord.getId());
            updateWrapper.set("pay_status", ExConstants.PAY_STATE_已支付);
            customerRegItemGroupMapper.update(null, updateWrapper);
        }
    }

    @Transactional
    @Override
    public void payByHis4Combine(FeePayRecord feePayRecord) throws Exception {
        validatePaymentInfo(feePayRecord);

        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        if (customerReg == null) {
            throw new Exception("登记信息为空！");
        }
        Customer customer = customerMapper.selectById(customerReg.getCustomerId());
        if (customer == null || StringUtils.isBlank(customer.getHisPid())) {
            throw new Exception("未在HIS成功建档！");
        }
        if (StringUtils.isBlank(customerReg.getHisPid())) {
            throw new Exception("未在HIS成功挂号！");
        }
        boolean payFlag = feePayRecord.getAmount().compareTo(BigDecimal.ZERO) == 0;

        feePayRecord.setState(payFlag?ExConstants.PAY_STATE_支付成功 : ExConstants.PAY_STATE_支付中);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecordMapper.insert(feePayRecord);
        if (!payFlag) {
            String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
            hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
            log.info("HIS收费接口发送：" + JSONObject.toJSONString(feePayRecord));

            String finalUrl = hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH;
            String resultStr = HttpClient.textBody(finalUrl).json(JSONObject.toJSONString(feePayRecord)).execute().asString();
            //String resultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH, JSONObject.toJSONString(feePayRecord));
            log.info("HIS收费接口返回：" + resultStr);
            JSONObject result = JSONObject.parseObject(resultStr);

            if (result != null && result.getBoolean("success")) {
                JSONObject data = result.getJSONObject("data");
                String applyNo = data.getString("applyNo");
                if (StringUtils.isBlank(applyNo)) {
                    throw new Exception(StringUtils.isNotBlank(result.getString("message")) ? result.getString("message") : "HIS接口未返回收费号！");
                }
                jdbcTemplate.update("update  fee_pay_record set his_apply_no=? where id=?", applyNo, feePayRecord.getId());

          /*  String applyDetailNo = data.getString("applyDetailNo");
            if (StringUtils.isBlank(applyDetailNo)) {
                jdbcTemplate.update("update  fee_pay_record set his_apply_no=? where id=?", applyNo, feePayRecord.getId());

            }else{
                jdbcTemplate.update("update  fee_pay_record set his_apply_no=?, his_detail_no=? where id=?", applyNo,applyDetailNo, feePayRecord.getId());
            }*/
            } else {
                log.error("HIS收费接口失败，FeePayRecord：" + JSONObject.toJSONString(feePayRecord));
                throw new Exception("HIS收费接口调用端失败");
            }
        }
    }

    @Transactional
    @Override
    public void payCombined(FeePayRecord feePayRecord) throws Exception {
        validatePaymentInfo(feePayRecord);

        List<FeePayRecord> subPayRecordList = feePayRecord.getSubPayRecords();
        if (subPayRecordList == null || subPayRecordList.isEmpty()) {
            throw new Exception("合并收费数据为空！");
        }
        if (subPayRecordList.size() == 1) {
            throw new Exception("请使用常规收费方式！");
        }


        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getBizId());

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        CustomerRegBill customerRegBill = new CustomerRegBill();
        customerRegBill.setCustomerId(customerReg.getCustomerId());
        customerRegBill.setCustomerRegId(customerReg.getId());
        customerRegBill.setCreator(loginUser.getRealname());
        customerRegBill.setIp(feePayRecord.getClientIp());
        customerRegBill.setExamNo(customerReg.getExamNo());
        customerRegBill.setStatus(ExConstants.PAY_STATE_支付中);

        //先保存主收费记录
        feePayRecord.setCurrency("CNY");
        feePayRecord.setState(ExConstants.PAY_STATE_支付中);
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecord.setAmount(BigDecimal.ZERO);
        feePayRecordMapper.insert(feePayRecord);

        //保存子收费记录
        for (FeePayRecord subPayRecord : subPayRecordList) {
            subPayRecord.setPid(feePayRecord.getId());
            if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_门诊)) {
                payByHis4Combine(subPayRecord);
            } else if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_现金)) {
                payOffline(subPayRecord);
            } else if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_体检卡)) {
                payByCard(subPayRecord);
            } else if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_微信支付) || StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_支付宝支付)) {
                payOnline(subPayRecord);
            }
        }

        //如果所有子收费记录都支付成功，则更新主收费记录状态为支付成功
        List<String> customerRegItemGroupIds = feePayRecord.getCustomerRegItemGroupIds();
        List<String> subPayRecordStates = subPayRecordList.stream().map(FeePayRecord::getState).toList();
        if (subPayRecordStates.stream().allMatch(state -> StringUtils.equals(state, ExConstants.PAY_STATE_支付成功))) {
            feePayRecord.setState(ExConstants.PAY_STATE_支付成功);
            feePayRecordMapper.updateById(feePayRecord);
            //更新customerRegItemGroupIds关联的体检人项目收费状态
            LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            itemGroupLambdaUpdateWrapper.set(CustomerRegItemGroup::getFeeRecordId, feePayRecord.getId()).set(CustomerRegItemGroup::getPayStatus, ExConstants.PAY_STATE_已支付).in(CustomerRegItemGroup::getId, customerRegItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, feePayRecord.getBizId());
            customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper);
        } else {
            //设置customerRegItemGroupIds关联的支付记录ID
            LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            itemGroupLambdaUpdateWrapper.set(CustomerRegItemGroup::getFeeRecordId, feePayRecord.getId()).in(CustomerRegItemGroup::getId, customerRegItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, feePayRecord.getBizId());
            customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper);
        }
    }

    @Transactional
    @Override
    public String payOnline(FeePayRecord feePayRecord) throws Exception {
        validatePaymentInfo(feePayRecord);

        if (StringUtils.isBlank(feePayRecord.getAuthCode())) {
            throw new Exception("支付授权码为空！");
        }

        PayConfig payConfig = getPayConfigFromSysSetting();
        if (payConfig == null) {
            throw new Exception("支付配置信息未配置");
        }

        CustomerReg customerReg = customerRegMapper.selectById(feePayRecord.getCustomerRegId());
        List<String> customerRegItemGroupIds = feePayRecord.getCustomerRegItemGroupIds();

        feePayRecord.setState(ExConstants.PAY_STATE_订单生成);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        feePayRecord.setCreatedTime(new Date());
        feePayRecord.setCreator(loginUser.getRealname());
        feePayRecord.setCreateBy(loginUser.getUsername());
        feePayRecord.setRefundAmount(BigDecimal.valueOf(0));
        feePayRecord.setExamNo(customerReg.getExamNo());
        feePayRecord.setArchivesNum(customerReg.getArchivesNum());
        feePayRecord.setBizType(ExConstants.PAY_BIZ_TYPE_体检费);
        feePayRecordMapper.insert(feePayRecord);

        //获取请求参数
        String appId = payConfig.getAppId();
        String mchNo = payConfig.getMchNo();
        Long amount = feePayRecord.getAmount().longValue() * 100;
        String wayCode = feePayRecord.getWayCode();
        String authCode = feePayRecord.getAuthCode();

        Byte divisionMode = 0;
        String orderTitle = "体检费";

        // 前端明确了支付参数的类型 payDataType
        String payDataType = feePayRecord.getWayCode();

        PayOrderCreateRequest request = new PayOrderCreateRequest();
        PayOrderCreateReqModel model = new PayOrderCreateReqModel();
        request.setBizModel(model);

        model.setMchNo(mchNo); // 商户号
        model.setAppId(appId);
        model.setMchOrderNo(feePayRecord.getId());
        model.setWayCode(wayCode);
        model.setAmount(amount);
        model.setCurrency("CNY");

        model.setClientIp(payConfig.getGateWayPublicIp());
        model.setSubject(orderTitle);
        model.setBody(orderTitle);

        String baseUrl = sysSettingService.getValueByCode("base_url");
        baseUrl = StringUtils.endsWith(baseUrl, "/") ? baseUrl : baseUrl + "/";
        model.setNotifyUrl(baseUrl + "fee/anon/paytestNotify/payOrder"); //回调地址

        //设置扩展参数
        JSONObject extParams = new JSONObject();
        if (StringUtils.isNotEmpty(payDataType)) {
            extParams.put("payDataType", payDataType.trim());
        }
        if (StringUtils.isNotEmpty(authCode)) {
            extParams.put("authCode", authCode.trim());
        }
        model.setChannelExtra(extParams.toString());

       /* JeepayClient jeepayClient = new JeepayClient(payConfig.getGateWayUrl(), payConfig.getApiKey());

        PayOrderCreateResponse response = jeepayClient.execute(request);
        if (response.getCode() != 0) {
            log.error(response.getMsg());
            throw new Exception(response.getMsg());
        } else {
            JSONObject data = response.getData();
            String msg = response.getMsg();
            if (StringUtils.equals(msg, "SUCCESS")) {

                UpdateWrapper<FeePayRecord> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", feePayRecord.getId());
                updateWrapper.set("channel_order_no", data.getString("payOrderId"));
                updateWrapper.set("state", ExConstants.PAY_STATE_支付成功);
                updateWrapper.set("success_time", new Date());
                updateWrapper.set("updated_time", new Date());
                update(updateWrapper);
                if (StringUtils.isBlank(feePayRecord.getPid()) && StringUtils.isBlank(feePayRecord.getBillId())) {
                    //更新customerRegItemGroupIds关联的体检人项目收费状态
                    LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                    itemGroupLambdaUpdateWrapper.set(CustomerRegItemGroup::getFeeRecordId, feePayRecord.getId()).set(CustomerRegItemGroup::getPayStatus, ExConstants.PAY_STATE_已支付).in(CustomerRegItemGroup::getId, customerRegItemGroupIds).eq(CustomerRegItemGroup::getCustomerRegId, feePayRecord.getBizId());
                    customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper);
                }
                return data.getString("payOrderId");
            } else {
                throw new Exception(data.getString("errMsg"));
            }
        }*/

        return null;
    }

    @Transactional
    @Override
    public void refundHis(FeePayRecord feePayRecord) throws Exception {
        List<String> groupIdList = feePayRecord.getRefundItemGroupIds();
        String customerRegId = feePayRecord.getBizId();

        if (groupIdList == null || groupIdList.isEmpty()) {
            throw new Exception("未选择要退费的项目！");
        }
        //1、获取退费项目
        List<CustomerRegItemGroup> itemGroupList = customerRegItemGroupMapper.listByIds(groupIdList);
        //校验检查状态，如果状态是未检查才可以退
        boolean allChecked = itemGroupList.stream().allMatch(item -> StringUtils.equals(item.getCheckStatus(), ExConstants.CHECK_STATUS_未检) && StringUtils.equalsAny(item.getInterfaceSyncStatus(), ExConstants.INTERFACE_SYNC_STATUS_WAIT, ExConstants.INTERFACE_SYNC_CANCELED, ExConstants.INTERFACE_SYNC_SAMPLED));
        if (!allChecked) {
            String refundFeeFialItems = itemGroupList.stream().filter(item -> !StringUtils.equals(item.getCheckStatus(), ExConstants.CHECK_STATUS_未检) || !StringUtils.equalsAny(item.getInterfaceSyncStatus(), ExConstants.INTERFACE_SYNC_STATUS_WAIT, ExConstants.INTERFACE_SYNC_CANCELED, ExConstants.INTERFACE_SYNC_SAMPLED)).map(i -> {
                return String.format("【%s】：执行科室%s", i.getItemGroupName(), StringUtils.equals(i.getCheckStatus(), ExConstants.CHECK_STATUS_已检) ? i.getCheckStatus() : i.getInterfaceSyncStatus());
            }).collect(Collectors.joining("  "));
            throw new Exception("所选项目存在已检查的项目，无法退费！ " + refundFeeFialItems);
        }
        //2、校验项目是否已经全部收费
        boolean allPayed = itemGroupList.stream().allMatch(item -> StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATE_支付成功) || StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATUS_PAYED));
        if (!allPayed) {
            throw new Exception("所选项目存在未收费的项目，无法退费！");
        }
        //计算退费金额
        BigDecimal refundAmount = itemGroupList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
        //找到FeePayRecord
        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new Exception("退费金额小于等于0，无法退费！");
        }
        if (refundAmount.compareTo(feePayRecord.getApplyRefundAmount()) > 0) {
            throw new Exception("退费金额大于支付金额，无法退费！");
        }


        String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
        //4、遍历分组，进行先退再收
        String feeRecordId = feePayRecord.getId();

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //全额退费
        FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
        feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
        feeRefundRecord.setPayRecordId(feePayRecord.getId());
        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款中);
        feeRefundRecord.setRefundAmount(feePayRecord.getAmount());
        feeRefundRecord.setCreateBy(loginUser.getUsername());
        feeRefundRecord.setCreatedTime(new Date());
        feeRefundRecord.setCreator(loginUser.getRealname());
        feeRefundRecord.setCustomerRegId(customerRegId);
        feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
        feeRefundRecord.setExamNo(feePayRecord.getExamNo());
        feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
        feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setName(feePayRecord.getName());
        feeRefundRecord.setCurrency("cny");
        feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
        feeRefundRecordMapper.insert(feeRefundRecord);

        jdbcTemplate.update("update customer_reg_item_group set refund_fee_record_id=?,lock_by_refund=? where fee_record_id=?", feeRefundRecord.getId(), "1", feePayRecord.getId());

        log.info("调用" + hipInterfaceUrl + ExApiConstants.EXAM_REFUND_PUSH_PATH + " 数据：" + JSONObject.toJSONString(feeRefundRecord));


        String finalUrl = hipInterfaceUrl + ExApiConstants.EXAM_REFUND_PUSH_PATH;
        String refundResultStr = HttpClient.textBody(finalUrl).json(JSONObject.toJSONString(feePayRecord)).execute().asString();
        //String refundResultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_REFUND_PUSH_PATH, JSONObject.toJSONString(feeRefundRecord));
        //String refundResultStr = HttpClient.textBody(hipInterfaceUrl + "/datasync/interface/sendRefundInfo").json(JSONObject.toJSONString(feeRefundRecord)).asString();
        log.info("HIS退费接口返回：" + refundResultStr);
        JSONObject refundResult = JSONObject.parseObject(refundResultStr);
        if (refundResult != null && refundResult.getBoolean("success")) {
            JSONObject refundData = refundResult.getJSONObject("data");
            String applyNo = refundData.getString("applyNo");
            if (StringUtils.isBlank(applyNo)) {
                throw new Exception(StringUtils.isNotBlank(refundResult.getString("message")) ? refundResult.getString("message") : "HIS接口未返回退费号！");
            }
            jdbcTemplate.update("update fee_refund_record set his_apply_no=? where id=?", applyNo, feeRefundRecord.getId());
            jdbcTemplate.batchUpdate("update customer_reg_item_group set pay_status=? where id=?", itemGroupList, itemGroupList.size(), (ps, argument) -> {
                ps.setString(1, ExConstants.REFUND_STATE_退款中);
                ps.setString(2, argument.getId());
            });

            List<CustomerRegItemGroup> itemGroupsOfFeePayRecord = customerRegItemGroupMapper.listByFeeRecordId(feeRecordId);

            if (feePayRecord.getAmount().subtract(feePayRecord.getApplyRefundAmount()).compareTo(BigDecimal.ZERO) > 0) {
                FeePayRecord rePayFeePayRecord = new FeePayRecord();
                rePayFeePayRecord.setPayerType(feePayRecord.getPayerType());
                rePayFeePayRecord.setClientIp(feePayRecord.getClientIp());
                rePayFeePayRecord.setExamNo(feePayRecord.getExamNo());
                rePayFeePayRecord.setArchivesNum(feePayRecord.getArchivesNum());
                rePayFeePayRecord.setState(ExConstants.PAY_STATE_支付中);
                rePayFeePayRecord.setAmount(feePayRecord.getAmount().subtract(feePayRecord.getApplyRefundAmount()));
                rePayFeePayRecord.setName(feePayRecord.getName());
                rePayFeePayRecord.setRefundAmount(BigDecimal.ZERO);
                rePayFeePayRecord.setCreateBy(loginUser.getUsername());
                rePayFeePayRecord.setCreatedTime(new Date());
                rePayFeePayRecord.setCreator(loginUser.getRealname());
                rePayFeePayRecord.setCustomerRegId(customerRegId);
                rePayFeePayRecord.setCurrency(feePayRecord.getCurrency());
                rePayFeePayRecord.setPayChannel(feePayRecord.getPayChannel());

                feePayRecordMapper.insert(rePayFeePayRecord);

                String finaFeelUrl = hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH;
                String rePayResultStr = HttpClient.textBody(finaFeelUrl).json(JSONObject.toJSONString(rePayFeePayRecord)).execute().asString();

                //String rePayResultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH, JSONObject.toJSONString(rePayFeePayRecord));
                log.info("整单退费后，重新发送收费申请接口返回：" + rePayResultStr);
                JSONObject rePayResult = JSONObject.parseObject(rePayResultStr);
                if (rePayResult == null || !rePayResult.getBoolean("success")) {
                    log.error("整单退费后，重新发送收费申请失败,rePayFeePayRecord:" + JSONObject.toJSONString(rePayFeePayRecord));
                    throw new Exception("退费异常,HIS接口调用失败！");
                }

                JSONObject rePayResultData = rePayResult.getJSONObject("data");
                String receiptApplyNo = rePayResultData.getString("applyNo");
                if (StringUtils.isBlank(applyNo)) {
                    throw new Exception(StringUtils.isNotBlank(refundResult.getString("message")) ? refundResult.getString("message") : "HIS接口未返回收费号！");
                }
                jdbcTemplate.update("update fee_pay_record set his_apply_no=? where id=?", receiptApplyNo, rePayFeePayRecord.getId());

                //从itemGroupsOfFeePayRecord中减掉needRefundList
                List<CustomerRegItemGroup> rePayCustomerRegItemGroups = itemGroupsOfFeePayRecord.stream().filter(item -> !itemGroupList.contains(item)).toList();
                jdbcTemplate.batchUpdate("update customer_reg_item_group set fee_record_id=? where id=?", rePayCustomerRegItemGroups, rePayCustomerRegItemGroups.size(), (ps, argument) -> {
                    ps.setString(1, rePayFeePayRecord.getId());
                    ps.setString(2, argument.getId());
                });
            }
        } else {
            throw new Exception("HIS退费接口调用端失败");
        }
    }

    @Transactional
    public void refundHis4Combine(FeePayRecord feePayRecord) throws Exception {
        if (feePayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("退款金额小于0，无法退款！");
        }
        //在全额退后不用再收的情况下，将refundDone标记为true
        boolean refundDone = false;
        //如果退款金额等于0，直接生成退款记录，并标记退款成功
        String customerRegId = feePayRecord.getBizId();
        customerRegId = StringUtils.isBlank(customerRegId) ? feePayRecord.getCustomerRegId() : customerRegId;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //如果退费金额是0，则只记录退费金额并直接标记为退款成功
        if (feePayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) == 0) {
            FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
            feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
            feeRefundRecord.setBillNo(feePayRecord.getBillNo());
            feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
            feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
            feeRefundRecord.setPayRecordId(feePayRecord.getId());
            feeRefundRecord.setState(ExConstants.REFUND_STATE_退款成功);
            feeRefundRecord.setRefundAmount(feePayRecord.getApplyRefundAmount());
            feeRefundRecord.setCreateBy(loginUser.getUsername());
            feeRefundRecord.setCreatedTime(new Date());
            feeRefundRecord.setSuccessTime(new Date());
            feeRefundRecord.setCreator(loginUser.getRealname());
            feeRefundRecord.setCustomerRegId(customerRegId);
            feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
            feeRefundRecord.setExamNo(feePayRecord.getExamNo());
            feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
            feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
            feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
            feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
            feeRefundRecord.setName(feePayRecord.getName());
            feeRefundRecord.setCurrency("cny");
            feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
            feeRefundRecord.setClientIp(feePayRecord.getClientIp());
            feeRefundRecordMapper.insert(feeRefundRecord);
            feePayRecord.setRefundDone(true);
            return;
        }

        //全额退费
        FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
        feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
        feeRefundRecord.setBillNo(feePayRecord.getBillNo());
        feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
        feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
        feeRefundRecord.setPayRecordId(feePayRecord.getId());
        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款中);
        feeRefundRecord.setRefundAmount(feePayRecord.getAmount());
        feeRefundRecord.setCreateBy(loginUser.getUsername());
        feeRefundRecord.setCreatedTime(new Date());
        feeRefundRecord.setCreator(loginUser.getRealname());
        feeRefundRecord.setCustomerRegId(customerRegId);
        feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
        feeRefundRecord.setExamNo(feePayRecord.getExamNo());
        feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
        feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setName(feePayRecord.getName());
        feeRefundRecord.setCurrency("cny");
        feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setHisDetailNo(feePayRecord.getHisDetailNo());
        feeRefundRecord.setClientIp(feePayRecord.getClientIp());
        feeRefundRecord.setRefundApplyNo(String.valueOf(sequencesService.getNextSequence("refundApplyNo")));
        feeRefundRecordMapper.insert(feeRefundRecord);


        String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
        String finalUrl = hipInterfaceUrl + ExApiConstants.EXAM_REFUND_PUSH_PATH;
        String refundResultStr = HttpClient.textBody(finalUrl).json(JSONObject.toJSONString(feeRefundRecord)).execute().asString();
        log.info("调用" + finalUrl + " 数据：" + JSONObject.toJSONString(feeRefundRecord));
        //String refundResultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_REFUND_PUSH_PATH, JSONObject.toJSONString(feeRefundRecord));
        //String refundResultStr = HttpClient.textBody(hipInterfaceUrl + "/datasync/interface/sendRefundInfo").json(JSONObject.toJSONString(feeRefundRecord)).asString();
        log.info("HIS退费接口返回：" + refundResultStr);
        JSONObject refundResult = JSONObject.parseObject(refundResultStr);
        if (refundResult != null && refundResult.getBoolean("success")) {
            JSONObject refundData = refundResult.getJSONObject("data");
            String applyNo = refundData.getString("applyNo");
            if (StringUtils.isBlank(applyNo)) {
                throw new Exception(StringUtils.isNotBlank(refundResult.getString("message")) ? refundResult.getString("message") : "HIS接口未返回退费号！");
            }
            jdbcTemplate.update("update fee_refund_record set his_apply_no=? where id=?", applyNo, feeRefundRecord.getId());


            if (feePayRecord.getAmount().subtract(feePayRecord.getApplyRefundAmount()).compareTo(BigDecimal.ZERO) > 0) {
                FeePayRecord rePayFeePayRecord = new FeePayRecord();
                rePayFeePayRecord.setPayerType(feePayRecord.getPayerType());
                rePayFeePayRecord.setClientIp(feePayRecord.getClientIp());
                rePayFeePayRecord.setExamNo(feePayRecord.getExamNo());
                rePayFeePayRecord.setArchivesNum(feePayRecord.getArchivesNum());
                rePayFeePayRecord.setState(ExConstants.PAY_STATE_支付中);
                rePayFeePayRecord.setAmount(feePayRecord.getAmount().subtract(feePayRecord.getApplyRefundAmount()));
                rePayFeePayRecord.setName(feePayRecord.getName());
                rePayFeePayRecord.setRefundAmount(BigDecimal.ZERO);
                rePayFeePayRecord.setCreateBy(loginUser.getUsername());
                rePayFeePayRecord.setCreatedTime(new Date());
                rePayFeePayRecord.setCreator(loginUser.getRealname());
                rePayFeePayRecord.setCustomerRegId(customerRegId);
                rePayFeePayRecord.setCurrency(feePayRecord.getCurrency());
                rePayFeePayRecord.setPayChannel(feePayRecord.getPayChannel());
                rePayFeePayRecord.setBillId(feePayRecord.getBillId());
                rePayFeePayRecord.setHisRefundRecordId(feeRefundRecord.getId());
                rePayFeePayRecord.setBizId(feePayRecord.getBizId());
                rePayFeePayRecord.setBillNo(feePayRecord.getBillNo());
                rePayFeePayRecord.setBizType(feePayRecord.getBizType());
                rePayFeePayRecord.setRefundCustomerRegItemGroupIds(feePayRecord.getRefundCustomerRegItemGroupIds());


                feePayRecordMapper.insert(rePayFeePayRecord);

                String finaRefeeUrl = hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH;
                String rePayResultStr = HttpClient.textBody(finaRefeeUrl).json(JSONObject.toJSONString(rePayFeePayRecord)).execute().asString();

                //String rePayResultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH, JSONObject.toJSONString(rePayFeePayRecord));
                log.info("整单退费后，重新发送收费申请接口返回：" + rePayResultStr);
                JSONObject rePayResult = JSONObject.parseObject(rePayResultStr);
                if (rePayResult == null || !rePayResult.getBoolean("success")) {
                    log.error("整单退费后，重新发送收费申请失败,rePayFeePayRecord:" + JSONObject.toJSONString(rePayFeePayRecord));
                    throw new Exception("退费异常,HIS接口调用失败！");
                }

                JSONObject rePayResultData = rePayResult.getJSONObject("data");
                String receiptApplyNo = rePayResultData.getString("applyNo");
                if (StringUtils.isBlank(applyNo)) {
                    throw new Exception(StringUtils.isNotBlank(refundResult.getString("message")) ? refundResult.getString("message") : "HIS接口未返回收费号！");
                }
                jdbcTemplate.update("update fee_pay_record set his_apply_no=? where id=?", receiptApplyNo, rePayFeePayRecord.getId());
            } /*else {
                refundDone = true;
            }*/
        } else {
            throw new Exception("HIS退费接口调用失败");
        }
        //更新支付记录状态
        //jdbcTemplate.update("update fee_pay_record set refund_state=? where id=?", ExConstants.REFUND_STATE_退款中,  feeRefundRecord.getPayRecordId());
        feePayRecord.setRefundDone(refundDone);
    }

    @Transactional
    @Override
    public void refundBatch(List<FeePayRecord> recordList) throws Exception {
        for (FeePayRecord feePayRecord : recordList) {
            if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_门诊)) {
                refundHis(feePayRecord);
            } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_现金)) {
                refundOffline(feePayRecord);
            } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_凭证支付)) {
                refundOffline(feePayRecord);
            } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_体检卡)) {
                refundCard(feePayRecord);
            } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_微信支付) || StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_支付宝支付)) {
                refundOnline(feePayRecord);
            } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_组合支付)) {
                refundCombined(feePayRecord);
            } else if (StringUtils.equals(feePayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_赠送)) {
                refund4GiveAway(feePayRecord);
            }
        }
    }

    @Transactional
    @Override
    public void refund4GiveAway(FeePayRecord feePayRecord) throws Exception {
        // 1. Insert a FeeRefundRecord for Audit Trail
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
        feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
        feeRefundRecord.setBillNo(feePayRecord.getBillNo());
        feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
        feeRefundRecord.setWayCode(ExConstants.PAY_CHANNEL_赠送); // Assuming there's a constant for Giveaway Refund
        feeRefundRecord.setPayRecordId(feePayRecord.getId());
        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款成功);
//        feeRefundRecord.setRefundAmount(BigDecimal.ZERO); // Since it's a giveaway
        feeRefundRecord.setRefundAmount(feePayRecord.getApplyRefundAmount()); // Since it's a giveaway
        feeRefundRecord.setCreateBy(loginUser.getUsername());
        feeRefundRecord.setCreatedTime(new Date());
        feeRefundRecord.setSuccessTime(new Date());
        feeRefundRecord.setCreator(loginUser.getRealname());
        feeRefundRecord.setCustomerRegId(feePayRecord.getBizId());
        feeRefundRecord.setCurrency("CNY");
        feeRefundRecord.setExamNo(feePayRecord.getExamNo());
        feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
        feeRefundRecord.setChannelOrderNo(feePayRecord.getId()); // Linking to original FeePayRecord
        feeRefundRecord.setName(feePayRecord.getName());
        feeRefundRecord.setHisApplyNo(null); // Not applicable for giveaway
        feeRefundRecord.setClientIp(feePayRecord.getClientIp());
        feeRefundRecordMapper.insert(feeRefundRecord);

        // 5. Mark the FeePayRecord as Refunded
//        feePayRecord.setRefundAmount(BigDecimal.ZERO);
        BigDecimal applyRefundAmount = Objects.nonNull(feePayRecord.getApplyRefundAmount()) ? feePayRecord.getApplyRefundAmount() : BigDecimal.ZERO;
        BigDecimal refundAmount = Objects.nonNull(feePayRecord.getRefundAmount()) ? feePayRecord.getRefundAmount() : BigDecimal.ZERO;
        feePayRecord.setRefundAmount(applyRefundAmount.add(refundAmount));
        feePayRecord.setRefundDone(true);
        feePayRecordMapper.updateById(feePayRecord);

        // 自动冲红电子票据（赠送退款）
        try {
            electronicBillAutoWriteoffService.autoWriteoffAfterRefund(
                feePayRecord.getBillId(),
                "赠送退款",
                loginUser.getRealname()
            );
        } catch (Exception e) {
            log.warn("赠送退款后自动冲红电子票据失败：{}", e.getMessage());
        }
    }

    @Transactional
    @Override
    public void refundCombined(FeePayRecord parentRecord) throws Exception {
        if (parentRecord == null) {
            throw new Exception("主收费记录为空！");
        }
        List<FeePayRecord> subPayRecordList = parentRecord.getSubPayRecords();
        if (subPayRecordList == null || subPayRecordList.isEmpty()) {
            throw new Exception("合并收费数据为空！");
        }

        List<FeePayRecord> refundableList = subPayRecordList.stream().filter(item -> item.getApplyRefundAmount() != null && item.getApplyRefundAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());

        //校验子收费记录的总金额是否等于主收费记录的refundAmount
        BigDecimal totalAmount = refundableList.stream().map(FeePayRecord::getApplyRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalAmount.compareTo(parentRecord.getApplyRefundAmount()) != 0) {
            throw new Exception("退款金额与待退金额不一致！");
        }
        //saveOrUpdate(parentRecord);
        //获取退费项目
        List<String> groupIdList = parentRecord.getRefundItemGroupIds();
        List<CustomerRegItemGroup> itemGroupList = customerRegItemGroupMapper.listByIds(groupIdList);
        //校验检查状态，如果状态是未检查才可以退
        boolean allChecked = itemGroupList.stream().allMatch(item -> StringUtils.equals(item.getCheckStatus(), ExConstants.CHECK_STATUS_未检) && StringUtils.equalsAny(item.getInterfaceSyncStatus(), ExConstants.INTERFACE_SYNC_STATUS_WAIT, ExConstants.INTERFACE_SYNC_CANCELED, ExConstants.INTERFACE_SYNC_SAMPLED));
        if (!allChecked) {
            String refundFeeFialItems = itemGroupList.stream().filter(item -> !StringUtils.equals(item.getCheckStatus(), ExConstants.CHECK_STATUS_未检) || !StringUtils.equalsAny(item.getInterfaceSyncStatus(), ExConstants.INTERFACE_SYNC_STATUS_WAIT, ExConstants.INTERFACE_SYNC_CANCELED, ExConstants.INTERFACE_SYNC_SAMPLED)).map(i -> {
                return String.format("【%s】：执行科室%s", i.getItemGroupName(), StringUtils.equals(i.getCheckStatus(), ExConstants.CHECK_STATUS_已检) ? i.getCheckStatus() : i.getInterfaceSyncStatus());
            }).collect(Collectors.joining("  "));
            throw new Exception("所选项目存在已检查的项目，无法退费！ " + refundFeeFialItems);
        }
        //校验项目是否已经全部收费
        boolean allPayed = itemGroupList.stream().allMatch(item -> StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATE_支付成功) || StringUtils.equals(item.getPayStatus(), ExConstants.PAY_STATUS_PAYED));
        if (!allPayed) {
            throw new Exception("所选项目存在未收费的项目，无法退费！");
        }

        LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        itemGroupLambdaUpdateWrapper.set(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款中).set(CustomerRegItemGroup::getLockByRefund, "1").in(CustomerRegItemGroup::getId, groupIdList).eq(CustomerRegItemGroup::getCustomerRegId, parentRecord.getBizId());
        customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper);
        //遍历子收费记录，逐个退款
        for (FeePayRecord subPayRecord : refundableList) {
            if (subPayRecord.getApplyRefundAmount() == null || subPayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //将关联的项目组锁定
            if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_门诊)) {
                refundHis4Combine(subPayRecord);
            } else if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_现金)) {
                refundOffline(subPayRecord);
            } else if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_体检卡)) {
                refundCard(subPayRecord);
            } else if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_微信支付) || StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_支付宝支付)) {
                refundOnline(subPayRecord);
            } else if (StringUtils.equals(subPayRecord.getPayChannel(), ExConstants.PAY_CHANNEL_赠送)) {
                refund4GiveAway(subPayRecord);
            }
        }

        //如果所有的refundableList都退款成功（判断refundDone属性），则更新主收费记录状态为退款成功
        if (refundableList.stream().allMatch(FeePayRecord::isRefundDone)) {
            //更新customerRegItemGroupIds关联的体检人项目收费状态
            LambdaUpdateWrapper<CustomerRegItemGroup> itemGroupLambdaUpdateWrapper2 = new LambdaUpdateWrapper<>();
            itemGroupLambdaUpdateWrapper2.set(CustomerRegItemGroup::getPayStatus, ExConstants.REFUND_STATE_退款成功).set(CustomerRegItemGroup::getLockByRefund, "0").in(CustomerRegItemGroup::getId, groupIdList).eq(CustomerRegItemGroup::getCustomerRegId, parentRecord.getBizId());
            customerRegItemGroupMapper.update(null, itemGroupLambdaUpdateWrapper2);

            parentRecord.setState(ExConstants.PAY_STATE_支付成功);
            parentRecord.setSuccessTime(new Date());
            updateById(parentRecord);
        }
    }

    @Transactional
    @Override
    public void refundOffline(FeePayRecord feePayRecord) throws Exception {
        if (feePayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("退款金额小于0，无法退款！");
        }
        String customerRegId = feePayRecord.getBizId();
        //1、进行退款
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //退费
        FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
        feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
        feeRefundRecord.setBillNo(feePayRecord.getBillNo());
        feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
        feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
        feeRefundRecord.setPayRecordId(feePayRecord.getId());
        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款成功);
        feeRefundRecord.setRefundAmount(feePayRecord.getApplyRefundAmount());
        feeRefundRecord.setCreateBy(loginUser.getUsername());
        feeRefundRecord.setCreatedTime(new Date());
        feeRefundRecord.setSuccessTime(new Date());
        feeRefundRecord.setCreator(loginUser.getRealname());
        feeRefundRecord.setCustomerRegId(customerRegId);
        feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
        feeRefundRecord.setExamNo(feePayRecord.getExamNo());
        feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
        feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setName(feePayRecord.getName());
        feeRefundRecord.setCurrency("cny");
        feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setClientIp(feePayRecord.getClientIp());
        feeRefundRecordMapper.insert(feeRefundRecord);

        jdbcTemplate.update("update fee_pay_record set refund_amount=ifnull(refund_amount,0)+?,refund_times = ifnull(refund_times,0)+1,refund_state=? where id=?", feePayRecord.getApplyRefundAmount(), ExConstants.REFUND_STATE_退款成功, feePayRecord.getId());

        feePayRecord.setRefundDone(true);
    }

    @Transactional
    @Override
    public void refundOnline(FeePayRecord feePayRecord) throws Exception {
        if (feePayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("退款金额小于0，无法退款！");
        }
        //调用支付网关的退费接口
        PayConfig payConfig = getPayConfigFromSysSetting();
        if (payConfig == null) {
            throw new Exception("支付配置信息未配置");
        }

        String customerRegId = feePayRecord.getBizId();
        //1、进行退款
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
        feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
        feeRefundRecord.setBillNo(feePayRecord.getBillNo());
        feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
        feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
        feeRefundRecord.setPayRecordId(feePayRecord.getId());
        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款中);
        feeRefundRecord.setRefundAmount(feePayRecord.getApplyRefundAmount());
        feeRefundRecord.setCreateBy(loginUser.getUsername());
        feeRefundRecord.setCreatedTime(new Date());
        feeRefundRecord.setCreator(loginUser.getRealname());
        feeRefundRecord.setCustomerRegId(customerRegId);
        feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
        feeRefundRecord.setExamNo(feePayRecord.getExamNo());
        feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
        feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setName(feePayRecord.getName());
        feeRefundRecord.setCurrency("cny");
        feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setClientIp(feePayRecord.getClientIp());
        feeRefundRecordMapper.insert(feeRefundRecord);
        /*if(StringUtils.isBlank(feePayRecord.getPid())) {
            jdbcTemplate.update("update customer_reg_item_group set refund_fee_record_id=?,pay_status=?,lock_by_refund='1' where fee_record_id=?", feeRefundRecord.getId(), ExConstants.REFUND_STATE_退款中, feePayRecord.getId());
        }*/
        //获取请求参数
        String appId = payConfig.getAppId();
        String mchNo = payConfig.getMchNo();
        Long amount = feePayRecord.getApplyRefundAmount().longValue() * 100;
        String wayCode = feePayRecord.getWayCode();
        String authCode = feePayRecord.getAuthCode();

        Byte divisionMode = 0;
        String orderTitle = "体检费";

        // 前端明确了支付参数的类型 payDataType
        String payDataType = feePayRecord.getWayCode();

        PayOrderCreateRequest request = new PayOrderCreateRequest();
        PayOrderCreateReqModel model = new PayOrderCreateReqModel();
        request.setBizModel(model);

        model.setMchNo(mchNo); // 商户号
        model.setAppId(appId);
        model.setMchOrderNo(feePayRecord.getId());
        model.setWayCode(wayCode);
        model.setAmount(amount);
        model.setCurrency("CNY");

        model.setClientIp(payConfig.getGateWayPublicIp());
        model.setSubject(orderTitle);
        model.setBody(orderTitle);

        String baseUrl = sysSettingService.getValueByCode("base_url");
        baseUrl = StringUtils.endsWith(baseUrl, "/") ? baseUrl : baseUrl + "/";
        model.setNotifyUrl(baseUrl + "fee/anon/paytestNotify/payOrder"); //回调地址
        //model.setDivisionMode(divisionMode); //分账模式

        //设置扩展参数
       /* JSONObject extParams = new JSONObject();
        if (StringUtils.isNotEmpty(payDataType)) {
            extParams.put("payDataType", payDataType.trim());
        }
        if (StringUtils.isNotEmpty(authCode)) {
            extParams.put("authCode", authCode.trim());
        }
        model.setChannelExtra(extParams.toString());

        JeepayClient jeepayClient = new JeepayClient(payConfig.getGateWayUrl(), payConfig.getApiKey());

        PayOrderCreateResponse response = jeepayClient.execute(request);
        if (response.getCode() != 0) {
            log.error(response.getMsg());
            throw new Exception(response.getMsg());
        } else {
            JSONObject data = response.getData();
            String msg = response.getMsg();
            if (StringUtils.equals(msg, "SUCCESS")) {

                jdbcTemplate.update("update fee_pay_record set refund_amount=ifnull(refund_amount,0)+?,refund_times = ifnull(refund_times,0)+1,refund_state=? where id=?", feePayRecord.getApplyRefundAmount(), ExConstants.REFUND_STATE_退款成功, feePayRecord.getId());
                //更新customerRegItemGroupIds关联的体检人项目收费状态
            } else {
                throw new Exception(data.getString("errMsg"));
            }
        }

        // 自动冲红电子票据（线上退款）
        try {
            electronicBillAutoWriteoffService.autoWriteoffAfterRefund(
                feePayRecord.getBillId(),
                "线上退款",
                loginUser.getRealname()
            );
        } catch (Exception e) {
            log.warn("线上退款后自动冲红电子票据失败：{}", e.getMessage());
        }

        feePayRecord.setRefundDone(true);*/
    }


    @Override
    public BigDecimal getRemainAmount4Reg(String regId, String payerType) {

        BigDecimal totalPrice = feePayRecordMapper.getTotalPriceOfReg(regId, payerType);
        totalPrice = totalPrice != null ? totalPrice : BigDecimal.valueOf(0);

        BigDecimal payedAmount = feePayRecordMapper.getPayedAmountOfReg(regId, payerType);
        //payedAmount单位是分，需要转为元
        payedAmount = payedAmount != null ? payedAmount : BigDecimal.valueOf(0);

        return totalPrice.subtract(payedAmount);
    }

    @Override
    public PayConfig getPayConfigFromSysSetting() {
        String payGatewayIp = sysSettingService.getValueByCode("pay_gateway_ip");
        String payGatewayPublicIp = sysSettingService.getValueByCode("pay_gateway_public_ip");
        String mchNo = sysSettingService.getValueByCode("mch_no");
        String payAppId = sysSettingService.getValueByCode("pay_appId");
        String payApiKey = sysSettingService.getValueByCode("pay_api_key");

        if (StringUtils.isNotBlank(mchNo) && StringUtils.isNotBlank(payAppId) && StringUtils.isNotBlank(payGatewayIp) && StringUtils.isNotBlank(payApiKey)) {
            PayConfig payConfig = new PayConfig();
            payConfig.setMchNo(mchNo);
            payConfig.setAppId(payAppId);
            payConfig.setGateWayUrl(payGatewayIp);
            payConfig.setGateWayPublicIp(StringUtils.isNotBlank(payGatewayPublicIp) ? payGatewayPublicIp : payGatewayIp);
            payConfig.setApiKey(payApiKey);
            return payConfig;
        }

        return null;
    }

    @Override
    public List<TeamFeeStat> listTeamPayStat(String companyRegId, String accountCloseStatus) {

        List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listByCompanyRegId(companyRegId);

        Map<String, List<CustomerRegItemGroup>> groupedByTeamId = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getTeamId));

        return groupedByTeamId.entrySet().stream().map(entry -> {
            String teamId = entry.getKey();
            List<CustomerRegItemGroup> groups = entry.getValue();

            TeamFeeStat stat = new TeamFeeStat();
            stat.setTeamId(teamId);
            stat.setTeamName(groups.get(0).getTeamName());
            //统计总金额
            List<CustomerRegItemGroup> normalList = groups.stream().filter(group -> group.getAddMinusFlag() != -1).toList();
            stat.setTotalAmount(normalList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add));
            //统计人数，按照customerRegId进行分组，然后取size
            stat.setTotalCount(groups.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getCustomerRegId)).size());

            return stat;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ItemGroupFeeStat> listGroupItemPayStat(String companyRegId) {
        List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listByCompanyRegId(companyRegId);

        Map<String, List<CustomerRegItemGroup>> groupedByItemGroupId = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getItemGroupId));

        return groupedByItemGroupId.entrySet().stream().map(entry -> {
            String itemGroupId = entry.getKey();
            List<CustomerRegItemGroup> groups = entry.getValue();

            ItemGroupFeeStat stat = new ItemGroupFeeStat();
            stat.setItemGroupId(itemGroupId);
            // Assuming there's a method getItemGroupName in CustomerRegItemGroup or another way to set it
            stat.setItemGroupName(groups.get(0).getItemGroupName());
            stat.setTotalAmount(groups.stream().filter(group -> group.getAddMinusFlag() != -1).map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add));
            stat.setTotalCountOfReg(groups.stream().filter(group -> StringUtils.equals(group.getCheckStatus(), ExConstants.CHECK_STATUS_未检)).map(CustomerRegItemGroup::getCustomerRegId).distinct().count());
            return stat;
        }).collect(Collectors.toList());
    }

    @Override
    public BigDecimal getPayedAmountOfCompanyReg(String companyRegId) {
        return feePayRecordMapper.getPayedAmountOfCompanyReg(companyRegId);
    }

    @Override
    public CompanyFeeStat getCompanyFeeStat(String companyRegId) {

        CompanyReg companyReg = companyRegMapper.selectById(companyRegId);

        Long totalCount = 0L;
        Long totalRegCount = 0L;
        try {
            totalCount = jdbcTemplate.queryForObject("select count(*) from customer_reg where company_reg_id=? ", Long.class, companyRegId);
        } catch (Exception ignored) {
        }
        totalCount = totalCount != null ? totalCount : 0L;
        try {
            totalRegCount = jdbcTemplate.queryForObject("select count(*) from customer_reg where company_reg_id=? and status=?", Long.class, companyRegId, ExConstants.REG_STATUS_WAIT);
        } catch (Exception ignored) {
        }
        totalRegCount = totalRegCount != null ? totalRegCount : 0L;

        List<CustomerRegItemGroup> groupList = customerRegItemGroupMapper.listByCompanyRegId(companyRegId);
        Map<String, List<CustomerRegItemGroup>> groupedByItemGroupId = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getItemGroupId));

        List<ItemGroupFeeStat> itemGroupFeeStats = groupedByItemGroupId.entrySet().stream().map(entry -> {
            String itemGroupId = entry.getKey();
            List<CustomerRegItemGroup> groups = entry.getValue();

            ItemGroupFeeStat stat = new ItemGroupFeeStat();
            stat.setItemGroupId(itemGroupId);
            // Assuming there's a method getItemGroupName in CustomerRegItemGroup or another way to set it
            stat.setItemGroupName(groups.get(0).getItemGroupName());
            stat.setTotalAmount(groups.stream().filter(group -> group.getAddMinusFlag() != -1).map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add));
            stat.setTotalCountOfReg(groups.stream().filter(group -> StringUtils.equals(group.getCheckStatus(), ExConstants.CHECK_STATUS_未检) && group.getAddMinusFlag() != -1).map(CustomerRegItemGroup::getCustomerRegId).distinct().count());
            return stat;
        }).toList();

        Map<String, List<CustomerRegItemGroup>> groupedByTeamId = groupList.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getTeamId));

        List<TeamFeeStat> teamFeeStats = groupedByTeamId.entrySet().stream().map(entry -> {
            String teamId = entry.getKey();
            List<CustomerRegItemGroup> groups = entry.getValue();

            TeamFeeStat stat = new TeamFeeStat();
            stat.setTeamId(teamId);
            stat.setTeamName(groups.get(0).getTeamName());
            //统计总金额
            List<CustomerRegItemGroup> normalList = groups.stream().filter(group -> group.getAddMinusFlag() != -1).toList();
            stat.setTotalAmount(normalList.stream().map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add));
            //统计人数，按照customerRegId进行分组，然后取size
            stat.setTotalCount(groups.stream().collect(Collectors.groupingBy(CustomerRegItemGroup::getCustomerRegId)).size());

            return stat;
        }).toList();

        BigDecimal totalAmountOfReg = groupList.stream().filter(group -> group.getAddMinusFlag() != -1).map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAmount = groupList.stream().filter(group -> StringUtils.equals(group.getCheckStatus(), ExConstants.CHECK_STATUS_未检) && group.getAddMinusFlag() != -1).map(CustomerRegItemGroup::getPriceAfterDis).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalPayedAmount = getPayedAmountOfCompanyReg(companyRegId);

        //处理为null的情况
        totalPayedAmount = totalPayedAmount != null ? totalPayedAmount : BigDecimal.ZERO;


        BigDecimal totalRemainAmount = totalAmount.subtract(totalPayedAmount);

        CompanyFeeStat companyFeeStat = new CompanyFeeStat();

        companyFeeStat.setItemGroupFeeStats(itemGroupFeeStats);
        companyFeeStat.setCompanyName(companyReg.getCompanyName());
        companyFeeStat.setCompanyId(companyReg.getCompanyId());
        companyFeeStat.setTotalCount(totalCount);
        companyFeeStat.setTotalRegCount(totalRegCount);
        companyFeeStat.setTeamFeeStats(teamFeeStats);
        companyFeeStat.setTotalAmount(totalAmount);
        companyFeeStat.setTotalAmountOfReg(totalAmountOfReg);
        companyFeeStat.setTotalPayedAmount(totalPayedAmount);
        //companyFeeStat.setTotalRefundAmount(BigDecimal.ZERO);
        companyFeeStat.setTotalRemainAmount(totalRemainAmount);

        return companyFeeStat;
    }


    @Override
    public void updateFeeState(ReceiptStatus receiptStatus) throws Exception {
        log.info("主程序接收到收费状态" + JSONObject.toJSONString(receiptStatus));
        String hisApplyNo = receiptStatus.getApplyNo();
        String applyStatusCode = receiptStatus.getApplyStatusCode();
        String applyNo = receiptStatus.getApplyNo();
        // if (StringUtils.equals(applyStatusCode, "0")) {
        //     jdbcTemplate.update("update fee_pay_record set state=? where his_apply_no=?", ExConstant.PAY_STATE_支付成功, applyNo);
        // } else if (StringUtils.equals(applyStatusCode, "2")) {
        //     jdbcTemplate.update("update fee_refund_record set state=? where his_apply_no=?", ExConstant.REFUND_STATE_退款成功, applyNo);
        // }
        String sql = "SELECT customer_reg_id FROM fee_pay_record WHERE his_apply_no = ?";
        String customerRegId = jdbcTemplate.queryForObject(sql, new Object[]{applyNo}, String.class);
        if (StringUtils.isBlank(customerRegId)) {
            throw new Exception("根据收费状态未取到customerRegId！");
        }
        if (StringUtils.equals(applyStatusCode, "1")) {
            //jdbcTemplate.update("update fee_pay_record set state=? where his_apply_no=?", ExConstants.PAY_STATE_支付成功, applyNo);
            //jdbcTemplate.update("update customer_reg set payment_state=? where id=?", ExConstants.PAY_STATUS_PAYED, customerRegId);
            // jdbcTemplate.update("update customer_reg_item_group set pay_status=? where id=?", ExConstants.PAY_STATUS_PAYED, customerRegId);

            CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
            //jmsMessageSender.sendPatientTopicMessage(JSON.toJSONString(customerReg));
        } else if (StringUtils.equals(applyStatusCode, "2")) {
            //jdbcTemplate.update("update customer_reg set payment_state=? where id=?", ExConstants.PAY_STATUS_WAIT, customerRegId);
            //jdbcTemplate.update("update fee_pay_record set state=?,refund_state=? where his_apply_no=?", ExConstants.REFUND_STATE_退款成功,"全额退款",applyNo);
            // jdbcTemplate.update("update fee_refund_record set state=? where his_apply_no=?", ExConstants.REFUND_STATE_退款成功, applyNo);
        } else if (StringUtils.equals(applyStatusCode, "3")) {
            // jdbcTemplate.update("update fee_pay_record set state=? where his_apply_no=?", ExConstants.PAY_STATE_订单关闭, applyNo);
        }
    }

    @Override
    public BigDecimal getPaidAmountOfBill(String recipeId) {

        return feePayRecordMapper.getPaidAmountOfBill(recipeId);
    }

    @Override
    public boolean isAllBillPaied(CustomerRegBill bill) {
        BigDecimal paidAmount = feePayRecordMapper.getPaidAmountOfBill(bill.getId());
        paidAmount = paidAmount != null ? paidAmount : BigDecimal.ZERO;
        return paidAmount.compareTo(bill.getAmount()) == 0;
    }

    @Override
    public List<FeePayRecord> getByBillId(String billId) {

        LambdaQueryWrapper<FeePayRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FeePayRecord::getBillId, billId);
        return feePayRecordMapper.selectList(queryWrapper);
    }

    @Override
    public void invalidHisPayRecord(FeePayRecord feePayRecord) throws Exception {
        String customerRegId = feePayRecord.getBizId();
        customerRegId = StringUtils.isBlank(customerRegId) ? feePayRecord.getCustomerRegId() : customerRegId;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //金额不为0发送收费作废申请
        if (feePayRecord.getAmount().compareTo(BigDecimal.ZERO) != 0) {
            CustomerReg customerReg = customerRegMapper.selectById(customerRegId);
            feePayRecord.setCustomerRegId(customerRegId);
            feePayRecord.setHisVisitNo(customerReg.getHisVisitNo());
            feePayRecord.setState(ExConstants.PAY_STATE_订单关闭);
            feePayRecord.setHisPid(customerReg.getHisPid());
            List<CustomerRegItemGroup> groups = customerRegItemGroupMapper.listByBillId(feePayRecord.getBillId());
            if (CollectionUtils.isNotEmpty(groups)) {
                feePayRecord.setCustomerRegItemGroupIds(groups.stream().map(CustomerRegItemGroup::getId).toList());
            }
            String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
            hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
            String finalUrl = hipInterfaceUrl + ExApiConstants.INVALIDATE_PAY_PUSH_PATH;
            String invalidatePayResultStr = HttpClient.textBody(finalUrl).json(JSONObject.toJSONString(feePayRecord)).execute().asString();
            log.info("调用" + finalUrl + " 数据：" + JSONObject.toJSONString(feePayRecord));
            log.info("HIS收费申请作废接口返回：" + invalidatePayResultStr);
            JSONObject refundResult = JSONObject.parseObject(invalidatePayResultStr);
            if (refundResult != null && refundResult.getBoolean("success")) {
               /* JSONObject refundData = refundResult.getJSONObject("data");
                String applyNo = refundData.getString("applyNo");
                if (StringUtils.isBlank(applyNo)) {
                    throw new Exception(StringUtils.isNotBlank(refundResult.getString("message")) ? refundResult.getString("message") : "HIS接口未返回退费号！");
                }*/

            } else {
                throw new Exception("HIS收费申请作废接口调用失败");
            }
        }
    }

    @Override
    public void invalidHisRefundRecord(FeeRefundRecord refundRecord) throws Exception {

        //如果退款金额等于0，直接生成退款记录，并标记退款成功
//        String customerRegId = refundRecord.getCustomerRegId();
//        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //如果退费金额是0，则只记录退费金额并直接标记为退款成功
        if (refundRecord.getRefundAmount().compareTo(BigDecimal.ZERO) != 0) {
//            FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
            refundRecord.setState(ExConstants.REFUND_STATE_退款任务关闭);
//            feeRefundRecord.setCreateBy(loginUser.getUsername());
//            feeRefundRecord.setCreatedTime(new Date());
//            feeRefundRecord.setCreator(loginUser.getRealname());
//            feeRefundRecord.setCustomerRegId(customerRegId);
//            feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
//            feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
//            feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
//            feeRefundRecord.setCurrency("cny");
//            feeRefundRecord.setRefundApplyNo(String.valueOf(sequencesService.getNextSequence("refundApplyNo")));

            String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
            hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
            String finalUrl = hipInterfaceUrl + ExApiConstants.INVALIDATE_REFUND_PUSH_PATH;
            String refundResultStr = HttpClient.textBody(finalUrl).json(JSONObject.toJSONString(refundRecord)).execute().asString();
            log.info("调用" + finalUrl + " 数据：" + JSONObject.toJSONString(refundRecord));
            //String refundResultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_REFUND_PUSH_PATH, JSONObject.toJSONString(feeRefundRecord));
            //String refundResultStr = HttpClient.textBody(hipInterfaceUrl + "/datasync/interface/sendRefundInfo").json(JSONObject.toJSONString(feeRefundRecord)).asString();
            log.info("HIS退费申请作废接口返回：" + refundResultStr);
            JSONObject refundResult = JSONObject.parseObject(refundResultStr);
            if (refundResult != null && refundResult.getBoolean("success")) {
               /* JSONObject refundData = refundResult.getJSONObject("data");
                String applyNo = refundData.getString("applyNo");
                if (StringUtils.isBlank(applyNo)) {
                    throw new Exception(StringUtils.isNotBlank(refundResult.getString("message")) ? refundResult.getString("message") : "HIS接口未返回退费号！");
                }
                jdbcTemplate.update("update fee_refund_record set his_apply_no=? where id=?", applyNo, feeRefundRecord.getId());
*/
            } else {
                throw new Exception("HIS退费申请作废接口调用失败");
            }
        }
    }

    @Override
    @Transactional
    public void refundHis4RelatedRecipe(FeePayRecord feePayRecord) throws Exception {
        if (feePayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new Exception("退款金额小于0，无法退款！");
        }
        //如果退款金额等于0，直接生成退款记录，并标记退款成功
        String customerRegId = feePayRecord.getBizId();
        customerRegId = StringUtils.isBlank(customerRegId) ? feePayRecord.getCustomerRegId() : customerRegId;
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //如果退费金额是0，则只记录退费金额并直接标记为退款成功
        if (feePayRecord.getApplyRefundAmount().compareTo(BigDecimal.ZERO) == 0) {
            FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
            feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
            feeRefundRecord.setBillNo(feePayRecord.getBillNo());
            feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
            feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
            feeRefundRecord.setPayRecordId(feePayRecord.getId());
            feeRefundRecord.setState(ExConstants.REFUND_STATE_退款成功);
            feeRefundRecord.setRefundAmount(feePayRecord.getApplyRefundAmount());
            feeRefundRecord.setCreateBy(loginUser.getUsername());
            feeRefundRecord.setCreatedTime(new Date());
            feeRefundRecord.setSuccessTime(new Date());
            feeRefundRecord.setCreator(loginUser.getRealname());
            feeRefundRecord.setCustomerRegId(customerRegId);
            feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
            feeRefundRecord.setExamNo(feePayRecord.getExamNo());
            feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
            feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
            feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
            feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
            feeRefundRecord.setName(feePayRecord.getName());
            feeRefundRecord.setCurrency("cny");
            feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
            feeRefundRecord.setClientIp(feePayRecord.getClientIp());
            feeRefundRecordMapper.insert(feeRefundRecord);
            feePayRecord.setRefundDone(true);
            return;
        }

        //全额退费
        FeeRefundRecord feeRefundRecord = new FeeRefundRecord();
        feeRefundRecord.setBillRefundId(feePayRecord.getBillRefundId());
        feeRefundRecord.setBillNo(feePayRecord.getBillNo());
        feeRefundRecord.setBillRefundNo(feePayRecord.getBillRefundNo());
        feeRefundRecord.setWayCode(feePayRecord.getPayChannel());
        feeRefundRecord.setPayRecordId(feePayRecord.getId());
        feeRefundRecord.setState(ExConstants.REFUND_STATE_退款中);
        feeRefundRecord.setRefundAmount(feePayRecord.getAmount());
        feeRefundRecord.setCreateBy(loginUser.getUsername());
        feeRefundRecord.setCreatedTime(new Date());
        feeRefundRecord.setCreator(loginUser.getRealname());
        feeRefundRecord.setCustomerRegId(customerRegId);
        feeRefundRecord.setCurrency(feeRefundRecord.getCurrency());
        feeRefundRecord.setExamNo(feePayRecord.getExamNo());
        feeRefundRecord.setMchRefundNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setMchNo(identifierGenerator.nextId(null).toString());
        feeRefundRecord.setArchivesNum(feePayRecord.getArchivesNum());
        feeRefundRecord.setChannelOrderNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setName(feePayRecord.getName());
        feeRefundRecord.setCurrency("cny");
        feeRefundRecord.setHisApplyNo(feePayRecord.getHisApplyNo());
        feeRefundRecord.setHisDetailNo(feePayRecord.getHisDetailNo());
        feeRefundRecord.setClientIp(feePayRecord.getClientIp());
        feeRefundRecord.setRefundApplyNo(String.valueOf(sequencesService.getNextSequence("refundApplyNo")));
        feeRefundRecordMapper.insert(feeRefundRecord);


        String hipInterfaceUrl = sysSettingService.getValueByCode("hip_interface_url");
        hipInterfaceUrl = StringUtils.removeEnd(hipInterfaceUrl, "/");
        String finalUrl = hipInterfaceUrl + ExApiConstants.EXAM_REFUND_PUSH_PATH;
        String refundResultStr = HttpClient.textBody(finalUrl).json(JSONObject.toJSONString(feeRefundRecord)).execute().asString();
        log.info("调用" + finalUrl + " 数据：" + JSONObject.toJSONString(feeRefundRecord));
        //String refundResultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_REFUND_PUSH_PATH, JSONObject.toJSONString(feeRefundRecord));
        //String refundResultStr = HttpClient.textBody(hipInterfaceUrl + "/datasync/interface/sendRefundInfo").json(JSONObject.toJSONString(feeRefundRecord)).asString();
        log.info("HIS退费接口返回：" + refundResultStr);
        JSONObject refundResult = JSONObject.parseObject(refundResultStr);
        if (refundResult != null && refundResult.getBoolean("success")) {
            JSONObject refundData = refundResult.getJSONObject("data");
            String applyNo = refundData.getString("applyNo");
            if (StringUtils.isBlank(applyNo)) {
                throw new Exception(StringUtils.isNotBlank(refundResult.getString("message")) ? refundResult.getString("message") : "HIS接口未返回退费号！");
            }
            jdbcTemplate.update("update fee_refund_record set his_apply_no=? where id=?", applyNo, feeRefundRecord.getId());

                FeePayRecord rePayFeePayRecord = new FeePayRecord();
                rePayFeePayRecord.setPayerType(feePayRecord.getPayerType());
                rePayFeePayRecord.setClientIp(feePayRecord.getClientIp());
                rePayFeePayRecord.setExamNo(feePayRecord.getExamNo());
                rePayFeePayRecord.setArchivesNum(feePayRecord.getArchivesNum());
                rePayFeePayRecord.setState(ExConstants.PAY_STATE_支付中);
                rePayFeePayRecord.setAmount(feePayRecord.getApplyRefundAmount());
                rePayFeePayRecord.setName(feePayRecord.getName());
                rePayFeePayRecord.setRefundAmount(BigDecimal.ZERO);
                rePayFeePayRecord.setCreateBy(loginUser.getUsername());
                rePayFeePayRecord.setCreatedTime(new Date());
                rePayFeePayRecord.setCreator(loginUser.getRealname());
                rePayFeePayRecord.setCustomerRegId(customerRegId);
                rePayFeePayRecord.setCurrency(feePayRecord.getCurrency());
                rePayFeePayRecord.setPayChannel(feePayRecord.getPayChannel());
                rePayFeePayRecord.setBillId(feePayRecord.getBillId());
                rePayFeePayRecord.setHisRefundRecordId(feeRefundRecord.getId());
                rePayFeePayRecord.setBizId(feePayRecord.getBizId());
                rePayFeePayRecord.setBillNo(feePayRecord.getBillNo());
                rePayFeePayRecord.setBizType(feePayRecord.getBizType());
                rePayFeePayRecord.setRemark("同一发票关联退费后整单重新收费");
                feePayRecordMapper.insert(rePayFeePayRecord);

                String finaRefeeUrl = hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH;
                String rePayResultStr = HttpClient.textBody(finaRefeeUrl).json(JSONObject.toJSONString(rePayFeePayRecord)).execute().asString();

                //String rePayResultStr = HttpClientUtil.sendPost(hipInterfaceUrl + ExApiConstants.EXAM_FEE_PUSH_PATH, JSONObject.toJSONString(rePayFeePayRecord));
                log.info("整单退费后，重新发送收费申请接口返回：" + rePayResultStr);
                JSONObject rePayResult = JSONObject.parseObject(rePayResultStr);
                if (rePayResult == null || !rePayResult.getBoolean("success")) {
                    log.error("整单退费后，重新发送收费申请失败,rePayFeePayRecord:" + JSONObject.toJSONString(rePayFeePayRecord));
                    throw new Exception("退费异常,HIS接口调用失败！");
                }

                JSONObject rePayResultData = rePayResult.getJSONObject("data");
                String receiptApplyNo = rePayResultData.getString("applyNo");
                if (StringUtils.isBlank(applyNo)) {
                    throw new Exception(StringUtils.isNotBlank(refundResult.getString("message")) ? refundResult.getString("message") : "HIS接口未返回收费号！");
                }
                jdbcTemplate.update("update fee_pay_record set his_apply_no=? where id=?", receiptApplyNo, rePayFeePayRecord.getId());
        } else {
            throw new Exception("HIS退费接口调用失败");
        }
    }
}
