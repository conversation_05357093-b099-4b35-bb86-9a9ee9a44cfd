package org.jeecg.modules.fee.gateway.dto;

import lombok.Data;

@Data
public class CancelRequest {
    
    private String orderNo;
    
    private String channelOrderNo;
    
    private String reason;
    
    private PaymentRequest.OperatorInfo operatorInfo;
    
    public CancelRequest() {}
    
    public CancelRequest(String orderNo) {
        this.orderNo = orderNo;
    }
    
    public CancelRequest(String orderNo, String reason) {
        this.orderNo = orderNo;
        this.reason = reason;
    }
}