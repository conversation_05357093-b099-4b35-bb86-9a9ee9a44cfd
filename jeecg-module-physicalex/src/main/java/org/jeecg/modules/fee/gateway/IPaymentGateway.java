package org.jeecg.modules.fee.gateway;

import org.jeecg.modules.fee.gateway.dto.PaymentRequest;
import org.jeecg.modules.fee.gateway.dto.PaymentResponse;
import org.jeecg.modules.fee.gateway.dto.QueryRequest;
import org.jeecg.modules.fee.gateway.dto.QueryResponse;
import org.jeecg.modules.fee.gateway.dto.RefundRequest;
import org.jeecg.modules.fee.gateway.dto.RefundResponse;
import org.jeecg.modules.fee.gateway.dto.CancelRequest;
import org.jeecg.modules.fee.gateway.dto.CancelResponse;

public interface IPaymentGateway {
    
    String getGatewayName();
    
    PaymentResponse createPayment(PaymentRequest request);
    
    PaymentResponse createQrCodePayment(PaymentRequest request);
    
    PaymentResponse createBarcodePayment(PaymentRequest request);
    
    QueryResponse queryPayment(QueryRequest request);
    
    RefundResponse refundPayment(RefundRequest request);
    
    CancelResponse cancelPayment(CancelRequest request);
    
    boolean verifyNotifySign(String notifyData);
    
    boolean isOrderCompleted(String orderNo);
}