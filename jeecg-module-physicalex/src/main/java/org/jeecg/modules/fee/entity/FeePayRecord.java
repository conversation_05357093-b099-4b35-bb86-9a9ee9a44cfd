package org.jeecg.modules.fee.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.modules.reg.entity.Customer;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 收费记录
 * @Author: jeecg-boot
 * @Date: 2024-06-25
 * @Version: V1.0
 */
@Data
@TableName("fee_pay_record")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "fee_pay_record对象", description = "收费记录")
public class FeePayRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 支付单ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "支付单ID")
    private String id;

    /**
     * 登记记录ID：针对个人
     */
    @Excel(name = "登记记录ID", width = 15)
    @ApiModelProperty(value = "登记记录ID")
    private String customerRegId;
    /**
     * 档案号：针对个人
     */
    @Excel(name = "档案号", width = 15)
    @ApiModelProperty(value = "档案号")
    private String archivesNum;
    /**
     * 单位预约ID：针对团体
     */
    @Excel(name = "单位预约ID", width = 15)
    @ApiModelProperty(value = "单位预约ID")
    private String companyRegId;
    /**
     * 单位ID：针对团体
     */
    @Excel(name = "单位ID", width = 15)
    @ApiModelProperty(value = "单位ID")
    private String companyId;
    /**
     * 支付渠道
     */
    @Excel(name = "支付渠道", width = 15)
    @ApiModelProperty(value = "支付渠道")
    private String payChannel;
    /**
     * 支付渠道的支付方式
     */
    @Excel(name = "支付渠道的支付方式", width = 15)
    @ApiModelProperty(value = "支付渠道的支付方式")
    private String payChannelWay;
    /**
     * 支付金额,单位分
     */
    @Excel(name = "支付金额,单位分", width = 15)
    @ApiModelProperty(value = "支付金额,单位分")
    private BigDecimal amount;
    /**
     * 三位货币代码,人民币:cny
     */
    @Excel(name = "三位货币代码,人民币:cny", width = 15)
    @ApiModelProperty(value = "三位货币代码,人民币:cny")
    private String currency;
    /**
     * 支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭
     */
    @Excel(name = "支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭", width = 15)
    @ApiModelProperty(value = "支付状态: 0-订单生成, 1-支付中, 2-支付成功, 3-支付失败, 4-已撤销, 5-已退款, 6-订单关闭")
    private String state;
    /**
     * 客户端IP
     */
    @Excel(name = "客户端IP", width = 15)
    @ApiModelProperty(value = "客户端IP")
    private String clientIp;
    /**
     * 渠道订单号
     */
    @Excel(name = "渠道订单号", width = 15)
    @ApiModelProperty(value = "渠道订单号")
    private String channelOrderNo;
    /**
     * 退款状态: 0-未发生实际退款, 1-部分退款, 2-全额退款
     */
    @Excel(name = "退款状态: 0-未发生实际退款, 1-部分退款, 2-全额退款", width = 15)
    @ApiModelProperty(value = "退款状态: 0-未发生实际退款, 1-部分退款, 2-全额退款")
    private String refundState;
    /**
     * 退款次数
     */
    @Excel(name = "退款次数", width = 15)
    @ApiModelProperty(value = "退款次数")
    private Integer refundTimes;
    /**
     * 退款总金额
     */
    @Excel(name = "退款总金额", width = 15)
    @ApiModelProperty(value = "退款总金额")
    private BigDecimal refundAmount;
    /**
     * 渠道支付错误码
     */
    @Excel(name = "渠道支付错误码", width = 15)
    @ApiModelProperty(value = "渠道支付错误码")
    private String errCode;
    /**
     * 订单支付成功时间
     */
    @Excel(name = "订单支付成功时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单支付成功时间")
    private Date successTime;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "创建时间")
    private Date createdTime;
    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 15, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updatedTime;
    /**
     * 收费员账号
     */
    @ApiModelProperty(value = "收费员账号")
    private String createBy;
    /**
     * 收费员姓名
     */
    @Excel(name = "收费员姓名", width = 15)
    @ApiModelProperty(value = "收费员姓名")
    private String creator;
    /**
     * 体检号
     */
    @Excel(name = "体检号", width = 15)
    @ApiModelProperty(value = "体检号")
    private String examNo;
    /**
     * 体检人
     */
    @Excel(name = "体检人", width = 15)
    @ApiModelProperty(value = "体检人")
    private String name;
    /**
     * 体检单位
     */
    @Excel(name = "体检单位", width = 15)
    @ApiModelProperty(value = "体检单位")
    private String componyName;
    /**
     * 渠道支付错误描述
     */
    @Excel(name = "渠道支付错误描述", width = 15)
    @ApiModelProperty(value = "渠道支付错误描述")
    private String errMsg;
    /**
     * 支付方类型
     */
    @Excel(name = "支付方类型", width = 15)
    @ApiModelProperty(value = "支付方类型")
    private String payerType;
    /**
     * his收费申请号
     */
    @Excel(name = "his收费申请号", width = 15)
    @ApiModelProperty(value = "his收费申请号")
    private String hisApplyNo;
    /**
     * 业务类型
     */
    @Excel(name = "业务类型", width = 15)
    @ApiModelProperty(value = "业务类型")
    private String bizType;
    /**
     * 业务ID
     */
    @Excel(name = "业务ID", width = 15)
    @ApiModelProperty(value = "业务ID")
    private String bizId;
    /**
     * 组合支付情况下的父ID
     */
    @Excel(name = "组合支付情况下的父ID", width = 15)
    @ApiModelProperty(value = "组合支付情况下的父ID")
    private String pid;
    /**
     * 结账单Id
     */
    @Excel(name = "结账单Id", width = 15)
    @ApiModelProperty(value = "结账单Id")
    private String billId;
    /**
     * 结账单号
     */
    @Excel(name = "结账单号", width = 15)
    @ApiModelProperty(value = "结账单号")
    private String billNo;
    /**
     * 业务信息，比如卡号
     */
    @Excel(name = "业务信息", width = 15)
    @ApiModelProperty(value = "业务信息")
    private String bizInfo;

    /**
     * 门诊支付专用，全额退部分收，记录全额退款的退款记录ID
     */
    @Excel(name = "门诊支付专用，全额退部分收，记录全额退款的退款记录ID", width = 15)
    @ApiModelProperty(value = "门诊支付专用，全额退部分收，记录全额退款的退款记录ID")
    private String hisRefundRecordId;

    private String receiptPic;
    @ApiModelProperty(value = "收费申请单号")
    private String payApplyNo;
    @ApiModelProperty(value = "his收费明细号")
    private String hisDetailNo;
    @ApiModelProperty(value = "his收费小序号")
    private String hisChargeNo;
    @ApiModelProperty(value = "备注")
    private String remark;
    private String teamId;

    @TableField(exist = false)
    private String wayCode;
    @TableField(exist = false)
    private String authCode;
    @TableField(exist = false)
    private String pwd;
    @TableField(exist = false)
    private String cardNo;
    @TableField(exist = false)
    private List<String> customerRegItemGroupIds;
    @TableField(exist = false)
    private List<FeePayRecord> subPayRecords;
    @TableField(exist = false)
    private Customer customer;
    @TableField(exist = false)
    private List<String> refundItemGroupIds;
    @TableField(exist = false)
    private BigDecimal applyRefundAmount;
    @TableField(exist = false)
    private boolean refundDone;
    @TableField(exist = false)
    private String billRefundId;
    @TableField(exist = false)
    private String billRefundNo;

    @TableField(exist = false)
    private String customerId;
    @TableField(exist = false)
    private String originCustomerLimitAmountId;
    @TableField(exist = false)
    private String hisVisitNo;
    @TableField(exist = false)
    private String hisPid;
    @TableField(exist = false)
    private List<String> refundCustomerRegItemGroupIds;

}
