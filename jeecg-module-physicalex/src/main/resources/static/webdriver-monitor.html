<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebDriver监控仪表板</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .status-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-card h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .metric-value {
            font-weight: bold;
        }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-danger { color: #dc3545; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-warning:hover {
            background: #e0a800;
        }
        .log-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        .log-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #2196F3;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WebDriver监控仪表板</h1>
            <p>实时监控WebDriver池状态和Chrome进程</p>
        </div>

        <div class="auto-refresh">
            <label class="switch">
                <input type="checkbox" id="autoRefresh" checked>
                <span class="slider"></span>
            </label>
            <label for="autoRefresh">自动刷新 (30秒)</label>
            <button class="btn" onclick="refreshData()">立即刷新</button>
        </div>

        <div class="status-grid">
            <div class="status-card">
                <h3>WebDriver池状态</h3>
                <div id="poolStatus" class="loading">加载中...</div>
            </div>

            <div class="status-card">
                <h3>Chrome进程监控</h3>
                <div id="processStatus" class="loading">加载中...</div>
            </div>

            <div class="status-card">
                <h3>系统健康状态</h3>
                <div id="healthStatus" class="loading">加载中...</div>
            </div>

            <div class="status-card">
                <h3>工作状态</h3>
                <div id="workingStatus" class="loading">加载中...</div>
            </div>

            <div class="status-card">
                <h3>管理操作</h3>
                <button class="btn" onclick="safeCleanup()">安全清理Chrome进程</button>
                <button class="btn btn-warning" onclick="forceCleanup(false)">强制清理Chrome进程</button>
                <button class="btn btn-danger" onclick="forceCleanup(true)">强制清理(忽略工作状态)</button>
                <button class="btn btn-danger" onclick="restartPool()">重启WebDriver池</button>
                <button class="btn" onclick="showProcessDetails()">查看进程详情</button>
            </div>
        </div>

        <div class="log-container">
            <h3>最近事件日志</h3>
            <div id="eventLog" class="log-content loading">加载中...</div>
        </div>

        <div class="log-container">
            <h3>系统进程信息</h3>
            <div id="processInfo" class="log-content loading">加载中...</div>
        </div>
    </div>

    <script>
        let autoRefreshInterval;
        const API_BASE = '/jeecg-boot/webdriver/management';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            setupAutoRefresh();
        });

        // 设置自动刷新
        function setupAutoRefresh() {
            const checkbox = document.getElementById('autoRefresh');
            
            function toggleAutoRefresh() {
                if (checkbox.checked) {
                    autoRefreshInterval = setInterval(refreshData, 30000);
                } else {
                    clearInterval(autoRefreshInterval);
                }
            }
            
            checkbox.addEventListener('change', toggleAutoRefresh);
            toggleAutoRefresh(); // 初始设置
        }

        // 刷新数据
        async function refreshData() {
            try {
                await Promise.all([
                    loadPoolStatus(),
                    loadHealthStatus(),
                    loadProcessInfo()
                ]);
            } catch (error) {
                console.error('刷新数据失败:', error);
            }
        }

        // 加载池状态
        async function loadPoolStatus() {
            try {
                const response = await fetch(`${API_BASE}/pool/status`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.result;
                    document.getElementById('poolStatus').innerHTML = `
                        <div class="metric">
                            <span>池状态:</span>
                            <span class="metric-value">${data.poolStatus}</span>
                        </div>
                        <div class="metric">
                            <span>当前进程数:</span>
                            <span class="metric-value ${data.currentProcessCount > 10 ? 'status-danger' : data.currentProcessCount > 5 ? 'status-warning' : 'status-healthy'}">${data.currentProcessCount}</span>
                        </div>
                        <div class="metric">
                            <span>总清理次数:</span>
                            <span class="metric-value">${data.totalCleanupCount}</span>
                        </div>
                        <div class="metric">
                            <span>更新时间:</span>
                            <span class="metric-value">${new Date(data.reportTime).toLocaleString()}</span>
                        </div>
                    `;

                    // 更新工作状态
                    const workingStatusHtml = `
                        <div class="metric">
                            <span>工作中实例:</span>
                            <span class="metric-value ${data.workingDriverCount > 0 ? 'status-warning' : 'status-healthy'}">${data.workingDriverCount}</span>
                        </div>
                        <div class="metric">
                            <span>可安全清理:</span>
                            <span class="metric-value ${data.canSafelyCleanup ? 'status-healthy' : 'status-warning'}">${data.canSafelyCleanup ? '是' : '否'}</span>
                        </div>
                        ${Object.keys(data.workingTasks || {}).length > 0 ?
                            '<div style="margin-top: 10px;"><strong>正在工作的任务:</strong><br>' +
                            Object.entries(data.workingTasks).map(([taskId, taskInfo]) =>
                                `<small>${taskId}: ${taskInfo}</small>`
                            ).join('<br>') + '</div>' :
                            '<div style="margin-top: 10px;"><small>当前无工作任务</small></div>'
                        }
                    `;
                    document.getElementById('workingStatus').innerHTML = workingStatusHtml;
                    
                    // 更新事件日志
                    if (data.recentEvents && data.recentEvents.length > 0) {
                        const eventLog = data.recentEvents.map(event => 
                            `[${event.timestamp}] ${event.type}: ${event.message}`
                        ).join('\n');
                        document.getElementById('eventLog').textContent = eventLog;
                    }
                } else {
                    document.getElementById('poolStatus').innerHTML = `<div class="status-danger">加载失败: ${result.message}</div>`;
                }
            } catch (error) {
                document.getElementById('poolStatus').innerHTML = `<div class="status-danger">网络错误: ${error.message}</div>`;
            }
        }

        // 加载健康状态
        async function loadHealthStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.result;
                    const statusClass = data.overallHealthy ? 'status-healthy' : 'status-danger';
                    
                    document.getElementById('healthStatus').innerHTML = `
                        <div class="metric">
                            <span>整体状态:</span>
                            <span class="metric-value ${statusClass}">${data.status}</span>
                        </div>
                        <div class="metric">
                            <span>池健康:</span>
                            <span class="metric-value ${data.poolHealthy ? 'status-healthy' : 'status-danger'}">${data.poolHealthy ? '正常' : '异常'}</span>
                        </div>
                        <div class="metric">
                            <span>进程健康:</span>
                            <span class="metric-value ${data.processHealthy ? 'status-healthy' : 'status-danger'}">${data.processHealthy ? '正常' : '异常'}</span>
                        </div>
                        <div class="metric">
                            <span>进程数量:</span>
                            <span class="metric-value">${data.processCount}</span>
                        </div>
                    `;
                } else {
                    document.getElementById('healthStatus').innerHTML = `<div class="status-danger">加载失败: ${result.message}</div>`;
                }
            } catch (error) {
                document.getElementById('healthStatus').innerHTML = `<div class="status-danger">网络错误: ${error.message}</div>`;
            }
        }

        // 加载进程信息
        async function loadProcessInfo() {
            try {
                const response = await fetch(`${API_BASE}/processes/chrome`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.result;
                    document.getElementById('processInfo').textContent = data.systemProcessList || '无进程信息';
                } else {
                    document.getElementById('processInfo').textContent = `加载失败: ${result.message}`;
                }
            } catch (error) {
                document.getElementById('processInfo').textContent = `网络错误: ${error.message}`;
            }
        }

        // 安全清理
        async function safeCleanup() {
            if (!confirm('确定要安全清理Chrome进程吗？系统将等待正在工作的任务完成。')) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/cleanup/safe`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.success) {
                    alert('安全清理成功: ' + result.result);
                    refreshData();
                } else {
                    alert('安全清理失败: ' + result.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        // 强制清理
        async function forceCleanup(ignoreWorkingStatus = false) {
            const message = ignoreWorkingStatus ?
                '确定要强制清理所有Chrome进程吗？这将终止所有正在进行的PDF生成任务！' :
                '确定要强制清理Chrome进程吗？如果有正在工作的任务，清理将被拒绝。';

            if (!confirm(message)) {
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/cleanup/force?ignoreWorkingStatus=${ignoreWorkingStatus}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();

                if (result.success) {
                    alert('强制清理成功: ' + result.result);
                    refreshData();
                } else {
                    alert('强制清理失败: ' + result.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        // 重启池
        async function restartPool() {
            if (!confirm('确定要重启WebDriver池吗？这将重新初始化所有WebDriver实例。')) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/pool/restart`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                const result = await response.json();
                
                if (result.success) {
                    alert('重启成功: ' + result.result);
                    refreshData();
                } else {
                    alert('重启失败: ' + result.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        // 显示进程详情
        function showProcessDetails() {
            const processInfo = document.getElementById('processInfo').textContent;
            const newWindow = window.open('', '_blank', 'width=800,height=600');
            newWindow.document.write(`
                <html>
                    <head><title>Chrome进程详情</title></head>
                    <body style="font-family: monospace; padding: 20px;">
                        <h2>Chrome进程详情</h2>
                        <pre>${processInfo}</pre>
                        <button onclick="window.close()">关闭</button>
                    </body>
                </html>
            `);
        }
    </script>
</body>
</html>
