-- 表单规则管理系统 - 菜单权限插入SQL
-- 执行前请确保sys_permission表存在
-- 建议在系统管理员账号下执行

-- =====================================================
-- 1. 主菜单：表单规则管理
-- =====================================================
INSERT INTO sys_permission(
    id, 
    parent_id, 
    name, 
    url, 
    component, 
    component_name, 
    redirect, 
    menu_type, 
    perms, 
    perms_type, 
    sort_no, 
    always_show, 
    icon, 
    is_route, 
    is_leaf, 
    keep_alive, 
    hidden, 
    hide_tab, 
    description, 
    status, 
    del_flag, 
    rule_flag, 
    create_by, 
    create_time, 
    update_by, 
    update_time, 
    internal_or_external
) VALUES (
    '2025081608001180410',           -- id: 主菜单ID
    NULL,                            -- parent_id: 顶级菜单
    '表单规则管理',                   -- name: 菜单名称
    '/system/formRuleManagementList', -- url: 访问路径
    'system/FormRuleManagement',      -- component: 前端组件路径
    NULL,                            -- component_name
    NULL,                            -- redirect
    0,                               -- menu_type: 0=菜单
    NULL,                            -- perms: 主菜单无需权限
    '1',                             -- perms_type: 1=前端控制
    1000.00,                         -- sort_no: 排序号
    0,                               -- always_show: 0=不总是显示
    'ant-design:form-outlined',       -- icon: 菜单图标
    1,                               -- is_route: 1=是路由
    0,                               -- is_leaf: 0=非叶子节点
    0,                               -- keep_alive: 0=不缓存
    0,                               -- hidden: 0=不隐藏
    0,                               -- hide_tab: 0=不隐藏标签
    '表单规则配置和管理',             -- description: 描述
    '1',                             -- status: 1=启用
    0,                               -- del_flag: 0=未删除
    0,                               -- rule_flag: 0=非规则
    'admin',                         -- create_by: 创建人
    '2025-08-16 20:00:00',          -- create_time: 创建时间
    NULL,                            -- update_by
    NULL,                            -- update_time
    0                                -- internal_or_external: 0=内部
);

-- =====================================================
-- 2. 权限控制：按钮级权限
-- =====================================================

-- 2.1 查看权限
INSERT INTO sys_permission(
    id, parent_id, name, url, component, is_route, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, 
    keep_alive, hidden, hide_tab, description, create_by, create_time, 
    update_by, update_time, del_flag, rule_flag, status, internal_or_external
) VALUES (
    '2025081608001180411',           -- id
    '2025081608001180410',           -- parent_id: 指向主菜单
    '查看表单规则',                   -- name
    NULL, NULL, 0, NULL, NULL,       -- url等字段为空
    2,                               -- menu_type: 2=按钮
    'form:rule:view',                -- perms: 权限标识
    '1',                             -- perms_type
    1,                               -- sort_no
    0, NULL, 1, 0, 0, 0,            -- 其他显示控制
    '查看表单规则列表和详情',         -- description
    'admin', '2025-08-16 20:00:00',  -- 创建信息
    NULL, NULL, 0, 0, '1', 0         -- 其他字段
);

-- 2.2 新增权限
INSERT INTO sys_permission(
    id, parent_id, name, url, component, is_route, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, 
    keep_alive, hidden, hide_tab, description, create_by, create_time, 
    update_by, update_time, del_flag, rule_flag, status, internal_or_external
) VALUES (
    '2025081608001180412', '2025081608001180410', '添加表单规则', 
    NULL, NULL, 0, NULL, NULL, 2, 'form:rule:create', '1', 2, 
    0, NULL, 1, 0, 0, 0, '创建新的表单规则配置', 
    'admin', '2025-08-16 20:00:00', NULL, NULL, 0, 0, '1', 0
);

-- 2.3 编辑权限
INSERT INTO sys_permission(
    id, parent_id, name, url, component, is_route, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, 
    keep_alive, hidden, hide_tab, description, create_by, create_time, 
    update_by, update_time, del_flag, rule_flag, status, internal_or_external
) VALUES (
    '2025081608001180413', '2025081608001180410', '编辑表单规则', 
    NULL, NULL, 0, NULL, NULL, 2, 'form:rule:update', '1', 3, 
    0, NULL, 1, 0, 0, 0, '修改现有表单规则配置', 
    'admin', '2025-08-16 20:00:00', NULL, NULL, 0, 0, '1', 0
);

-- 2.4 删除权限
INSERT INTO sys_permission(
    id, parent_id, name, url, component, is_route, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, 
    keep_alive, hidden, hide_tab, description, create_by, create_time, 
    update_by, update_time, del_flag, rule_flag, status, internal_or_external
) VALUES (
    '2025081608001180414', '2025081608001180410', '删除表单规则', 
    NULL, NULL, 0, NULL, NULL, 2, 'form:rule:delete', '1', 4, 
    0, NULL, 1, 0, 0, 0, '删除表单规则配置', 
    'admin', '2025-08-16 20:00:00', NULL, NULL, 0, 0, '1', 0
);

-- 2.5 状态管理权限
INSERT INTO sys_permission(
    id, parent_id, name, url, component, is_route, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, 
    keep_alive, hidden, hide_tab, description, create_by, create_time, 
    update_by, update_time, del_flag, rule_flag, status, internal_or_external
) VALUES (
    '2025081608001180415', '2025081608001180410', '管理表单规则状态', 
    NULL, NULL, 0, NULL, NULL, 2, 'form:rule:status', '1', 5, 
    0, NULL, 1, 0, 0, 0, '启用或禁用表单规则', 
    'admin', '2025-08-16 20:00:00', NULL, NULL, 0, 0, '1', 0
);

-- 2.6 复制权限
INSERT INTO sys_permission(
    id, parent_id, name, url, component, is_route, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, 
    keep_alive, hidden, hide_tab, description, create_by, create_time, 
    update_by, update_time, del_flag, rule_flag, status, internal_or_external
) VALUES (
    '2025081608001180416', '2025081608001180410', '复制表单规则', 
    NULL, NULL, 0, NULL, NULL, 2, 'form:rule:copy', '1', 6, 
    0, NULL, 1, 0, 0, 0, '复制表单规则到其他表单', 
    'admin', '2025-08-16 20:00:00', NULL, NULL, 0, 0, '1', 0
);

-- 2.7 缓存管理权限
INSERT INTO sys_permission(
    id, parent_id, name, url, component, is_route, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, 
    keep_alive, hidden, hide_tab, description, create_by, create_time, 
    update_by, update_time, del_flag, rule_flag, status, internal_or_external
) VALUES (
    '2025081608001180417', '2025081608001180410', '清除表单规则缓存', 
    NULL, NULL, 0, NULL, NULL, 2, 'form:rule:cache:clear', '1', 7, 
    0, NULL, 1, 0, 0, 0, '手动清除表单规则缓存', 
    'admin', '2025-08-16 20:00:00', NULL, NULL, 0, 0, '1', 0
);

-- =====================================================
-- 3. 为管理员角色分配权限（可选）
-- =====================================================

-- 查询管理员角色ID（通常是admin角色）
-- SELECT id FROM sys_role WHERE role_code = 'admin';

-- 为admin角色分配表单规则管理的所有权限
-- 注意：请根据实际的角色ID调整role_id值
INSERT INTO sys_role_permission (id, role_id, permission_id) VALUES 
('form_rule_role_perm_001', 'admin', '2025081608001180410'),  -- 主菜单权限
('form_rule_role_perm_002', 'admin', '2025081608001180411'),  -- 查看权限
('form_rule_role_perm_003', 'admin', '2025081608001180412'),  -- 新增权限
('form_rule_role_perm_004', 'admin', '2025081608001180413'),  -- 编辑权限
('form_rule_role_perm_005', 'admin', '2025081608001180414'),  -- 删除权限
('form_rule_role_perm_006', 'admin', '2025081608001180415'),  -- 状态管理权限
('form_rule_role_perm_007', 'admin', '2025081608001180416'),  -- 复制权限
('form_rule_role_perm_008', 'admin', '2025081608001180417');  -- 缓存管理权限

-- =====================================================
-- 4. 验证插入结果
-- =====================================================

-- 查看插入的菜单
SELECT id, name, url, perms, menu_type, status 
FROM sys_permission 
WHERE id LIKE '202508160800118041%' 
ORDER BY id;

-- 查看权限分配情况
SELECT r.role_name, p.name as permission_name, p.perms 
FROM sys_role r 
JOIN sys_role_permission rp ON r.id = rp.role_id 
JOIN sys_permission p ON rp.permission_id = p.id 
WHERE p.id LIKE '202508160800118041%';

-- =====================================================
-- 说明：
-- 1. 执行前请备份sys_permission表
-- 2. 如果ID冲突，请修改ID值
-- 3. 根据实际角色情况调整权限分配
-- 4. 前端组件路径为：system/FormRuleManagement
-- 5. 访问路径为：/system/formRuleManagementList
-- =====================================================
