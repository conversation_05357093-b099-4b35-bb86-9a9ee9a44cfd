-- 表单规则管理系统数据库表结构 - 简化版
-- 适用于MySQL 5.7+
-- 简化原则：去除复杂的缓存版本控制，保留核心功能

-- 1. 表单规则配置主表（核心表）
DROP TABLE IF EXISTS `form_rule_config`;
CREATE TABLE `form_rule_config` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `form_code` varchar(100) NOT NULL COMMENT '表单代码',
  `form_name` varchar(200) NOT NULL COMMENT '表单名称',
  `description` varchar(500) DEFAULT NULL COMMENT '表单描述',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `field_rules` json NOT NULL COMMENT '字段规则JSON配置',
  `dependency_rules` json NOT NULL COMMENT '联动规则JSON配置',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0未删除，1已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_form_code` (`form_code`),
  KEY `idx_status` (`status`),
  KEY `idx_update_time` (`update_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单规则配置表';

-- 2. 表单字段元数据表（用于规则配置）
DROP TABLE IF EXISTS `form_field_metadata`;
CREATE TABLE `form_field_metadata` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `form_code` varchar(100) NOT NULL COMMENT '表单代码',
  `field_code` varchar(100) NOT NULL COMMENT '字段代码',
  `field_name` varchar(200) NOT NULL COMMENT '字段名称',
  `field_type` varchar(50) NOT NULL COMMENT '字段类型：string,number,date,boolean,select,radio,checkbox',
  `data_type` varchar(50) DEFAULT NULL COMMENT '数据类型：varchar,int,decimal,datetime,text',
  `max_length` int(11) DEFAULT NULL COMMENT '最大长度',
  `is_nullable` tinyint(1) DEFAULT 1 COMMENT '是否可为空：1可空，0不可空',
  `default_value` varchar(500) DEFAULT NULL COMMENT '默认值',
  `options_data` json DEFAULT NULL COMMENT '选项数据（用于select,radio,checkbox）',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序号',
  `description` varchar(500) DEFAULT NULL COMMENT '字段描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_form_field` (`form_code`, `field_code`),
  KEY `idx_form_code` (`form_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单字段元数据表';

-- 3. 表单规则变更历史表（审计用，可选）
DROP TABLE IF EXISTS `form_rule_change_log`;
CREATE TABLE `form_rule_change_log` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `form_code` varchar(100) NOT NULL COMMENT '表单代码',
  `change_type` varchar(50) NOT NULL COMMENT '变更类型：CREATE,UPDATE,DELETE,STATUS_CHANGE',
  `version_before` int(11) DEFAULT NULL COMMENT '变更前版本',
  `version_after` int(11) DEFAULT NULL COMMENT '变更后版本',
  `change_summary` varchar(500) DEFAULT NULL COMMENT '变更摘要',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `operator_ip` varchar(50) DEFAULT NULL COMMENT '操作IP',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_form_code` (`form_code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单规则变更历史表';

-- 4. 插入客户登记表单的字段元数据
INSERT INTO `form_field_metadata` (`id`, `form_code`, `field_code`, `field_name`, `field_type`, `data_type`, `max_length`, `is_nullable`, `default_value`, `options_data`, `sort_order`, `description`) VALUES
('field_001', 'customer_reg_form', 'examCategory', '体检分类', 'select', 'varchar', 50, 0, NULL, JSON_ARRAY(
  JSON_OBJECT('value', '个人体检', 'label', '个人体检'),
  JSON_OBJECT('value', '团体体检', 'label', '团体体检'),
  JSON_OBJECT('value', '干部体检', 'label', '干部体检'),
  JSON_OBJECT('value', '入职体检', 'label', '入职体检')
), 1, '选择体检类型'),

('field_002', 'customer_reg_form', 'appointmentDate', '预约日期', 'date', 'datetime', NULL, 0, NULL, NULL, 2, '选择体检预约日期'),

('field_003', 'customer_reg_form', 'name', '姓名', 'string', 'varchar', 50, 0, NULL, NULL, 3, '客户真实姓名'),

('field_004', 'customer_reg_form', 'cardType', '证件类型', 'select', 'varchar', 20, 0, '身份证', JSON_ARRAY(
  JSON_OBJECT('value', '身份证', 'label', '身份证'),
  JSON_OBJECT('value', '护照', 'label', '护照'),
  JSON_OBJECT('value', '军官证', 'label', '军官证'),
  JSON_OBJECT('value', '其他', 'label', '其他')
), 4, '证件类型选择'),

('field_005', 'customer_reg_form', 'idCard', '证件号', 'string', 'varchar', 30, 0, NULL, NULL, 5, '证件号码'),

('field_006', 'customer_reg_form', 'gender', '性别', 'radio', 'varchar', 10, 0, NULL, JSON_ARRAY(
  JSON_OBJECT('value', '男', 'label', '男'),
  JSON_OBJECT('value', '女', 'label', '女')
), 6, '性别选择'),

('field_007', 'customer_reg_form', 'birthday', '出生日期', 'date', 'date', NULL, 0, NULL, NULL, 7, '出生日期'),

('field_008', 'customer_reg_form', 'phone', '电话', 'string', 'varchar', 20, 0, NULL, NULL, 8, '联系电话'),

('field_009', 'customer_reg_form', 'age', '年龄', 'number', 'int', NULL, 1, NULL, NULL, 9, '年龄（自动计算）'),

('field_010', 'customer_reg_form', 'marriageStatus', '婚姻状况', 'select', 'varchar', 20, 1, NULL, JSON_ARRAY(
  JSON_OBJECT('value', '未婚', 'label', '未婚'),
  JSON_OBJECT('value', '已婚', 'label', '已婚'),
  JSON_OBJECT('value', '离异', 'label', '离异'),
  JSON_OBJECT('value', '丧偶', 'label', '丧偶')
), 10, '婚姻状况'),

('field_011', 'customer_reg_form', 'career', '职业', 'string', 'varchar', 100, 1, NULL, NULL, 11, '职业信息'),

('field_012', 'customer_reg_form', 'emergencyContact', '紧急联系人', 'string', 'varchar', 50, 1, NULL, NULL, 12, '紧急联系人姓名'),

('field_013', 'customer_reg_form', 'emergencyPhone', '紧急电话', 'string', 'varchar', 20, 1, NULL, NULL, 13, '紧急联系人电话'),

('field_014', 'customer_reg_form', 'pregnancyFlag', '是否备孕', 'radio', 'tinyint', NULL, 1, '0', JSON_ARRAY(
  JSON_OBJECT('value', 1, 'label', '是'),
  JSON_OBJECT('value', 0, 'label', '否')
), 14, '是否备孕（仅女性）'),

('field_015', 'customer_reg_form', 'companyName', '单位名称', 'string', 'varchar', 200, 1, NULL, NULL, 15, '工作单位名称');

-- 5. 插入默认的客户登记表单规则配置
INSERT INTO `form_rule_config` (
  `id`, 
  `form_code`, 
  `form_name`, 
  `description`,
  `version`, 
  `status`, 
  `field_rules`, 
  `dependency_rules`,
  `create_by`
) VALUES (
  'customer_reg_form_001',
  'customer_reg_form',
  '客户登记表单',
  '体检客户登记表单的验证规则和字段联动配置',
  1,
  1,
  JSON_ARRAY(
    JSON_OBJECT(
      'fieldCode', 'examCategory',
      'fieldName', '体检分类',
      'isRequired', true,
      'requiredMessage', '请选择体检分类',
      'visible', true,
      'disabled', false,
      'sortOrder', 1
    ),
    JSON_OBJECT(
      'fieldCode', 'appointmentDate',
      'fieldName', '预约日期',
      'isRequired', true,
      'requiredMessage', '请选择预约日期',
      'visible', true,
      'disabled', false,
      'sortOrder', 2
    ),
    JSON_OBJECT(
      'fieldCode', 'name',
      'fieldName', '姓名',
      'isRequired', true,
      'requiredMessage', '请输入姓名',
      'visible', true,
      'disabled', false,
      'sortOrder', 3
    ),
    JSON_OBJECT(
      'fieldCode', 'cardType',
      'fieldName', '证件类型',
      'isRequired', true,
      'requiredMessage', '请选择证件类型',
      'visible', true,
      'disabled', false,
      'sortOrder', 4
    ),
    JSON_OBJECT(
      'fieldCode', 'idCard',
      'fieldName', '证件号',
      'isRequired', true,
      'requiredMessage', '请输入证件号',
      'visible', true,
      'disabled', false,
      'sortOrder', 5,
      'validationRules', JSON_ARRAY(
        JSON_OBJECT(
          'type', 'pattern',
          'value', '^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$',
          'message', '请输入正确的身份证号'
        )
      )
    ),
    JSON_OBJECT(
      'fieldCode', 'gender',
      'fieldName', '性别',
      'isRequired', true,
      'requiredMessage', '请选择性别',
      'visible', true,
      'disabled', false,
      'sortOrder', 6
    ),
    JSON_OBJECT(
      'fieldCode', 'birthday',
      'fieldName', '出生日期',
      'isRequired', true,
      'requiredMessage', '请选择出生日期',
      'visible', true,
      'disabled', false,
      'sortOrder', 7
    ),
    JSON_OBJECT(
      'fieldCode', 'phone',
      'fieldName', '电话',
      'isRequired', true,
      'requiredMessage', '请输入电话',
      'visible', true,
      'disabled', false,
      'sortOrder', 8,
      'validationRules', JSON_ARRAY(
        JSON_OBJECT(
          'type', 'pattern',
          'value', '^(?:\\d{11}|(?:\\d{3,4}-)\\d{7,8})$',
          'message', '电话必须是11位数字或固定电话（如：0471-2537660）'
        )
      )
    ),
    JSON_OBJECT(
      'fieldCode', 'age',
      'fieldName', '年龄',
      'isRequired', false,
      'visible', true,
      'disabled', true,
      'sortOrder', 9
    ),
    JSON_OBJECT(
      'fieldCode', 'marriageStatus',
      'fieldName', '婚姻状况',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 10
    ),
    JSON_OBJECT(
      'fieldCode', 'career',
      'fieldName', '职业',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 11
    ),
    JSON_OBJECT(
      'fieldCode', 'emergencyContact',
      'fieldName', '紧急联系人',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 12
    ),
    JSON_OBJECT(
      'fieldCode', 'emergencyPhone',
      'fieldName', '紧急电话',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 13,
      'validationRules', JSON_ARRAY(
        JSON_OBJECT(
          'type', 'pattern',
          'value', '^\\d{11}$',
          'message', '紧急电话必须是11位数字'
        )
      )
    ),
    JSON_OBJECT(
      'fieldCode', 'pregnancyFlag',
      'fieldName', '是否备孕',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 14
    ),
    JSON_OBJECT(
      'fieldCode', 'companyName',
      'fieldName', '单位名称',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 15
    )
  ),
  JSON_ARRAY(
    JSON_OBJECT(
      'id', 'dep_exam_category_phone',
      'sourceField', 'examCategory',
      'targetField', 'phone',
      'dependencyType', 'required',
      'conditionType', 'not_equals',
      'conditionValue', '干部体检',
      'actionValue', true,
      'priority', 10
    ),
    JSON_OBJECT(
      'id', 'dep_exam_category_idcard',
      'sourceField', 'examCategory',
      'targetField', 'idCard',
      'dependencyType', 'required',
      'conditionType', 'not_equals',
      'conditionValue', '干部体检',
      'actionValue', true,
      'priority', 10
    ),
    JSON_OBJECT(
      'id', 'dep_gender_pregnancy',
      'sourceField', 'gender',
      'targetField', 'pregnancyFlag',
      'dependencyType', 'visible',
      'conditionType', 'equals',
      'conditionValue', '女',
      'actionValue', true,
      'priority', 5
    )
  ),
  'system'
);

-- 4. 创建索引优化查询性能
CREATE INDEX `idx_form_rule_config_composite` ON `form_rule_config` (`form_code`, `status`, `version`);
CREATE INDEX `idx_form_rule_change_log_composite` ON `form_rule_change_log` (`form_code`, `create_time`);

-- 5. 创建视图简化查询（可选）
CREATE VIEW `v_form_rule_summary` AS
SELECT 
  frc.`form_code`,
  frc.`form_name`,
  frc.`version`,
  frc.`status`,
  JSON_LENGTH(frc.`field_rules`) as `field_count`,
  JSON_LENGTH(frc.`dependency_rules`) as `dependency_count`,
  frc.`update_time`
FROM `form_rule_config` frc
WHERE frc.`del_flag` = 0;
