-- 表单规则管理系统数据库表结构
-- 适用于MySQL 5.7+

-- 1. 表单规则配置主表
DROP TABLE IF EXISTS `form_rule_config`;
CREATE TABLE `form_rule_config` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `form_code` varchar(100) NOT NULL COMMENT '表单代码',
  `form_name` varchar(200) NOT NULL COMMENT '表单名称',
  `description` varchar(500) DEFAULT NULL COMMENT '表单描述',
  `version` int(11) NOT NULL DEFAULT 1 COMMENT '版本号',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `field_rules` json NOT NULL COMMENT '字段规则JSON配置',
  `dependency_rules` json NOT NULL COMMENT '联动规则JSON配置',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0未删除，1已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_form_code` (`form_code`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单规则配置表';

-- 2. 表单规则变更历史表
DROP TABLE IF EXISTS `form_rule_change_log`;
CREATE TABLE `form_rule_change_log` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `form_code` varchar(100) NOT NULL COMMENT '表单代码',
  `change_type` varchar(50) NOT NULL COMMENT '变更类型：CREATE,UPDATE,DELETE,STATUS_CHANGE',
  `version_before` int(11) DEFAULT NULL COMMENT '变更前版本',
  `version_after` int(11) DEFAULT NULL COMMENT '变更后版本',
  `change_content` json DEFAULT NULL COMMENT '变更内容详情',
  `change_summary` varchar(500) DEFAULT NULL COMMENT '变更摘要',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作人',
  `operator_ip` varchar(50) DEFAULT NULL COMMENT '操作IP',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `idx_form_code` (`form_code`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单规则变更历史表';

-- 3. 表单规则缓存版本表
DROP TABLE IF EXISTS `form_rule_cache_version`;
CREATE TABLE `form_rule_cache_version` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `form_code` varchar(100) NOT NULL COMMENT '表单代码',
  `version` int(11) NOT NULL COMMENT '当前版本号',
  `cache_key` varchar(200) NOT NULL COMMENT 'Redis缓存键',
  `last_updated` bigint(20) NOT NULL COMMENT '最后更新时间戳',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_form_code` (`form_code`),
  KEY `idx_version` (`version`),
  KEY `idx_last_updated` (`last_updated`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='表单规则缓存版本表';

-- 4. 插入默认的客户登记表单规则配置
INSERT INTO `form_rule_config` (
  `id`, 
  `form_code`, 
  `form_name`, 
  `description`,
  `version`, 
  `status`, 
  `field_rules`, 
  `dependency_rules`,
  `create_by`
) VALUES (
  'customer_reg_form_001',
  'customer_reg_form',
  '客户登记表单',
  '体检客户登记表单的验证规则和字段联动配置',
  1,
  1,
  JSON_ARRAY(
    JSON_OBJECT(
      'fieldCode', 'examCategory',
      'fieldName', '体检分类',
      'isRequired', true,
      'requiredMessage', '请选择体检分类',
      'visible', true,
      'disabled', false,
      'sortOrder', 1
    ),
    JSON_OBJECT(
      'fieldCode', 'appointmentDate',
      'fieldName', '预约日期',
      'isRequired', true,
      'requiredMessage', '请选择预约日期',
      'visible', true,
      'disabled', false,
      'sortOrder', 2
    ),
    JSON_OBJECT(
      'fieldCode', 'name',
      'fieldName', '姓名',
      'isRequired', true,
      'requiredMessage', '请输入姓名',
      'visible', true,
      'disabled', false,
      'sortOrder', 3
    ),
    JSON_OBJECT(
      'fieldCode', 'cardType',
      'fieldName', '证件类型',
      'isRequired', true,
      'requiredMessage', '请选择证件类型',
      'visible', true,
      'disabled', false,
      'sortOrder', 4
    ),
    JSON_OBJECT(
      'fieldCode', 'idCard',
      'fieldName', '证件号',
      'isRequired', true,
      'requiredMessage', '请输入证件号',
      'visible', true,
      'disabled', false,
      'sortOrder', 5,
      'validationRules', JSON_ARRAY(
        JSON_OBJECT(
          'type', 'pattern',
          'value', '^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$',
          'message', '请输入正确的身份证号'
        )
      )
    ),
    JSON_OBJECT(
      'fieldCode', 'gender',
      'fieldName', '性别',
      'isRequired', true,
      'requiredMessage', '请选择性别',
      'visible', true,
      'disabled', false,
      'sortOrder', 6
    ),
    JSON_OBJECT(
      'fieldCode', 'birthday',
      'fieldName', '出生日期',
      'isRequired', true,
      'requiredMessage', '请选择出生日期',
      'visible', true,
      'disabled', false,
      'sortOrder', 7
    ),
    JSON_OBJECT(
      'fieldCode', 'phone',
      'fieldName', '电话',
      'isRequired', true,
      'requiredMessage', '请输入电话',
      'visible', true,
      'disabled', false,
      'sortOrder', 8,
      'validationRules', JSON_ARRAY(
        JSON_OBJECT(
          'type', 'pattern',
          'value', '^(?:\\d{11}|(?:\\d{3,4}-)\\d{7,8})$',
          'message', '电话必须是11位数字或固定电话（如：0471-2537660）'
        )
      )
    ),
    JSON_OBJECT(
      'fieldCode', 'age',
      'fieldName', '年龄',
      'isRequired', false,
      'visible', true,
      'disabled', true,
      'sortOrder', 9
    ),
    JSON_OBJECT(
      'fieldCode', 'marriageStatus',
      'fieldName', '婚姻状况',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 10
    ),
    JSON_OBJECT(
      'fieldCode', 'career',
      'fieldName', '职业',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 11
    ),
    JSON_OBJECT(
      'fieldCode', 'emergencyContact',
      'fieldName', '紧急联系人',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 12
    ),
    JSON_OBJECT(
      'fieldCode', 'emergencyPhone',
      'fieldName', '紧急电话',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 13,
      'validationRules', JSON_ARRAY(
        JSON_OBJECT(
          'type', 'pattern',
          'value', '^\\d{11}$',
          'message', '紧急电话必须是11位数字'
        )
      )
    ),
    JSON_OBJECT(
      'fieldCode', 'pregnancyFlag',
      'fieldName', '是否备孕',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 14
    ),
    JSON_OBJECT(
      'fieldCode', 'companyName',
      'fieldName', '单位名称',
      'isRequired', false,
      'visible', true,
      'disabled', false,
      'sortOrder', 15
    )
  ),
  JSON_ARRAY(
    JSON_OBJECT(
      'id', 'dep_exam_category_phone',
      'sourceField', 'examCategory',
      'targetField', 'phone',
      'dependencyType', 'required',
      'conditionType', 'not_equals',
      'conditionValue', '干部体检',
      'actionValue', true,
      'priority', 10
    ),
    JSON_OBJECT(
      'id', 'dep_exam_category_idcard',
      'sourceField', 'examCategory',
      'targetField', 'idCard',
      'dependencyType', 'required',
      'conditionType', 'not_equals',
      'conditionValue', '干部体检',
      'actionValue', true,
      'priority', 10
    ),
    JSON_OBJECT(
      'id', 'dep_gender_pregnancy',
      'sourceField', 'gender',
      'targetField', 'pregnancyFlag',
      'dependencyType', 'visible',
      'conditionType', 'equals',
      'conditionValue', '女',
      'actionValue', true,
      'priority', 5
    )
  ),
  'system'
);

-- 5. 插入缓存版本记录
INSERT INTO `form_rule_cache_version` (
  `id`,
  `form_code`,
  `version`,
  `cache_key`,
  `last_updated`
) VALUES (
  'cache_customer_reg_form_001',
  'customer_reg_form',
  1,
  'form_rule:customer_reg_form',
  UNIX_TIMESTAMP() * 1000
);

-- 6. 创建索引优化查询性能
CREATE INDEX `idx_form_rule_config_composite` ON `form_rule_config` (`form_code`, `status`, `version`);
CREATE INDEX `idx_form_rule_change_log_composite` ON `form_rule_change_log` (`form_code`, `create_time`);

-- 7. 创建视图简化查询
CREATE VIEW `v_form_rule_summary` AS
SELECT 
  frc.`form_code`,
  frc.`form_name`,
  frc.`version`,
  frc.`status`,
  JSON_LENGTH(frc.`field_rules`) as `field_count`,
  JSON_LENGTH(frc.`dependency_rules`) as `dependency_count`,
  frc.`update_time`,
  frcv.`last_updated`
FROM `form_rule_config` frc
LEFT JOIN `form_rule_cache_version` frcv ON frc.`form_code` = frcv.`form_code`
WHERE frc.`del_flag` = 0;
