-- 电子票据管理相关数据表结构
-- 基于《医疗电子票据管理平台-接口规范vsaas2.0--内蒙古自治区人民医院20250630》

-- 1. 电子票据主表
CREATE TABLE `electronic_receipt` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `bill_id` varchar(32) NOT NULL COMMENT '关联支付单ID',
  `bill_no` varchar(50) DEFAULT NULL COMMENT '支付单号',
  `receipt_no` varchar(100) DEFAULT NULL COMMENT '电子票据号',
  `vsaas_apply_no` varchar(100) DEFAULT NULL COMMENT 'vsaas申请单号',
  `org_code` varchar(50) NOT NULL COMMENT '机构编码',
  `receipt_type` int(2) NOT NULL COMMENT '票据类型：1-门诊收费票据，2-住院收费票据',
  `business_type` int(2) NOT NULL COMMENT '业务类型：1-收费，2-退费',
  `patient_name` varchar(50) NOT NULL COMMENT '患者姓名',
  `patient_id_card` varchar(20) DEFAULT NULL COMMENT '患者身份证号',
  `patient_phone` varchar(20) DEFAULT NULL COMMENT '患者手机号',
  `total_amount` decimal(10,2) NOT NULL COMMENT '票据总金额',
  `refunded_amount` decimal(10,2) DEFAULT '0.00' COMMENT '已冲红金额',
  `charge_date` datetime NOT NULL COMMENT '收费日期',
  `charge_user_code` varchar(50) DEFAULT NULL COMMENT '收费员工编码',
  `charge_user_name` varchar(50) DEFAULT NULL COMMENT '收费员工姓名',
  `dept_code` varchar(50) DEFAULT NULL COMMENT '科室编码',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '科室名称',
  `receipt_status` int(2) DEFAULT '1' COMMENT '票据状态：1-申请中，2-已开具，3-已作废，4-已冲红',
  `issue_time` datetime DEFAULT NULL COMMENT '开票时间',
  `void_time` datetime DEFAULT NULL COMMENT '作废时间',
  `pdf_url` varchar(500) DEFAULT NULL COMMENT '票据PDF文件下载地址',
  `image_url` varchar(500) DEFAULT NULL COMMENT '票据图片下载地址',
  `qr_code` varchar(500) DEFAULT NULL COMMENT '票据二维码',
  `expire_time` datetime DEFAULT NULL COMMENT '票据有效期',
  `team_flag` int(1) DEFAULT '0' COMMENT '团检标志：1-团检，0-个检',
  `pre_issue_flag` int(1) DEFAULT '0' COMMENT '预开票标志：1-预开票，0-正常开票',
  `company_name` varchar(200) DEFAULT NULL COMMENT '团检单位名称',
  `company_code` varchar(50) DEFAULT NULL COMMENT '团检单位编码',
  `sync_status` int(2) DEFAULT '0' COMMENT '同步状态：0-未同步，1-已同步，2-同步失败',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误消息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `ext1` varchar(200) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(200) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(200) DEFAULT NULL COMMENT '扩展字段3',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_id` (`bill_id`),
  KEY `idx_receipt_no` (`receipt_no`),
  KEY `idx_vsaas_apply_no` (`vsaas_apply_no`),
  KEY `idx_patient_id_card` (`patient_id_card`),
  KEY `idx_charge_date` (`charge_date`),
  KEY `idx_receipt_status` (`receipt_status`),
  KEY `idx_team_flag` (`team_flag`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据主表';

-- 2. 电子票据明细表
CREATE TABLE `electronic_receipt_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `receipt_id` varchar(32) NOT NULL COMMENT '电子票据ID',
  `receipt_no` varchar(100) DEFAULT NULL COMMENT '电子票据号',
  `pay_record_id` varchar(32) DEFAULT NULL COMMENT '支付记录ID',
  `item_group_id` varchar(32) DEFAULT NULL COMMENT '项目组合ID',
  `item_code` varchar(50) NOT NULL COMMENT '项目编码',
  `item_name` varchar(200) NOT NULL COMMENT '项目名称',
  `specification` varchar(100) DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `self_pay_amount` decimal(10,2) DEFAULT '0.00' COMMENT '自费金额',
  `exec_dept_code` varchar(50) DEFAULT NULL COMMENT '执行科室编码',
  `exec_dept_name` varchar(100) DEFAULT NULL COMMENT '执行科室名称',
  `doctor_code` varchar(50) DEFAULT NULL COMMENT '医生编码',
  `doctor_name` varchar(50) DEFAULT NULL COMMENT '医生姓名',
  `category_code` varchar(50) DEFAULT NULL COMMENT '项目分类编码',
  `category_name` varchar(100) DEFAULT NULL COMMENT '项目分类名称',
  `refunded_quantity` decimal(10,2) DEFAULT '0.00' COMMENT '已冲红数量',
  `refunded_amount` decimal(10,2) DEFAULT '0.00' COMMENT '已冲红金额',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_receipt_no` (`receipt_no`),
  KEY `idx_pay_record_id` (`pay_record_id`),
  KEY `idx_item_group_id` (`item_group_id`),
  KEY `idx_item_code` (`item_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据明细表';

-- 3. 电子票据冲红记录表
CREATE TABLE `electronic_receipt_refund` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `original_receipt_id` varchar(32) NOT NULL COMMENT '原电子票据ID',
  `original_receipt_no` varchar(100) NOT NULL COMMENT '原电子票据号',
  `original_vsaas_apply_no` varchar(100) DEFAULT NULL COMMENT '原vsaas申请单号',
  `refund_receipt_no` varchar(100) DEFAULT NULL COMMENT '冲红票据号',
  `vsaas_refund_apply_no` varchar(100) DEFAULT NULL COMMENT 'vsaas冲红申请单号',
  `refund_bill_no` varchar(50) NOT NULL COMMENT '退费单号',
  `refund_bill_id` varchar(32) NOT NULL COMMENT '退费单ID',
  `refund_type` int(2) NOT NULL COMMENT '冲红类型：1-全额冲红，2-部分冲红',
  `refund_amount` decimal(10,2) NOT NULL COMMENT '冲红金额',
  `refund_date` datetime NOT NULL COMMENT '退费日期',
  `refund_user_code` varchar(50) DEFAULT NULL COMMENT '退费员工编码',
  `refund_user_name` varchar(50) DEFAULT NULL COMMENT '退费员工姓名',
  `refund_reason` varchar(500) NOT NULL COMMENT '退费原因',
  `refund_status` int(2) DEFAULT '1' COMMENT '冲红状态：1-申请中，2-已冲红，3-冲红失败',
  `refund_time` datetime DEFAULT NULL COMMENT '冲红时间',
  `refund_pdf_url` varchar(500) DEFAULT NULL COMMENT '冲红票据PDF文件下载地址',
  `refund_image_url` varchar(500) DEFAULT NULL COMMENT '冲红票据图片下载地址',
  `sync_status` int(2) DEFAULT '0' COMMENT '同步状态：0-未同步，1-已同步，2-同步失败',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误消息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `ext1` varchar(200) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(200) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(200) DEFAULT NULL COMMENT '扩展字段3',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_original_receipt_id` (`original_receipt_id`),
  KEY `idx_original_receipt_no` (`original_receipt_no`),
  KEY `idx_refund_bill_id` (`refund_bill_id`),
  KEY `idx_refund_date` (`refund_date`),
  KEY `idx_refund_status` (`refund_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据冲红记录表';

-- 4. 电子票据审计日志表
CREATE TABLE `electronic_receipt_audit_log` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `receipt_id` varchar(32) DEFAULT NULL COMMENT '关联票据ID',
  `receipt_no` varchar(100) DEFAULT NULL COMMENT '电子票据号',
  `audit_type` varchar(30) NOT NULL COMMENT '审计类型：CREATE/UPDATE/DELETE/APPLY/REFUND/DOWNLOAD/SYNC/VOID',
  `operation_desc` varchar(200) DEFAULT NULL COMMENT '操作描述',
  `risk_level` varchar(10) DEFAULT 'LOW' COMMENT '风险级别：LOW/MEDIUM/HIGH/CRITICAL',
  `compliance_flag` varchar(1) DEFAULT '1' COMMENT '合规标志：1-合规，0-不合规',
  `compliance_result` varchar(500) DEFAULT NULL COMMENT '合规检查结果',
  `before_data` text COMMENT '操作前数据(JSON格式)',
  `after_data` text COMMENT '操作后数据(JSON格式)',
  `change_summary` varchar(500) DEFAULT NULL COMMENT '数据变更摘要',
  `involved_amount` decimal(15,2) DEFAULT NULL COMMENT '涉及金额',
  `operator_id` varchar(32) DEFAULT NULL COMMENT '操作用户ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作用户姓名',
  `operator_role` varchar(50) DEFAULT NULL COMMENT '操作用户角色',
  `operator_ip` varchar(50) DEFAULT NULL COMMENT '操作IP地址',
  `operation_time` datetime NOT NULL COMMENT '操作时间',
  `business_module` varchar(50) DEFAULT NULL COMMENT '业务模块',
  `business_scene` varchar(30) DEFAULT NULL COMMENT '业务场景：individual/team/batch',
  `interface_call_id` varchar(50) DEFAULT NULL COMMENT '接口调用ID',
  `request_params` text COMMENT '请求参数(JSON格式)',
  `response_result` text COMMENT '响应结果(JSON格式)',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行耗时(毫秒)',
  `operation_result` varchar(20) DEFAULT NULL COMMENT '操作结果：SUCCESS/FAILURE/PARTIAL',
  `error_code` varchar(50) DEFAULT NULL COMMENT '错误代码',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误消息',
  `exception_stack` text COMMENT '异常堆栈信息',
  `related_business_id` varchar(32) DEFAULT NULL COMMENT '关联业务ID(支付单ID/退费单ID等)',
  `related_business_type` varchar(30) DEFAULT NULL COMMENT '关联业务类型',
  `data_sensitivity_level` varchar(20) DEFAULT 'INTERNAL' COMMENT '数据敏感级别：PUBLIC/INTERNAL/CONFIDENTIAL/SECRET',
  `audit_level` varchar(20) DEFAULT 'BUSINESS' COMMENT '审计级别：SYSTEM/BUSINESS/SECURITY',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `ext1` varchar(200) DEFAULT NULL COMMENT '扩展字段1',
  `ext2` varchar(200) DEFAULT NULL COMMENT '扩展字段2',
  `ext3` varchar(200) DEFAULT NULL COMMENT '扩展字段3',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_receipt_no` (`receipt_no`),
  KEY `idx_audit_type` (`audit_type`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_compliance_flag` (`compliance_flag`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_business_scene` (`business_scene`),
  KEY `idx_operation_result` (`operation_result`),
  KEY `idx_related_business_id` (`related_business_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据审计日志表';

-- 5. 电子票据合规性检查表
CREATE TABLE `electronic_receipt_compliance_check` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `receipt_id` varchar(32) DEFAULT NULL COMMENT '关联票据ID',
  `receipt_no` varchar(100) DEFAULT NULL COMMENT '电子票据号',
  `check_type` varchar(50) NOT NULL COMMENT '检查类型：DATA_INTEGRITY/AMOUNT_VERIFICATION/TIME_VALIDATION/PERMISSION_CHECK',
  `check_rule` varchar(200) NOT NULL COMMENT '检查规则',
  `check_result` varchar(20) NOT NULL COMMENT '检查结果：PASS/FAIL/WARNING',
  `check_score` int(3) DEFAULT NULL COMMENT '检查评分(0-100)',
  `risk_level` varchar(10) DEFAULT 'LOW' COMMENT '风险级别：LOW/MEDIUM/HIGH/CRITICAL',
  `check_detail` text COMMENT '检查详情(JSON格式)',
  `violation_description` varchar(1000) DEFAULT NULL COMMENT '违规描述',
  `suggestion` varchar(1000) DEFAULT NULL COMMENT '改进建议',
  `auto_fix_flag` varchar(1) DEFAULT '0' COMMENT '自动修复标志：1-可自动修复，0-需人工处理',
  `fix_action` varchar(500) DEFAULT NULL COMMENT '修复操作',
  `check_time` datetime NOT NULL COMMENT '检查时间',
  `checker_type` varchar(20) DEFAULT 'SYSTEM' COMMENT '检查者类型：SYSTEM/MANUAL',
  `checker_id` varchar(32) DEFAULT NULL COMMENT '检查者ID',
  `checker_name` varchar(50) DEFAULT NULL COMMENT '检查者姓名',
  `related_audit_log_id` varchar(32) DEFAULT NULL COMMENT '关联审计日志ID',
  `follow_up_required` varchar(1) DEFAULT '0' COMMENT '需要跟进：1-需要，0-不需要',
  `follow_up_status` varchar(20) DEFAULT NULL COMMENT '跟进状态：PENDING/IN_PROGRESS/COMPLETED',
  `follow_up_deadline` datetime DEFAULT NULL COMMENT '跟进截止时间',
  `resolution_time` datetime DEFAULT NULL COMMENT '解决时间',
  `resolution_description` varchar(1000) DEFAULT NULL COMMENT '解决描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_receipt_id` (`receipt_id`),
  KEY `idx_receipt_no` (`receipt_no`),
  KEY `idx_check_type` (`check_type`),
  KEY `idx_check_result` (`check_result`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_check_time` (`check_time`),
  KEY `idx_follow_up_required` (`follow_up_required`),
  KEY `idx_follow_up_status` (`follow_up_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据合规性检查表';

-- 创建索引优化查询性能
-- 电子票据主表复合索引
CREATE INDEX `idx_receipt_status_team_flag` ON `electronic_receipt` (`receipt_status`, `team_flag`);
CREATE INDEX `idx_charge_date_receipt_status` ON `electronic_receipt` (`charge_date`, `receipt_status`);
CREATE INDEX `idx_company_code_pre_issue_flag` ON `electronic_receipt` (`company_code`, `pre_issue_flag`);

-- 审计日志表复合索引
CREATE INDEX `idx_operation_time_audit_type` ON `electronic_receipt_audit_log` (`operation_time`, `audit_type`);
CREATE INDEX `idx_risk_level_compliance_flag` ON `electronic_receipt_audit_log` (`risk_level`, `compliance_flag`);
CREATE INDEX `idx_business_scene_operation_result` ON `electronic_receipt_audit_log` (`business_scene`, `operation_result`);