-- 为customer_reg_bill表添加电子票据相关字段
-- 用于支持电子票据功能的集成

ALTER TABLE `customer_reg_bill` 
ADD COLUMN `electronic_receipt_status` int(2) DEFAULT '0' COMMENT '电子票据申请状态：0-未申请，1-申请中，2-已开具，3-申请失败' AFTER `haveRefundFlag`,
ADD COLUMN `electronic_receipt_no` varchar(100) DEFAULT NULL COMMENT '电子票据号' AFTER `electronic_receipt_status`,
ADD COLUMN `vsaas_apply_no` varchar(100) DEFAULT NULL COMMENT 'vsaas申请单号' AFTER `electronic_receipt_no`,
ADD COLUMN `electronic_receipt_apply_time` datetime DEFAULT NULL COMMENT '电子票据申请时间' AFTER `vsaas_apply_no`,
ADD COLUMN `electronic_receipt_issue_time` datetime DEFAULT NULL COMMENT '电子票据开具时间' AFTER `electronic_receipt_apply_time`,
ADD COLUMN `electronic_receipt_pdf_url` varchar(500) DEFAULT NULL COMMENT '电子票据PDF下载地址' AFTER `electronic_receipt_issue_time`,
ADD COLUMN `electronic_receipt_image_url` varchar(500) DEFAULT NULL COMMENT '电子票据图片下载地址' AFTER `electronic_receipt_pdf_url`,
ADD COLUMN `electronic_receipt_qr_code` varchar(500) DEFAULT NULL COMMENT '电子票据二维码' AFTER `electronic_receipt_image_url`,
ADD COLUMN `electronic_receipt_error` varchar(1000) DEFAULT NULL COMMENT '电子票据错误消息' AFTER `electronic_receipt_qr_code`,
ADD COLUMN `need_electronic_receipt` int(1) DEFAULT '1' COMMENT '是否需要电子票据：1-需要，0-不需要' AFTER `electronic_receipt_error`,
ADD COLUMN `electronic_receipt_sync_status` int(2) DEFAULT '0' COMMENT '电子票据同步状态：0-未同步，1-已同步，2-同步失败' AFTER `need_electronic_receipt`,
ADD COLUMN `electronic_receipt_last_sync_time` datetime DEFAULT NULL COMMENT '电子票据最后同步时间' AFTER `electronic_receipt_sync_status`;

-- 添加索引以优化查询性能
CREATE INDEX `idx_electronic_receipt_status` ON `customer_reg_bill` (`electronic_receipt_status`);
CREATE INDEX `idx_electronic_receipt_no` ON `customer_reg_bill` (`electronic_receipt_no`);
CREATE INDEX `idx_vsaas_apply_no` ON `customer_reg_bill` (`vsaas_apply_no`);
CREATE INDEX `idx_need_electronic_receipt` ON `customer_reg_bill` (`need_electronic_receipt`);
CREATE INDEX `idx_electronic_receipt_sync_status` ON `customer_reg_bill` (`electronic_receipt_sync_status`);

-- 添加复合索引
CREATE INDEX `idx_receipt_status_sync_status` ON `customer_reg_bill` (`electronic_receipt_status`, `electronic_receipt_sync_status`);
CREATE INDEX `idx_need_receipt_status` ON `customer_reg_bill` (`need_electronic_receipt`, `electronic_receipt_status`);