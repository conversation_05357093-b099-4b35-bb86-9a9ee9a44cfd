-- 电子票据管理相关数据表结构（矫正版）
-- 基于博思电子票据管理平台的实际接口规范

-- 1. 电子票据主表
CREATE TABLE `electronic_bill` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `bill_id` varchar(32) NOT NULL COMMENT '关联支付单ID',
  `bill_no` varchar(50) DEFAULT NULL COMMENT '支付单号',
  `bus_no` varchar(50) NOT NULL COMMENT '业务流水号',
  `bill_batch_code` varchar(50) DEFAULT NULL COMMENT '电子票据代码',
  `electronic_bill_no` varchar(20) DEFAULT NULL COMMENT '电子票据号码',
  `random_code` varchar(20) DEFAULT NULL COMMENT '电子校验码',
  `bus_type` varchar(20) NOT NULL COMMENT '业务标识：01-住院,02-门诊,03-挂号,04-体检',
  `payer` varchar(100) NOT NULL COMMENT '患者姓名',
  `bus_date_time` varchar(17) NOT NULL COMMENT '业务发生时间：yyyyMMddHHmmssSSS',
  `place_code` varchar(50) NOT NULL COMMENT '开票点编码',
  `payee` varchar(50) NOT NULL COMMENT '收费员',
  `author` varchar(100) NOT NULL COMMENT '票据编制人',
  `checker` varchar(100) DEFAULT NULL COMMENT '票据复核人',
  `total_amt` decimal(14,2) NOT NULL COMMENT '开票总金额',
  `bill_status` varchar(1) DEFAULT '1' COMMENT '票据状态：1-正常，2-作废',
  `is_scarlet` varchar(1) DEFAULT '0' COMMENT '是否已开红票：0-未开红票，1-已开红票',
  `ivc_date_time` varchar(17) DEFAULT NULL COMMENT '开票时间：yyyyMMddHHmmssSSS',
  `bill_qr_code` longtext DEFAULT NULL COMMENT '电子票据二维码图片数据(BASE64)',
  `picture_url` varchar(500) DEFAULT NULL COMMENT '电子票据H5页面URL',
  `picture_net_url` varchar(500) DEFAULT NULL COMMENT '电子票据外网H5页面URL',
  `team_flag` int(1) DEFAULT '0' COMMENT '团检标志：1-团检，0-个检',
  `pre_issue_flag` int(1) DEFAULT '0' COMMENT '预开票标志：1-预开票，0-正常开票',
  `company_name` varchar(200) DEFAULT NULL COMMENT '团检单位名称',
  `card_no` varchar(20) DEFAULT NULL COMMENT '患者身份证号',
  `tel` varchar(11) DEFAULT NULL COMMENT '患者手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `dept_code` varchar(50) DEFAULT NULL COMMENT '科室代码',
  `dept_name` varchar(100) DEFAULT NULL COMMENT '科室名称',
  `doctor_code` varchar(50) DEFAULT NULL COMMENT '医生代码',
  `doctor_name` varchar(50) DEFAULT NULL COMMENT '医生姓名',
  `is_arrears` varchar(1) DEFAULT NULL COMMENT '是否可流通',
  `arrears_reason` varchar(200) DEFAULT NULL COMMENT '不可流通原因',
  `charge_date` varchar(17) DEFAULT NULL COMMENT '交费日期',
  `in_hospital_no` varchar(50) DEFAULT NULL COMMENT '住院号',
  `in_hospital_date` varchar(17) DEFAULT NULL COMMENT '入院日期',
  `out_hospital_date` varchar(17) DEFAULT NULL COMMENT '出院日期',
  `in_hospital_days` int(5) DEFAULT NULL COMMENT '住院天数',
  `ward_code` varchar(50) DEFAULT NULL COMMENT '病区',
  `bed_no` varchar(20) DEFAULT NULL COMMENT '床位号',
  `sync_status` int(2) DEFAULT '0' COMMENT '同步状态：0-未同步，1-已同步，2-同步失败',
  `last_sync_time` datetime DEFAULT NULL COMMENT '最后同步时间',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误消息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_bill_id` (`bill_id`),
  KEY `idx_bus_no` (`bus_no`),
  KEY `idx_electronic_bill_no` (`electronic_bill_no`),
  KEY `idx_bill_batch_code` (`bill_batch_code`),
  KEY `idx_payer` (`payer`),
  KEY `idx_bus_date_time` (`bus_date_time`),
  KEY `idx_bill_status` (`bill_status`),
  KEY `idx_team_flag` (`team_flag`),
  KEY `idx_sync_status` (`sync_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据主表';

-- 2. 电子票据明细表
CREATE TABLE `electronic_bill_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `bill_id` varchar(32) NOT NULL COMMENT '电子票据ID',
  `pay_record_id` varchar(32) DEFAULT NULL COMMENT '支付记录ID',
  `item_group_id` varchar(32) DEFAULT NULL COMMENT '项目组合ID',
  `code` varchar(50) NOT NULL COMMENT '费用类别编码',
  `name` varchar(200) NOT NULL COMMENT '费用类别名称',
  `standard` varchar(100) DEFAULT NULL COMMENT '规格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `price` decimal(10,4) NOT NULL COMMENT '单价',
  `amt` decimal(14,2) NOT NULL COMMENT '金额',
  `med_care_institution` varchar(50) DEFAULT NULL COMMENT '医保报销类型',
  `balanced_number` varchar(50) DEFAULT NULL COMMENT '费款所属期',
  `exec_dept_name` varchar(100) DEFAULT NULL COMMENT '执行科室',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_pay_record_id` (`pay_record_id`),
  KEY `idx_item_group_id` (`item_group_id`),
  KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据明细表';

-- 3. 电子票据冲红记录表
CREATE TABLE `electronic_bill_writeoff` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `original_bill_id` varchar(32) NOT NULL COMMENT '原电子票据ID',
  `original_bill_batch_code` varchar(50) NOT NULL COMMENT '原电子票据代码',
  `original_bill_no` varchar(20) NOT NULL COMMENT '原电子票据号码',
  `scarlet_bill_batch_code` varchar(50) DEFAULT NULL COMMENT '电子红票代码',
  `scarlet_bill_no` varchar(20) DEFAULT NULL COMMENT '电子红票号码',
  `scarlet_random` varchar(20) DEFAULT NULL COMMENT '电子红票校验码',
  `reason` varchar(200) NOT NULL COMMENT '冲红原因',
  `operator` varchar(60) NOT NULL COMMENT '经办人',
  `bus_date_time` varchar(17) NOT NULL COMMENT '业务发生时间：yyyyMMddHHmmssSSS',
  `place_code` varchar(50) NOT NULL COMMENT '开票点编码',
  `create_time` varchar(17) DEFAULT NULL COMMENT '电子红票生成时间：yyyyMMddHHmmssSSS',
  `bill_qr_code` longtext DEFAULT NULL COMMENT '电子红票二维码图片数据(BASE64)',
  `picture_url` varchar(500) DEFAULT NULL COMMENT '电子红票H5页面URL',
  `picture_net_url` varchar(500) DEFAULT NULL COMMENT '电子红票外网H5页面URL',
  `status` varchar(1) DEFAULT '1' COMMENT '冲红状态：1-成功，0-失败',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误消息',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_original_bill_id` (`original_bill_id`),
  KEY `idx_original_bill_no` (`original_bill_no`),
  KEY `idx_scarlet_bill_no` (`scarlet_bill_no`),
  KEY `idx_operator` (`operator`),
  KEY `idx_bus_date_time` (`bus_date_time`),
  KEY `idx_create_date` (`create_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电子票据冲红记录表';

-- 4. 为customer_reg_bill表添加电子票据相关字段（如果还未添加）
ALTER TABLE `customer_reg_bill` 
ADD COLUMN `electronic_bill_batch_code` varchar(50) DEFAULT NULL COMMENT '电子票据代码' AFTER `haveRefundFlag`,
ADD COLUMN `electronic_bill_no` varchar(20) DEFAULT NULL COMMENT '电子票据号码' AFTER `electronic_bill_batch_code`,
ADD COLUMN `electronic_bill_random` varchar(20) DEFAULT NULL COMMENT '电子校验码' AFTER `electronic_bill_no`,
ADD COLUMN `electronic_bill_status` varchar(1) DEFAULT '0' COMMENT '电子票据状态：0-未申请，1-申请中，2-已开具，3-申请失败' AFTER `electronic_bill_random`,
ADD COLUMN `electronic_bill_qr_code` longtext DEFAULT NULL COMMENT '电子票据二维码' AFTER `electronic_bill_status`,
ADD COLUMN `electronic_bill_picture_url` varchar(500) DEFAULT NULL COMMENT '电子票据H5页面URL' AFTER `electronic_bill_qr_code`,
ADD COLUMN `electronic_bill_ivc_time` datetime DEFAULT NULL COMMENT '开票时间' AFTER `electronic_bill_picture_url`,
ADD COLUMN `electronic_bill_error` varchar(1000) DEFAULT NULL COMMENT '开票错误信息' AFTER `electronic_bill_ivc_time`;

-- 添加索引
CREATE INDEX `idx_electronic_bill_status` ON `customer_reg_bill` (`electronic_bill_status`);
CREATE INDEX `idx_electronic_bill_no` ON `customer_reg_bill` (`electronic_bill_no`);
CREATE INDEX `idx_electronic_bill_batch_code` ON `customer_reg_bill` (`electronic_bill_batch_code`);

-- 创建复合索引优化查询性能
CREATE INDEX `idx_bill_status_team_flag` ON `electronic_bill` (`bill_status`, `team_flag`);
CREATE INDEX `idx_bus_date_time_bill_status` ON `electronic_bill` (`bus_date_time`, `bill_status`);
CREATE INDEX `idx_company_name_pre_issue_flag` ON `electronic_bill` (`company_name`, `pre_issue_flag`);
CREATE INDEX `idx_sync_status_last_sync_time` ON `electronic_bill` (`sync_status`, `last_sync_time`);

-- 插入测试数据（可选）
-- INSERT INTO `electronic_bill` (`id`, `bill_id`, `bill_no`, `bus_no`, `bus_type`, `payer`, `bus_date_time`, `place_code`, `payee`, `author`, `total_amt`) 
-- VALUES ('test001', 'bill001', 'B001', 'OUT20250124001', '02', '测试患者', '20250124101530001', 'PLACE_001', '收费员1', '收费员1', 100.00);