# 表单规则管理模块配置 - 简化版
# 复用现有项目的数据库、Redis等配置，无需重复配置

# 表单规则模块特定配置
form-rule:
  # 缓存配置 - 简化为长期缓存
  cache:
    # 缓存过期时间（天）
    expire-days: 7
    # 版本缓存过期时间（天）
    version-expire-days: 1
    # 预热的表单代码列表
    warmup-form-codes:
      - customer_reg_form

  # 定时任务配置 - 简化版
  scheduled:
    # 是否启用定时任务
    enabled: true
    # 日志清理cron表达式（每周执行一次）
    log-cleanup-cron: "0 2 0 * * 0"
    # 统计生成cron表达式（每天凌晨0:30执行）
    statistics-cron: "0 30 0 * * ?"

# 日志配置 - 补充表单规则模块的日志级别
logging:
  level:
    # 表单规则模块日志
    org.jeecg.modules.formrule: DEBUG
